import axios from 'axios';
import { TOKEN, SUUID } from '../constants';
import { API_URL } from '../config/config';

export const setSuuid = suuid => {
  sessionStorage.setItem(SUUID, suuid);
};

export const getSuuid = () => sessionStorage.getItem(SUUID);

export const removeSuuid = () => {
  sessionStorage.removeItem(SUUID);
};

export const setToken = data => {
  localStorage.setItem(TOKEN, data.token);
};

export const getToken = () => localStorage.getItem(TOKEN);

export const removeToken = () => {
  localStorage.removeItem(TOKEN);
};

export const getAuthHeader = () => {
  return { Authorization: `Bearer ${getToken()}` };
};

export const getSuuidHeader = () => {
  let suuid = getSuuid();

  return suuid == null ? {} : { suuid: suuid };
};

export const isAuthenticated = () => !!getToken();

export const refreshToken = () => {
  return new Promise((resolve, reject) => {
    axios
      .post(`${API_URL}/auth/refresh`)
      .then(response => {
        const {
          data: { data },
        } = response;
        setToken(data);
        return resolve(data);
      })
      .catch(reason => {
        removeToken();
        reject(reason);

        // TODO: purge state and navigate to login screen
        window.location.reload();
      });
  });
};

let refreshPromise = null;

axios.interceptors.request.use(
  config => {
    const token = getToken();
    if (token) {
      Object.assign(config.headers, getAuthHeader());
    }

    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

axios.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    if (error.config && error.response && error.response.status === 401) {
      refreshPromise = refreshPromise || refreshToken();

      return refreshPromise
        .then(() => {
          refreshPromise = null;
          Object.assign(error.config.headers, getAuthHeader());
          return axios.request(error.config);
        })
        .catch(reason => {
          refreshPromise = null;
          return Promise.reject(reason);
        });
    }
    return Promise.reject(error);
  }
);
