import humps from 'humps';

export const composeFormData = payload => {
  const formData = new FormData();

  for (const [key, value] of Object.entries(payload)) {
    // since we ignore decamelizeKeys in the axios, we decamelize it here (only keys)
    const decKey = humps.decamelize(key);

    // checking if some values are arrays, than composing formData as array for that key
    if (Array.isArray(value) && value.length) {
      for (let i = 0; i < value.length; i++) {
        formData.append(`${decKey}[]`, value[i]);
      }

      continue;
    }

    // formData converts all null values to "null" string,
    // therefore, we should change it to an empty string
    formData.append(decKey, value || '');
  }

  return formData;
};
