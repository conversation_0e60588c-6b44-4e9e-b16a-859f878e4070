import crypto from 'crypto';
import { isAndroid, isBrowser, isIPad13 } from 'react-device-detect';
import {
  LOAN_TYPE,
  LOAN_TYPES,
  FINGERPRINT,
  PREDEFINED_REAL_ESTATE,
  IS_LUYSER,
  IS_BANALI,
  IS_PAY4ME,
  OBL_PAY4ME,
} from '../constants';

export const showInfoMessage = () => {
  return process.env.REACT_APP_SHOW_INFO_MESSAGE === 'true';
};

export const showInfoModal = () => {
  return process.env.REACT_APP_SHOW_INFO_MODAL === 'true';
};

export const isDev = () => {
  return process.env.NODE_ENV === 'development';
};

export const isProd = () => {
  return process.env.REACT_APP_CUSTOM_ENV === 'production';
};

export const setLoanType = loanType => {
  sessionStorage.setItem(LOAN_TYPE, loanType);
};

export const getLoanType = () => {
  return +sessionStorage.getItem(LOAN_TYPE);
};

export const removeLoanType = () => {
  sessionStorage.removeItem(LOAN_TYPE);
};

export const isOIQL = () => {
  return getLoanType() === LOAN_TYPES.OIQL.id;
};

export const isOBL = () => {
  return getLoanType() === LOAN_TYPES.OBL.id;
};

export const isBNPL = () => {
  return getLoanType() === LOAN_TYPES.BNPL.id;
};

export const setFingerprintToken = token => {
  localStorage.setItem(FINGERPRINT, token);
};

export const getFingerprintToken = () => {
  return localStorage.getItem(FINGERPRINT);
};

export const isOASL = () => {
  return getLoanType() === LOAN_TYPES.OASL.id;
};

export const getLoanName = id => {
  const loan = Object.values(LOAN_TYPES).filter(loanType => loanType.id === id);
  return loan.length ? loan[0].name.toUpperCase() : '';
};

export const createHMAC = data => {
  return crypto
    .createHmac('md5', process.env.REACT_APP_DOCUMENT_NUMBER_SECRET)
    .update(data)
    .digest('hex');
};

export const getQrToken = () => {
  const location = window.location;
  const path = location.pathname;
  const searchParams = location.search;

  if (path.includes('/qr/')) {
    return path.slice(-8).toLowerCase();
  } else if (searchParams.includes('?token=')) {
    //?token= we are using on the old qr codes
    return searchParams.slice(7, 15).toLowerCase();
  }
};

// Real Estate Token
export const getReToken = () => {
  const path = window.location.href;

  if (path.includes('?re_token=')) {
    return path.slice(-PREDEFINED_REAL_ESTATE.TOKEN_LENGTH).toLowerCase();
  }
};

export const isLuyserInStorage = () => {
  return JSON.parse(sessionStorage.getItem(IS_LUYSER));
};

export const isBanaliInStorage = () => {
  return JSON.parse(sessionStorage.getItem(IS_BANALI));
};

export const isPay4meInStorage = () => {
  return JSON.parse(sessionStorage.getItem(IS_PAY4ME));
};

export const setSessionStorageItem = (key, value) => {
  sessionStorage.setItem(key, JSON.stringify(value));
};

export const isBanali = () => {
  const path = window.location.search;

  return (
    document.referrer.indexOf('https://banali.am/') >= 0 ||
    path.includes('?source=banali')
  );
};

export const isSourcePay4me = () => {
  const path = window.location.search;

  return path.includes('?source=pay4me');
};

export const getPay4meToken = () => {
  const path = window.location.href;

  if (path.includes('&token=')) {
    return path.slice(-OBL_PAY4ME.TOKEN_LENGTH);
  }
};

export const issetReferralCode = () => {
  const path = window.location.href;

  return path.includes('?h=');
};

export const isOVL = () => {
  return getLoanType() === LOAN_TYPES.OVL.id;
};

export const isREML = (loanTypeId = null) => {
  if (loanTypeId) {
    return loanTypeId === LOAN_TYPES.REML.id;
  }

  return getLoanType() === LOAN_TYPES.REML.id;
};

export const composeQueryParams = (history, attribute, rewrite = false) => {
  const searchParams = history.location.search;
  let queryString = '';
  const newQueryString = '?' + attribute;

  if (rewrite) {
    queryString = newQueryString;
  } else if (searchParams.indexOf(attribute) === -1) {
    queryString =
      searchParams === '' ? newQueryString : searchParams + '&' + attribute;
  }

  return queryString;
};

// These loan types are allowed to use from desktop device browsers too.
export const isBrowserAllowedLoanType = () => {
  return (
    isOIQL() ||
    isOASL() ||
    isBanaliInStorage() ||
    isLuyserInStorage() ||
    isOBL() ||
    isPay4meInStorage() ||
    isBNPL() ||
    isOVL()
  );
};

// These loan types are allowed to use from Web browsers.
export const isWebAllowedLoanType = () => {
  return (
    isOIQL() || isOASL() || isOBL() || isSourcePay4me() || isBNPL() || isOVL()
  );
};

export const isDesktopDeviceBrowser = () => {
  return isBrowser && !isAndroid && !isIPad13;
};
