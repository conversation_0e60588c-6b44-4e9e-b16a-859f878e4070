import moment from 'moment';

import {
  APP_DATE_FORMAT,
  ARM_TIMEZONE,
  OFFICE_TIME_FORMAT,
  WORK_TIME_FORMAT,
} from '../constants';

export const composeOffices = (officesData, serverTime) => {
  return officesData
    ? officesData.map(office => {
        const schedule = getSchedule(office.cashOfficeSchedules);

        return {
          schedule:
            !isWeekend(schedule) &&
            moment(schedule.openTime, OFFICE_TIME_FORMAT).format(
              WORK_TIME_FORMAT
            ) +
              ' - ' +
              moment(schedule.closeTime, OFFICE_TIME_FORMAT).format(
                WORK_TIME_FORMAT
              ),
          item: office,
          isOpen: isOpen(schedule, serverTime),
        };
      })
    : [];
};

export const getSchedule = schedules => {
  return schedules.find(
    schedule =>
      schedule.weekDay ===
      moment()
        .day()
        .toString()
  );
};

export const isOpen = (schedule, serverTime) => {
  let date = prepareDate(schedule, serverTime);

  return (
    !isWeekend(schedule) &&
    moment(date.current).isBetween(moment(date.start), moment(date.end))
  );
};

export const isWeekend = schedule => {
  return !schedule.openTime || !schedule.closeTime;
};

export const prepareDate = (schedule, serverTime) => {
  let current = moment(serverTime)
    .utc(serverTime)
    .utcOffset(ARM_TIMEZONE)
    .format(APP_DATE_FORMAT + ' ' + OFFICE_TIME_FORMAT);

  return {
    current,
    start: moment(current).format(APP_DATE_FORMAT) + ' ' + schedule.openTime,
    end: moment(current).format(APP_DATE_FORMAT) + ' ' + schedule.closeTime,
  };
};
