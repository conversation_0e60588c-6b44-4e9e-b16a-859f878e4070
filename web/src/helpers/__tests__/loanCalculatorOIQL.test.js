import LoanCalculatorOIQL from '../loanCalculatorOIQL';
import _ from 'lodash';
import moment from 'moment';

beforeEach(() => {
  moment.now = () => {
    return +new Date('2020-11-13');
  };
});

it('Calculates IQOS schedule for 15500 product with 1900 monthly payment and 70% rate', () => {
  const c = new LoanCalculatorOIQL(15500, 70, 24, 12);
  const actual = c.generateSchedule(1900);

  const first = _.pick(actual[0], [
    'serviceFeePlain',
    'serviceFeeInterest',
    'base',
    'balance',
    'payment',
  ]);

  expect(first).toEqual({
    serviceFeePlain: 586.03,
    serviceFeeInterest: 305.75,
    base: 1008.22,
    balance: 14491.78,
    payment: 1900.0,
  });

  // Check last item in the schedule
  const last = _.pick(actual[actual.length - 1], [
    'serviceFeePlain',
    'serviceFeeInterest',
    'base',
    'balance',
    'payment',
  ]);

  expect(last).toEqual({
    serviceFeePlain: 26.83,
    serviceFeeInterest: 14.0,
    base: 686.8,
    balance: 0,
    payment: 727.63,
  });
});

it('Calculates IQOS schedule for 39.000 product with 4700 monthly payment and 70% rate', () => {
  const c = new LoanCalculatorOIQL(39000, 70, 24, 12);
  const actual = c.generateSchedule(4700);

  const first = _.pick(actual[0], [
    'serviceFeePlain',
    'serviceFeeInterest',
    'base',
    'balance',
    'payment',
  ]);

  expect(first).toEqual({
    serviceFeePlain: 1474.52,
    serviceFeeInterest: 769.32,
    base: 2456.16,
    balance: 36543.84,
    payment: 4700.0,
  });

  // Check last item in the schedule
  const last = _.pick(actual[actual.length - 1], [
    'serviceFeePlain',
    'serviceFeeInterest',
    'base',
    'balance',
    'payment',
  ]);

  expect(last).toEqual({
    serviceFeePlain: 114.28,
    serviceFeeInterest: 59.62,
    base: 2925.05,
    balance: 0,
    payment: 3098.95,
  });
});
