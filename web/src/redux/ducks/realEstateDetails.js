import { ofType } from 'redux-observable';
import { catchError, mergeMap } from 'rxjs/operators';
import { API_URL } from '../../config/config';
import { getSuuid, getSuuidHeader } from '../../helpers/auth';
import { wizardError, wizardErrorClear } from './wizard';
import axios from 'axios';
import { from, of } from 'rxjs';

export const FETCH_REAL_ESTATE_DETAILS =
  'globalcredit/real_estate_details/FETCH_REAL_ESTATE_DETAILS';
export const FETCH_REAL_ESTATE_DETAILS_SUCCESS =
  'globalcredit/real_estate_details/FETCH_REAL_ESTATE_DETAILS_SUCCESS';
export const FETCH_REAL_ESTATE_DETAILS_ERROR =
  'globalcredit/real_estate_details/FETCH_REAL_ESTATE_DETAILS_ERROR';

export const STORE_REAL_ESTATE_DETAILS =
  'globalcredit/real_estate_details/STORE_REAL_ESTATE_DETAILS';
export const STORE_REAL_ESTATE_DETAILS_SUCCESS =
  'globalcredit/real_estate_details/STORE_REAL_ESTATE_DETAILS_SUCCESS';
export const STORE_REAL_ESTATE_DETAILS_ERROR =
  'globalcredit/real_estate_details/STORE_REAL_ESTATE_DETAILS_ERROR';

export const STORE_REAL_ESTATE_DETAILS_MEDIA =
  'globalcredit/real_estate_details/STORE_REAL_ESTATE_DETAILS_MEDIA';
export const STORE_REAL_ESTATE_DETAILS_MEDIA_SUCCESS =
  'globalcredit/real_estate_details/STORE_REAL_ESTATE_DETAILS_MEDIA_SUCCESS';
export const STORE_REAL_ESTATE_DETAILS_MEDIA_ERROR =
  'globalcredit/real_estate_details/STORE_REAL_ESTATE_DETAILS_MEDIA_ERROR';

export const FETCH_REAL_ESTATE_DETAILS_MEDIA =
  'globalcredit/real_estate_details/FETCH_REAL_ESTATE_DETAILS_MEDIA';
export const FETCH_REAL_ESTATE_DETAILS_MEDIA_SUCCESS =
  'globalcredit/real_estate_details/FETCH_REAL_ESTATE_DETAILS_MEDIA_SUCCESS';
export const FETCH_REAL_ESTATE_DETAILS_MEDIA_ERROR =
  'globalcredit/real_estate_details/FETCH_REAL_ESTATE_DETAILS_MEDIA_ERROR';

export const REMOVE_REAL_ESTATE_DETAILS_MEDIA =
  'globalcredit/real_estate_details/REMOVE_REAL_ESTATE_DETAILS_MEDIA';
export const REMOVE_REAL_ESTATE_DETAILS_MEDIA_SUCCESS =
  'globalcredit/real_estate_details/REMOVE_REAL_ESTATE_DETAILS_MEDIA_SUCCESS';
export const REMOVE_REAL_ESTATE_DETAILS_MEDIA_ERROR =
  'globalcredit/real_estate_details/REMOVE_REAL_ESTATE_DETAILS_MEDIA_ERROR';

export const fetchRealEstateDetails = () => ({
  type: FETCH_REAL_ESTATE_DETAILS,
});

export const fetchRealEstateDetailsFulfilled = payload => ({
  type: FETCH_REAL_ESTATE_DETAILS_SUCCESS,
  payload,
});

export const fetchRealEstateDetailsError = payload => ({
  type: FETCH_REAL_ESTATE_DETAILS_ERROR,
  payload,
});

export const storeRealEstateDetails = payload => ({
  type: STORE_REAL_ESTATE_DETAILS,
  payload,
});

export const storeRealEstateDetailsFulfilled = payload => ({
  type: STORE_REAL_ESTATE_DETAILS_SUCCESS,
  payload,
});

export const storeRealEstateDetailsError = payload => ({
  type: STORE_REAL_ESTATE_DETAILS_ERROR,
  payload,
});

export const storeRealEstateDetailsMedia = payload => ({
  type: STORE_REAL_ESTATE_DETAILS_MEDIA,
  payload,
});

export const storeRealEstateDetailsMediaFulfilled = payload => ({
  type: STORE_REAL_ESTATE_DETAILS_MEDIA_SUCCESS,
  payload,
});

export const storeRealEstateDetailsMediaError = payload => ({
  type: STORE_REAL_ESTATE_DETAILS_MEDIA_ERROR,
  payload,
});

export const fetchRealEstateDetailsMedia = () => ({
  type: FETCH_REAL_ESTATE_DETAILS_MEDIA,
});

export const fetchRealEstateDetailsMediaFulfilled = payload => ({
  type: FETCH_REAL_ESTATE_DETAILS_MEDIA_SUCCESS,
  payload,
});

export const fetchRealEstateDetailsMediaError = payload => ({
  type: FETCH_REAL_ESTATE_DETAILS_MEDIA_ERROR,
  payload,
});

export const removeRealEstateDetailsMedia = payload => ({
  type: REMOVE_REAL_ESTATE_DETAILS_MEDIA,
  payload,
});

export const removeRealEstateDetailsMediaFulfilled = payload => ({
  type: REMOVE_REAL_ESTATE_DETAILS_MEDIA_SUCCESS,
  payload,
});

export const removeRealEstateDetailsMediaError = payload => ({
  type: REMOVE_REAL_ESTATE_DETAILS_MEDIA_ERROR,
  payload,
});

const initialState = {
  data: undefined,
  loading: false,
  mediaLoading: {
    loading: false,
    remove: false,
    upload: false,
    uploadTransactionCount: 0,
  },
  media: [],
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case FETCH_REAL_ESTATE_DETAILS:
      return {
        ...state,
        loading: true,
      };
    case FETCH_REAL_ESTATE_DETAILS_SUCCESS:
      return {
        ...state,
        data: action.payload.data,
        loading: false,
      };
    case FETCH_REAL_ESTATE_DETAILS_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    case STORE_REAL_ESTATE_DETAILS:
      return {
        ...state,
        loading: true,
      };
    case STORE_REAL_ESTATE_DETAILS_SUCCESS:
      return {
        ...state,
        data: {
          ...state.data,
          ...action.payload.data,
        },
        loading: false,
      };
    case STORE_REAL_ESTATE_DETAILS_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    case STORE_REAL_ESTATE_DETAILS_MEDIA:
      return {
        ...state,
        mediaLoading: {
          ...state.mediaLoading,
          upload: true,
          uploadTransactionCount: state.mediaLoading.uploadTransactionCount + 1,
        },
      };
    case STORE_REAL_ESTATE_DETAILS_MEDIA_SUCCESS:
      return {
        ...state,
        media: action.payload.data,
        mediaLoading: {
          ...state.mediaLoading,
          upload: false,
          uploadTransactionCount: state.mediaLoading.uploadTransactionCount - 1,
        },
      };
    case STORE_REAL_ESTATE_DETAILS_MEDIA_ERROR:
      return {
        ...state,
        error: action.payload,
        mediaLoading: {
          ...state.mediaLoading,
          upload: false,
          uploadTransactionCount: state.mediaLoading.uploadTransactionCount - 1,
        },
      };
    case FETCH_REAL_ESTATE_DETAILS_MEDIA:
      return {
        ...state,
        mediaLoading: {
          ...state.mediaLoading,
          loading: true,
        },
      };
    case FETCH_REAL_ESTATE_DETAILS_MEDIA_SUCCESS:
      return {
        ...state,
        media: action.payload.data,
        mediaLoading: {
          ...state.mediaLoading,
          loading: false,
        },
      };
    case FETCH_REAL_ESTATE_DETAILS_MEDIA_ERROR:
      return {
        ...state,
        error: action.payload,
        mediaLoading: {
          ...state.mediaLoading,
          loading: false,
        },
      };
    case REMOVE_REAL_ESTATE_DETAILS_MEDIA:
      return {
        ...state,
        mediaLoading: {
          ...state.mediaLoading,
          remove: true,
        },
      };
    case REMOVE_REAL_ESTATE_DETAILS_MEDIA_SUCCESS:
      // we should delete it dynamically too to avoid calling to get all media again
      const filteredMedia = state.media.filter(el => {
        return el.id !== +action.payload.data.id;
      });

      return {
        ...state,
        media: filteredMedia,
        mediaLoading: {
          ...state.mediaLoading,
          remove: false,
        },
      };
    case REMOVE_REAL_ESTATE_DETAILS_MEDIA_ERROR:
      return {
        ...state,
        error: action.payload,
        mediaLoading: {
          ...state.mediaLoading,
          remove: false,
        },
      };
    default:
      return state;
  }
}

export const getRealEstateDetailsEpic = action$ =>
  action$.pipe(
    ofType(FETCH_REAL_ESTATE_DETAILS),
    mergeMap(() =>
      from(
        axios.get(`${API_URL}/real-estate-details`, {
          headers: getSuuidHeader(),
        })
      ).pipe(
        mergeMap(response => {
          return of(
            fetchRealEstateDetailsFulfilled(response.data),
            wizardErrorClear()
          );
        }),
        catchError(error =>
          of(
            fetchRealEstateDetailsError(
              error.response && error.response.data.error
            ),
            wizardError(error.response.data.error)
          )
        )
      )
    )
  );

export const storeRealEstateDetailsEpic = action$ =>
  action$.pipe(
    ofType(STORE_REAL_ESTATE_DETAILS),
    mergeMap(action =>
      from(
        axios.post(`${API_URL}/real-estate-details`, action.payload, {
          headers: {
            suuid: getSuuid(),
          },
        })
      ).pipe(
        mergeMap(response => {
          return of(
            storeRealEstateDetailsFulfilled(response.data),
            wizardErrorClear()
          );
        }),
        catchError(error =>
          of(
            storeRealEstateDetailsError(
              error.response && error.response.data.error
            ),
            wizardError(error.response.data.error)
          )
        )
      )
    )
  );

export const storeRealEstateDetailsMediaEpic = action$ =>
  action$.pipe(
    ofType(STORE_REAL_ESTATE_DETAILS_MEDIA),
    mergeMap(action =>
      from(
        axios.post(`${API_URL}/real-estate-details/media`, action.payload, {
          headers: {
            suuid: getSuuid(),
            'Content-Type': 'multipart/form-data',
          },
        })
      ).pipe(
        mergeMap(response => {
          return of(
            storeRealEstateDetailsMediaFulfilled(response.data),
            wizardErrorClear()
          );
        }),
        catchError(error =>
          of(
            storeRealEstateDetailsMediaError(
              error.response && error.response.data.error
            ),
            wizardError(error.response.data.error)
          )
        )
      )
    )
  );

export const fetchRealEstateDetailsMediaEpic = action$ =>
  action$.pipe(
    ofType(FETCH_REAL_ESTATE_DETAILS_MEDIA),
    mergeMap(() =>
      from(
        axios.get(`${API_URL}/real-estate-details/media`, {
          headers: getSuuidHeader(),
        })
      ).pipe(
        mergeMap(response => {
          return of(
            fetchRealEstateDetailsMediaFulfilled(response.data),
            wizardErrorClear()
          );
        }),
        catchError(error =>
          of(
            fetchRealEstateDetailsMediaError(
              error.response && error.response.data.error
            ),
            wizardError(error.response.data.error)
          )
        )
      )
    )
  );

export const removeRealEstateDetailsMediaEpic = action$ =>
  action$.pipe(
    ofType(REMOVE_REAL_ESTATE_DETAILS_MEDIA),
    mergeMap(action =>
      from(
        axios.delete(`${API_URL}/real-estate-details/media/${action.payload}`, {
          headers: getSuuidHeader(),
        })
      ).pipe(
        mergeMap(response => {
          return of(
            removeRealEstateDetailsMediaFulfilled(response.data),
            wizardErrorClear()
          );
        }),
        catchError(error =>
          of(
            removeRealEstateDetailsMediaError(
              error.response && error.response.data.error
            ),
            wizardError(error.response.data.error)
          )
        )
      )
    )
  );
