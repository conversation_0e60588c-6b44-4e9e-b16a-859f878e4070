import { ofType } from 'redux-observable';
import { mergeMap, catchError } from 'rxjs/operators';
import { of, from } from 'rxjs';
import { API_URL } from '../../config/config';
import { setSuuid, getSuuid } from '../../helpers/auth';
import { setLoanType } from '../../helpers/common';
import { wizardError, wizardErrorClear } from './wizard';
import axios from 'axios';
import _ from 'lodash';

export const DOCUMENT_SUBMIT_OASL = 'globalcredit/citizen/DOCUMENT_SUBMIT_OASL';
export const DOCUMENT_SUBMIT_OIQL = 'globalcredit/citizen/DOCUMENT_SUBMIT_OIQL';
export const DOCUMENT_SUBMIT_SUCCESS =
  'globalcredit/citizen/DOCUMENT_SUBMIT_SUCCESS';
export const DOCUMENT_SUBMIT_ERROR =
  'globalcredit/citizen/DOCUMENT_SUBMIT_ERROR';

export const CITIZEN_FETCH = 'globalcredit/citizen/CITIZEN_FETCH';
export const CITIZEN_FETCHED = 'globalcredit/citizen/CITIZEN_FETCHED';
export const CITIZEN_ERROR = 'globalcredit/citizen/CITIZEN_ERROR';

export const TRADE_VEHICLE_FETCH = 'globalcredit/citizen/TRADE_VEHICLE_FETCH';
export const TRADE_VEHICLE_FETCHED =
  'globalcredit/citizen/TRADE_VEHICLE_FETCHED';
export const TRADE_VEHICLE_ERROR = 'globalcredit/citizen/TRADE_VEHICLE_ERROR';

export const FETCH_DISCOUNT = 'globalcredit/loan/FETCH_DISCOUNT';
export const FETCH_DISCOUNT_SUCCESS =
  'globalcredit/loan/FETCH_DISCOUNT_SUCCESS';
export const FETCH_DISCOUNT_ERROR = 'globalcredit/loan/FETCH_DISCOUNT_ERROR';

export const submitDocumentOasl = payload => ({
  type: DOCUMENT_SUBMIT_OASL,
  payload,
});

export const submitDocumentOiql = payload => ({
  type: DOCUMENT_SUBMIT_OIQL,
  payload,
});

export const submitDocumentFulfilled = payload => ({
  type: DOCUMENT_SUBMIT_SUCCESS,
  payload,
});

export const submitDocumentError = payload => ({
  type: DOCUMENT_SUBMIT_ERROR,
  payload,
});

export const fetchCitizen = payload => ({
  type: CITIZEN_FETCH,
  payload,
});

export const fetchCitizenFulfilled = payload => ({
  type: CITIZEN_FETCHED,
  payload,
});

export const fetchCitizenError = payload => ({
  type: CITIZEN_ERROR,
  payload,
});

export const fetchTradeVehicle = payload => ({
  type: TRADE_VEHICLE_FETCH,
  payload,
});

export const fetchTradeVehicleFulfilled = payload => ({
  type: TRADE_VEHICLE_FETCHED,
  payload,
});

export const fetchTradeVehicleError = payload => ({
  type: TRADE_VEHICLE_ERROR,
  payload,
});

export const fetchDiscount = payload => ({
  type: FETCH_DISCOUNT,
  payload,
});

export const fetchDiscountFulfilled = payload => ({
  type: FETCH_DISCOUNT_SUCCESS,
  payload,
});

export const fetchDiscountError = payload => ({
  type: FETCH_DISCOUNT_ERROR,
  payload,
});

const initialState = {
  data: undefined,
  loading: false,
  discount: null,
  submitDocumentSuccess: false,
};

const modifyCredit = citizen => {
  if (citizen.credit) {
    if (citizen.credit.ovl && citizen.credit.ovl.length === 0) {
      citizen.credit.ovl = null;
    }

    if (citizen.credit.ocl && citizen.credit.ocl.length === 0) {
      citizen.credit.ocl = null;
    }
  }

  return citizen;
};

const concatTradeCredit = (credit, action) => {
  if (credit && credit.ovl) {
    return credit.ovl
      .filter(v => {
        return !v.rejected && v.vehicleInfo.tradeAmount === null;
      })
      .concat([action.payload.data]);
  }

  return [action.payload.data];
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case DOCUMENT_SUBMIT_OASL:
      return {
        loading: true,
      };
    case DOCUMENT_SUBMIT_OIQL:
      return {
        loading: true,
      };
    case DOCUMENT_SUBMIT_SUCCESS:
      return {
        ...state,
        submitDocumentSuccess: action.payload.data.submitDocumentSuccess,
        loading: false,
      };
    case DOCUMENT_SUBMIT_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };

    case CITIZEN_FETCH:
      return {
        loading: true,
      };
    case CITIZEN_FETCHED:
      const citizen = modifyCredit(action.payload.data);

      return {
        data: {
          ...citizen,
          passports: _.keyBy(citizen.passports, 'type'),
        },
        loading: false,
      };
    case CITIZEN_ERROR:
      return {
        error: action.payload,
        loading: false,
      };

    case TRADE_VEHICLE_FETCH:
      return {
        ...state,
        tradeError: null,
        tradeLoading: true,
      };
    case TRADE_VEHICLE_FETCHED:
      const ovl = concatTradeCredit(state.data.credit, action);

      return {
        ...state,
        data: {
          ...state.data,
          credit: {
            ...state.data.credit,
            ovl: ovl,
          },
        },
        tradeLoading: false,
      };
    case TRADE_VEHICLE_ERROR:
      return {
        ...state,
        tradeError: action.payload,
        tradeLoading: false,
      };
    case FETCH_DISCOUNT:
      return {
        ...state,
        loading: true,
      };
    case FETCH_DISCOUNT_SUCCESS:
      return {
        ...state,
        discount: +action.payload.data.discount,
        loading: false,
      };
    case FETCH_DISCOUNT_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    default:
      return state;
  }
}

export const submitDocumentOaslEpic = action$ =>
  action$.pipe(
    ofType(DOCUMENT_SUBMIT_OASL),
    mergeMap(action =>
      axios
        .post(`${API_URL}/submit-document-oasl`, action.payload)
        .then(response => {
          setSuuid(response.headers.suuid);

          setLoanType(action.payload.loan_type_id);

          return submitDocumentFulfilled(response.data);
        })
        .catch(error => {
          return submitDocumentError(
            error.response && error.response.data.error
          );
        })
    )
  );

export const submitDocumentOiqlEpic = action$ =>
  action$.pipe(
    ofType(DOCUMENT_SUBMIT_OIQL),
    mergeMap(action =>
      axios
        .post(`${API_URL}/submit-document-oiql`, action.payload)
        .then(response => {
          setSuuid(response.headers.suuid);

          setLoanType(action.payload.loan_type_id);

          return submitDocumentFulfilled(response.data);
        })
        .catch(error => {
          return submitDocumentError(
            error.response && error.response.data.error
          );
        })
    )
  );

export const citizenEpic = action$ =>
  action$.pipe(
    ofType(CITIZEN_FETCH),
    mergeMap(action =>
      from(
        axios.get(`${API_URL}/citizens`, {
          params: action.payload,
          headers: {
            suuid: getSuuid(),
          },
        })
      ).pipe(
        mergeMap(response => {
          return of(fetchCitizenFulfilled(response.data), wizardErrorClear());
        }),
        catchError(error =>
          of(
            wizardError(error.response.data.error),
            fetchCitizenError(error.response && error.response.data.error)
          )
        )
      )
    )
  );

export const tradeVehicleEpic = action$ =>
  action$.pipe(
    ofType(TRADE_VEHICLE_FETCH),
    mergeMap(action =>
      from(
        axios.get(`${API_URL}/trade-vehicle`, {
          params: {
            vehicle_number: action.payload.vehicleNumber,
            tech_passport: action.payload.techPassport,
            trade_amount: action.payload.tradeAmount,
          },
          headers: {
            suuid: getSuuid(),
          },
        })
      ).pipe(
        mergeMap(response => {
          return of(
            fetchTradeVehicleFulfilled(response.data),
            wizardErrorClear()
          );
        }),
        catchError(error =>
          of(
            fetchTradeVehicleError(error.response && error.response.data.error)
          )
        )
      )
    )
  );

export const fetchDiscountEpic = action$ =>
  action$.pipe(
    ofType(FETCH_DISCOUNT),
    mergeMap(action => {
      return axios
        .get(`${API_URL}/qr-get-discount`, {
          params: action.payload,
        })
        .then(({ data }) => fetchDiscountFulfilled(data))
        .catch(error =>
          fetchDiscountError(error.response && error.response.data.error)
        );
    })
  );
