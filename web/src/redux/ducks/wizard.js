export const WIZARD_ERROR = 'globalcredit/wizard/WIZARD_ERROR';
export const CLEAR_ERROR = 'globalcredit/wizard/CLEAR_ERROR';

export const wizardError = payload => ({
  type: WIZARD_ERROR,
  payload,
});

export const wizardErrorClear = payload => ({
  type: CLEAR_ERROR,
  payload,
});

const initialState = {
  error: null,
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case WIZARD_ERROR:
      return {
        ...state,
        error: action.payload,
      };
    case CLEAR_ERROR:
      return {
        ...state,
        error: null,
      };
    default:
      return state;
  }
}
