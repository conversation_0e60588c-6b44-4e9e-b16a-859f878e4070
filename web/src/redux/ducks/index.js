/**
 * We use 'Ducks' proposal for combining reducers,
 * actions, action creators and epics in one file
 *
 * For more information:
 * https://github.com/erikras/ducks-modular-redux
 */

import { combineEpics } from 'redux-observable';
import { combineReducers } from 'redux';
import { cloneDeep } from 'lodash';
import axios from 'axios';
import humps from 'humps';
import citizen, {
  citizenEpic,
  submitDocumentOaslEpic,
  submitDocumentOiqlEpic,
  tradeVehicleEpic,
  fetchDiscountEpic,
} from './citizen';
import arpiSolar, {
  fetchVillagesEpic,
  fetchTermsEpic,
  fetchSolarPanelTypesEpic,
} from './arpiSolar';
import agentSchedules, { agentSchedulesEpic } from './agentSchedules';
import loanConfigs, { loanConfigsForCitizenEpic } from './loanConfigs';
import appConfigs, {
  fetchAppStatusEpic,
  fetchServerTimeEpic,
} from './appConfigs';
import faceRecognition, {
  allowFaceRecognitionEpic,
  faceRecognitionEpic,
  storeEkengPhotoEpic,
} from './faceRecognition';
import login, { loginEpic, logoutEpic } from './login';
import cashOffices, {
  cashOfficesEpic,
  fetchCashOfficesByPublicIdEpic,
} from './cashOffices';
import carVerification, {
  fetchCarVerificationEpic,
  storeCarVerificationEpic,
} from './carVerification';
import wizard from './wizard';
import transferTypes, { transferTypesEpic } from './transferTypes';
import loan, {
  fetchLoanEpic,
  approveOaslEpic,
  approveLoanEpic,
  approvedLoanEpic,
  getPersonalInfoEpic,
  getLoanDocumentsEpic,
  storeTransferInfoEpic,
  updatePersonalInfoEpic,
  fetchLoanByPublicIdEpic,
} from './loan';
import smsValidation, {
  validateCodeEpic,
  getCodeEpic,
  expireCodeEpic,
} from './smsValidation';
import pipes, { fetchPipeTypesEpic } from './pipes';
import smsIdentityVerification, {
  verifySmsEpic,
  sendVerificationSmsEpic,
  resendVerificationSmsEpic,
} from './smsIdentityVerification';

import moderator, {
  assignEpic,
  getOneAssignedEpic,
  confirmEpic,
  rejectEpic,
  pollEpic,
  getTagsEpic,
} from './moderator';
import referralCode, {
  generateReferralAgreementEpic,
  getReferralCodeEpic,
  verifyReferralCodeEpic,
  setReferralCodeEpic,
} from './referralCode';
import realEstateEvaluationCompanies, {
  fetchEvaluationCompaniesEpic,
} from './realEstateEvaluationCompanies';
import realEstateDetails, {
  getRealEstateDetailsEpic,
  storeRealEstateDetailsEpic,
  storeRealEstateDetailsMediaEpic,
  fetchRealEstateDetailsMediaEpic,
  removeRealEstateDetailsMediaEpic,
} from './realEstateDetails';
import regions, { fetchRegionsEpic } from './regions';
import realEstateDeveloperCompanies, {
  fetchPredefinedRealEstateInfoEpic,
  fetchRealEstateDeveloperCompanyEpic,
} from './realEstateDeveloperCompanies';
import pay4me, { fetchPay4meEpic } from './pay4me';
import purchaseOrder, {
  submitPurchaseOrderEpic,
  checkPurchaseOrderExistenceEpic,
  fetchPurchaseOrdersEpic,
  fetchMerchantDetailsEpic,
} from './purchaseOrder';
import OVILOrder, {
  submitOVILOrderEpic,
  checkOVILOrderExistenceEpic,
  fetchOVILOrdersEpic,
  fetchOVILMerchantDetailsEpic,
  fetchVehicleMarksEpic,
} from './loanApplicationOrder';

import { MULTIPART_FORM_DATA } from '../../constants';

axios.interceptors.request.use(
  config => {
    // We need skip decamelize keys in this step if content-type is multipart/form-data
    if (config.headers['Content-Type'] !== MULTIPART_FORM_DATA) {
      config = {
        ...config,
        data: humps.decamelizeKeys(config.data),
      };
    }

    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

axios.interceptors.response.use(
  response => {
    if (response.status >= 200 && response.status <= 400) {
      // In some cases, we do not need to camelize response data
      if (response.config.skipCamelize) {
        return response;
      }

      return {
        ...response,
        data: humps.camelizeKeys(response.data),
      };
    }

    return response;
  },
  error => {
    if (error.response && error.response.status === 422) {
      const responseCopy = cloneDeep(error.response);
      responseCopy.data.error.errors = humps.camelizeKeys(
        error.response.data.error.errors
      );

      return Promise.reject({ response: responseCopy });
    }

    return Promise.reject(error);
  }
);

export const rootEpic = combineEpics(
  fetchTermsEpic,
  fetchVillagesEpic,
  fetchSolarPanelTypesEpic,
  citizenEpic,
  tradeVehicleEpic,
  submitDocumentOaslEpic,
  submitDocumentOiqlEpic,
  allowFaceRecognitionEpic,
  faceRecognitionEpic,
  storeEkengPhotoEpic,
  loginEpic,
  logoutEpic,
  verifySmsEpic,
  approveLoanEpic,
  approveOaslEpic,
  cashOfficesEpic,
  approvedLoanEpic,
  transferTypesEpic,
  agentSchedulesEpic,
  getPersonalInfoEpic,
  getLoanDocumentsEpic,
  storeTransferInfoEpic,
  fetchDiscountEpic,
  updatePersonalInfoEpic,
  fetchCarVerificationEpic,
  storeCarVerificationEpic,
  sendVerificationSmsEpic,
  resendVerificationSmsEpic,
  loanConfigsForCitizenEpic,
  fetchAppStatusEpic,
  fetchServerTimeEpic,
  fetchCashOfficesByPublicIdEpic,
  validateCodeEpic,
  fetchLoanEpic,
  getCodeEpic,
  expireCodeEpic,
  fetchLoanByPublicIdEpic,
  fetchPipeTypesEpic,
  assignEpic,
  getOneAssignedEpic,
  confirmEpic,
  rejectEpic,
  pollEpic,
  getTagsEpic,
  generateReferralAgreementEpic,
  getReferralCodeEpic,
  verifyReferralCodeEpic,
  setReferralCodeEpic,
  fetchEvaluationCompaniesEpic,
  setReferralCodeEpic,
  getTagsEpic,
  fetchRegionsEpic,
  getRealEstateDetailsEpic,
  storeRealEstateDetailsEpic,
  storeRealEstateDetailsMediaEpic,
  fetchRealEstateDetailsMediaEpic,
  removeRealEstateDetailsMediaEpic,
  fetchRealEstateDeveloperCompanyEpic,
  fetchPredefinedRealEstateInfoEpic,
  fetchPay4meEpic,
  submitPurchaseOrderEpic,
  checkPurchaseOrderExistenceEpic,
  fetchPurchaseOrdersEpic,
  fetchMerchantDetailsEpic,
  submitOVILOrderEpic,
  checkOVILOrderExistenceEpic,
  fetchOVILOrdersEpic,
  fetchOVILMerchantDetailsEpic,
  fetchVehicleMarksEpic
);

export const rootReducer = combineReducers({
  realEstateEvaluationCompanies,
  smsIdentityVerification,
  faceRecognition,
  carVerification,
  agentSchedules,
  smsValidation,
  transferTypes,
  cashOffices,
  loanConfigs,
  appConfigs,
  arpiSolar,
  citizen,
  wizard,
  login,
  loan,
  pipes,
  moderator,
  referralCode,
  realEstateDetails,
  regions,
  realEstateDeveloperCompanies,
  pay4me,
  purchaseOrder,
  OVILOrder,
});
