import { ofType } from 'redux-observable';
import { mergeMap, catchError } from 'rxjs/operators';
import { of, from } from 'rxjs';
import axios from 'axios';

import { API_URL } from '../../config/config';
import { HAS_ERROR_QUERY_STRING } from '../../constants';
import { getSuuidHeader, setSuuid } from '../../helpers/auth';
import { wizardError, wizardErrorClear } from './wizard';
import { composeQueryParams, setLoanType } from '../../helpers/common';

export const VERIFY_SMS = 'globalcredit/sms/VERIFY_SMS';
export const SMS_VERIFIED = 'globalcredit/sms/SMS_VERIFIED';
export const VERIFY_SMS_ERROR = 'globalcredit/sms/VERIFY_SMS_ERROR';

export const SEND_VERIFICATION_SMS = 'globalcredit/sms/SEND_VERIFICATION_SMS';
export const SEND_VERIFICATION_SMS_SUCCESS =
  'globalcredit/sms/SEND_VERIFICATION_SMS_SUCCESS';
export const SEND_VERIFICATION_SMS_ERROR =
  'globalcredit/sms/SEND_VERIFICATION_SMS_ERROR';

export const RESEND_VERIFICATION_SMS =
  'globalcredit/sms/RESEND_VERIFICATION_SMS';
export const RESEND_VERIFICATION_SMS_SUCCESS =
  'globalcredit/sms/RESEND_VERIFICATION_SMS_SUCCESS';
export const RESEND_VERIFICATION_SMS_ERROR =
  'globalcredit/sms/RESEND_VERIFICATION_SMS_ERROR';

export const CLEAN_UP_SERVER_ERROR = 'globalcredit/sms/CLEAN_UP_SERVER_ERROR';

export const sendVerificationSms = (payload, history) => ({
  type: SEND_VERIFICATION_SMS,
  payload,
  history,
});

export const sendVerificationSmsFulfilled = payload => ({
  type: SEND_VERIFICATION_SMS_SUCCESS,
  payload,
});
export const sendVerificationSmsError = payload => ({
  type: SEND_VERIFICATION_SMS_ERROR,
  payload,
});

export const resendVerificationSms = () => ({
  type: RESEND_VERIFICATION_SMS,
});
export const resendVerificationSmsFulfilled = payload => ({
  type: RESEND_VERIFICATION_SMS_SUCCESS,
  payload,
});
export const resendVerificationSmsError = payload => ({
  type: RESEND_VERIFICATION_SMS_ERROR,
  payload,
});

export const verifySms = payload => ({
  type: VERIFY_SMS,
  payload,
});
export const verifySmsFulfilled = payload => ({
  type: SMS_VERIFIED,
  payload,
});
export const verifySmsError = payload => ({
  type: VERIFY_SMS_ERROR,
  payload,
});

export const cleanUpServerError = () => ({
  type: CLEAN_UP_SERVER_ERROR,
});

const initialState = {
  loading: false,
  verified: false,
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case SEND_VERIFICATION_SMS:
      return {
        ...state,
        loading: true,
      };
    case SEND_VERIFICATION_SMS_SUCCESS:
      return {
        ...state,
        error: null,
        data: action.payload.data,
        loading: false,
      };
    case SEND_VERIFICATION_SMS_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };

    case RESEND_VERIFICATION_SMS:
      return {
        ...state,
        loading: true,
      };
    case RESEND_VERIFICATION_SMS_SUCCESS:
      return {
        ...state,
        error: null,
        data: action.payload.data,
        loading: false,
      };
    case RESEND_VERIFICATION_SMS_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };

    case VERIFY_SMS:
      return {
        ...state,
        loading: true,
      };
    case SMS_VERIFIED:
      return {
        ...state,
        error: null,
        verified: action.payload.data.verified,
        loading: false,
      };
    case VERIFY_SMS_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    case CLEAN_UP_SERVER_ERROR:
      return {
        ...state,
        error: null,
      };
    default:
      return state;
  }
}

export const sendVerificationSmsEpic = action$ =>
  action$.pipe(
    ofType(SEND_VERIFICATION_SMS),
    mergeMap(action =>
      axios
        .post(`${API_URL}/send-verification-sms`, action.payload)
        .then(response => {
          setSuuid(response.headers.suuid);

          setLoanType(action.payload.loan_type_id);

          wizardErrorClear();
          return sendVerificationSmsFulfilled(response.data);
        })
        .catch(error => {
          //We don't let the user go forward if in the home page url have hasError=true param (Google Analytics)
          const { history } = action;
          const queryString = composeQueryParams(
            history,
            HAS_ERROR_QUERY_STRING
          );

          if (queryString) {
            history.push(queryString);
          }

          wizardError(error.response.data.error);
          return sendVerificationSmsError(
            error.response && error.response.data.error
          );
        })
    )
  );

export const resendVerificationSmsEpic = action$ =>
  action$.pipe(
    ofType(RESEND_VERIFICATION_SMS),
    mergeMap(() =>
      from(
        axios.post(`${API_URL}/resend-verification-sms`, null, {
          headers: getSuuidHeader(),
        })
      ).pipe(
        mergeMap(response =>
          of(resendVerificationSmsFulfilled(response.data), wizardErrorClear())
        ),
        catchError(error =>
          of(
            wizardError(error.response.data.error),
            resendVerificationSmsError(
              error.response && error.response.data.error
            )
          )
        )
      )
    )
  );

export const verifySmsEpic = action$ =>
  action$.pipe(
    ofType(VERIFY_SMS),
    mergeMap(action =>
      from(
        axios.post(
          `${API_URL}/verify-sms`,
          { code: action.payload.code },
          {
            headers: getSuuidHeader(),
          }
        )
      ).pipe(
        mergeMap(response =>
          of(verifySmsFulfilled(response.data), wizardErrorClear())
        ),
        catchError(error =>
          of(
            wizardError(error.response.data.error),
            verifySmsError(error.response && error.response.data.error)
          )
        )
      )
    )
  );
