import 'react-app-polyfill/ie11';
import 'react-app-polyfill/stable';

import React from 'react';
import ReactDOM from 'react-dom';
import { Provider } from 'react-redux';
import { <PERSON><PERSON><PERSON><PERSON>outer as Router } from 'react-router-dom';
import { I18nextProvider } from 'react-i18next';
import { configureStore } from './redux/configureStore';

import AppComponent from './AppComponent';

import { setLoanType } from './helpers/common';
import { LOAN_TYPES } from './constants';
import {
  IQOS_HOSTNAME,
  ARPI_SOLAR_HOSTNAME,
  INTERNAL_BNPL_HOSTNAME,
  OVIL_HOSTNAME,
} from './config/config';
import i18n from './i18n';

import './logs/Log';

import 'semantic-ui-css/semantic.min.css';
import 'ao-components/dist/index.css';
import './index.scss';

const store = configureStore();

if (window.location.hostname === IQOS_HOSTNAME) {
  setLoanType(LOAN_TYPES.OIQL.id);
} else if (window.location.hostname === ARPI_SOLAR_HOSTNAME) {
  setLoanType(LOAN_TYPES.OASL.id);
} else if (window.location.hostname === INTERNAL_BNPL_HOSTNAME) {
  setLoanType(LOAN_TYPES.BNPL.id);
} else if (window.location.hostname === OVIL_HOSTNAME) {
  setLoanType(LOAN_TYPES.OVL.id);
}

ReactDOM.render(
  <Provider store={store}>
    <I18nextProvider i18n={i18n}>
      <Router>
        <AppComponent />
      </Router>
    </I18nextProvider>
  </Provider>,
  document.getElementById('root')
);

// TODO: Enable when we go live
// registerServiceWorker();
