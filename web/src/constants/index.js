import i18n from '../i18n';

export const TOKEN = 'token';

export const SUUID = 'SUUID';

export const LOAN_TYPE = 'LOAN_TYPE';

export const TIMER_EXP = 900; // 15 minutes in seconds

export const DOCUMENT_POLLING_INTERVAL = 5000; // 5 seconds

export const DOCUMENT_POLLING_TIMEOUT = 180000; // 3 minutes

export const CHECK_CAMERA_STATE_INTERVAL = 2000;

export const STANDARD_DATETIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';

export const APP_DATE_FORMAT = 'YYYY-MM-DD';

export const MULTIPART_FORM_DATA = 'multipart/form-data';

export const MAP_ZOOM = 12;
export const YEREVAN_INITIAL_LAT = 40.1792;
export const YEREVAN_INITIAL_LNG = 44.4991;

export const GLOBALCREDIT_LAT = 40.214106;
export const GLOBALCREDIT_LNG = 44.491185;

export const BANK_CODE_LENGTH = 3;
export const BANK_ACOUNT_NUM_MIN_LEN = 12;
export const BANK_ACOUNT_NUM_MAX_LEN = 16;

export const BIRTH_DATE_FORMAT = 'DD/MM/YYYY';

export const FORBIDDEN_ERROR_CODE = 403;

export const OK_STATUS_CODE = 200;

export const INTERNAL_SERVER_ERROR_STATUS_CODE = 500;

export const ARM_TIMEZONE = '+0400';

export const TIME_FORMAT = 'HH:mm';

export const CREDIT_CARD_MASK = '9999 9999 9999 9999';
export const VEHICLE_NUMBER_MASK = '99 aa 999';
export const TECH_PASSPORT_MASK = 'aa 999999';
export const PHONE_NUMBER_MASK = '(99) 999 999';

export const VIN_LENGTH = 17;

export const PASSPORT_TYPES = [
  'NON_BIOMETRIC_PASSPORT',
  'BIOMETRIC_PASSPORT',
  'ID_CARD',
];

export const TRANSFER_METHODS = [
  {
    id: 1,
    method: 'card_to_card',
    type: 'card_to_card',
    serverType: 'App\\Models\\CardToCardPayment',
    route: 'card-to-card-transfer',
  },
  {
    id: 2,
    method: 'cash_payment',
    type: 'cash',
    serverType: 'App\\Models\\CashPayment',
    route: 'cash-transfer',
  },
  {
    id: 3,
    method: 'idram_wallet',
    type: 'wallet',
    serverType: 'App\\Models\\IdramWalletPayment',
    route: 'idram-wallet-transfer',
  },
  {
    id: 4,
    method: 'wire_transfer',
    type: 'bank',
    serverType: 'App\\Models\\WirePayment',
    route: 'wire-transfer',
  },
  {
    id: 5,
    method: 'easypay_wallet',
    type: 'wallet',
    serverType: 'App\\Models\\EasypayWalletPayment',
    route: 'easypay-wallet-transfer',
  },
  {
    id: 6,
    method: 'product_provision',
    type: 'product',
    serverType: 'App\\Models\\ProductProvision',
    route: 'product-provision',
  },
];

export const WALLET_TRANSFERS = ['idram', 'easypay'];

export const CONFIRMED_NO_PAY = 'CONFIRMED_NO_PAY';

export const FINISHED = 'FINISHED';

export const REJECTED = 'REJECTED';

export const FINISHED_WITHOUT_MODERATION = 'FINISHED_WITHOUT_MODERATION';

export const FAILED_PAYMENT = 'FAILED';

export const BANK_CODES = {
  115: 'Armbusinessbank CJSC',
  118: 'ID Bank CJSC',
  151: 'Araratbank OJSC',
  157: 'Ameriabank CJSC',
  160: 'VTB Bank Armenia CJSC',
  163: 'Armeconombank OJSC',
  166: 'Evocabank CJSC',
  175: 'Armeconombank OJSC (BTA bank)',
  193: 'Converse Bank CJSC',
  205: 'InecoBank CJSC',
  208: 'Mellat bank CJSC',
  214: 'Byblos Bank Armenia CJSC',
  217: 'HSBC Bank Armenia CJSC',
  220: 'ACBA-Credit Agricol Bank CJSC',
  223: 'Artsakhbank CJSC',
  241: 'UniBank CJSC',
  247: 'Ardshinbank CJSC',
  250: 'ArmSwissBank CJSC',
  253: 'Inecobank CJSC (Procredit bank)',
  256: 'Panarmenian Bank OJSC',
  103: 'Central bank',
  248: 'Ardshinbank CJSC (Areximbank)',
  181: 'Ararat bank CJSC (AD bank)',
  0: 'Other',
};

export const LOAN_TYPES = {
  OCL: {
    id: 1,
    name: 'ocl',
    documents_count: 3,
    documents: {
      PERSONAL_SHEET: 'personal_sheet_private',
      CONTRACT: 'contract_ocl_private',
      WHAT_TO_DO: 'what_to_do',
    },
  },
  OVL: {
    id: 2,
    name: 'ovl',
    documents_count: 4,
    documents: {
      PERSONAL_SHEET: 'personal_sheet_private',
      CONTRACT: 'contract_ovl_private',
      CONTRACT_TRADE: 'contract_ovl_trade_private',
      MORTGAGE_CONTRACT: 'mortgage_contract_private',
      MORTGAGE_CONTRACT_TRADE: 'mortgage_contract_trade_private',
      WHAT_TO_DO: 'what_to_do',
    },
  },
  COMMON: {
    id: 3,
    name: 'common',
  },
  OIQL: {
    id: 4,
    name: 'oiql',
    documents_count: 3,
    documents: {
      PERSONAL_SHEET: 'personal_sheet',
      CONTRACT: 'contract_oiql',
      WHAT_TO_DO: 'what_to_do',
    },
  },
  OASL: {
    id: 5,
    name: 'oasl',
    documents_count: 4,
    documents: {
      PERSONAL_SHEET: 'personal_sheet_oasl',
      JO_CONTRACT: 'jo_contract_oasl',
      KFW_CONTRACT: 'kfw_contract_oasl',
      JO_MORTGAGE_CONTRACT: 'jo_mortgage_contract_oasl',
      KFW_MORTGAGE_CONTRACT: 'kfw_mortgage_contract_oasl',
      WHAT_TO_DO: 'what_to_do',
    },
  },
  REML: {
    id: 12,
    SREML: {
      id: 2,
      name: 'sreml',
      documents_count: 4,
      documents: {
        PERSONAL_SHEET: 'personal_sheet_reml_private',
        CONTRACT: 'contract_sreml_private',
        MORTGAGE_CONTRACT_SREML: 'mortgage_contract_sreml_private',
        WHAT_TO_DO: 'what_to_do',
      },
    },
    PREML: {
      id: 1,
      PREML_RENSHIN: {
        id: 1,
        name: 'preml',
        documents_count: 4,
        documents: {
          PERSONAL_SHEET: 'personal_sheet_reml_private',
          CONTRACT_PREML_RENSHIN: 'contract_preml_renshin_private',
          LAW_MORTGAGE_PREML_RENSHIN: 'law_mortgage_preml_renshin_private',
          WHAT_TO_DO: 'what_to_do',
        },
      },
      PREML_LUYSER: {
        id: 2,
        name: 'preml',
        documents_count: 4,
        documents: {
          PERSONAL_SHEET: 'personal_sheet_reml_private',
          CONTRACT_PREML_LUYSER: 'contract_preml_luyser_private',
          LAW_MORTGAGE_PREML_LUYSER: 'law_mortgage_preml_luyser_private',
          WHAT_TO_DO: 'what_to_do',
        },
      },
    },
  },
  OBL: {
    id: 16,
    name: 'obl',
    documents_count: 3,
    documents: {
      PERSONAL_SHEET: 'personal_sheet_obl_private',
      CONTRACT: 'contract_obl_private',
      WHAT_TO_DO: 'what_to_do',
    },
  },
  BNPL: {
    id: 13,
    name: 'bnpl',
  },
};

export const DEVELOPER_COMPANIES = [
  {
    id: 1,
    key: 'renshin',
    text: i18n.t('loan.steps.real_estate_loan.developer_companies.renshin'),
  },
  {
    id: 2,
    key: 'luyser',
    text: i18n.t('loan.steps.real_estate_loan.developer_companies.luyser'),
  },
];

export const PREDEFINED_REAL_ESTATE = {
  TOKEN_LENGTH: 10,
};

export const OBL_PAY4ME = {
  TOKEN_LENGTH: 36,
};

export const NOTIFICATION_METHODS = [
  {
    id: 0,
    text: i18n.t('loan.steps.personal_info.notification.email'),
    value: 'email',
  },
  {
    id: 1,
    text: i18n.t('loan.steps.personal_info.notification.in_person'),
    value: 'in_person',
  },
  {
    id: 2,
    text: i18n.t('loan.steps.personal_info.notification.post'),
    value: 'post',
  },
];

export const DISPUTE_SOLUTION = [
  {
    id: 0,
    text: i18n.t('loan.steps.personal_info.dispute_solution.gnm_arbitration'),
    value: 'gnm_arbitration',
  },
  {
    id: 1,
    text: i18n.t('loan.steps.personal_info.dispute_solution.optimum'),
    value: 'optimum',
  },
  {
    id: 2,
    text: i18n.t('loan.steps.personal_info.dispute_solution.court'),
    value: 'court',
  },
  {
    id: 3,
    text: i18n.t('loan.steps.personal_info.dispute_solution.arbitration'),
    value: 'arbitration',
  },
];

export const ARM_PHONE_CODES = [
  '33',
  '41',
  '43',
  '44',
  '47',
  '49',
  '55',
  '77',
  '91',
  '93',
  '94',
  '95',
  '96',
  '97',
  '98',
  '99',
];

export const EXPIRED_SUUID_ERROR_CODE = 4008;

export const FRACTION_NO = 0;

export const OFFICE_TIME_FORMAT = 'HH:mm:ss';

export const WORK_TIME_FORMAT = 'HH:mm';

export const APR_DATE_FORMAT = 'YYYY-MM-DD';

export const MONTHS_COUNT_IN_YEAR = 12;

export const UNSUPPORTED_BROWSERS = ['MIUI Browser'];

export const SAMSUNG_DEFAULT_BROWSER = 'Samsung Browser';

export const WEBCAM_VIDEO_CONSTRAINTS = {
  width: {
    ideal: 1920,
    max: 4096,
    min: 320,
  },
};

export const STREAM_RECORDING_DURATION = 5 * 60 * 1000; // 5 minutes
export const STREAM_RECORDING_UNMOUNT_DURATION = 2 * 60 * 1000; // 2 minutes

export const HOME_SCREEN_PDFS = {
  JERM_OJAX_AGREEMENT: 'http://cashme.am/home_screen_pdfs/jo_agreement.pdf',
  JERM_OJAX_LOAN_TERMS:
    'http://cashme.am/home_screen_pdfs/sarqeri_dzerqberman_vark.pdf',
  AGREEMENT: 'http://cashme.am/home_screen_pdfs/request_agreement.pdf',
  OIQL: 'http://cashme.am/home_screen_pdfs/iqos_terms.pdf',
  CASHME: 'http://cashme.am/home_screen_pdfs/cashme_terms.pdf',
  REML: 'http://cashme.am/home_screen_pdfs/reml_terms.pdf',
  LUYSER: 'http://cashme.am/home_screen_pdfs/luyser_reml_terms.pdf',
  PAY4ME: 'http://cashme.am/home_screen_pdfs/pay4me_terms.pdf',
  RENSHIN: 'http://cashme.am/home_screen_pdfs/renshin_reml_terms.pdf',
  REAL_ESTATE_REQUIREMENTS:
    'http://cashme.am/home_screen_pdfs/real_estate_requirements.pdf',
  ARMED_TERMS: 'http://cashme.am/home_screen_pdfs/armed_terms.pdf',
};

export const HOME_SCREEN_PDFS_PAGES_COUNT = {
  CASHME: 1,
  REML: 4,
  LUYSER: 2,
  RENSHIN: 3,
  PAY4ME: 2,
};

export const MODERATOR_POLL_TIMEOUT = 10000; // 10 seconds

export const MODERATOR_SETTLE_COUNTDOWN = 180; // 3 minutes in seconds

export const YEREVAN_COORDINATES = [
  { lng: 44.417859, lat: 40.242194 },
  { lng: 44.417859, lat: 40.24221 },
  { lng: 44.43333, lat: 40.252021 },
  { lng: 44.440175, lat: 40.256295 },
  { lng: 44.445325, lat: 40.25433 },
  { lng: 44.469872, lat: 40.24529 },
  { lng: 44.478455, lat: 40.246862 },
  { lng: 44.496995, lat: 40.250662 },
  { lng: 44.530125, lat: 40.257605 },
  { lng: 44.55021, lat: 40.259439 },
  { lng: 44.576302, lat: 40.261535 },
  { lng: 44.578706, lat: 40.255771 },
  { lng: 44.582997, lat: 40.246469 },
  { lng: 44.585744, lat: 40.238214 },
  { lng: 44.648287, lat: 40.198369 },
  { lng: 44.656193, lat: 40.193144 },
  { lng: 44.659792, lat: 40.190825 },
  { lng: 44.666981, lat: 40.186235 },
  { lng: 44.665809, lat: 40.183221 },
  { lng: 44.664291, lat: 40.178838 },
  { lng: 44.661985, lat: 40.172264 },
  { lng: 44.659117, lat: 40.164486 },
  { lng: 44.657296, lat: 40.159067 },
  { lng: 44.6504402160644, lat: 40.1523818969728 },
  { lng: 44.626014, lat: 40.125515 },
  { lng: 44.609688, lat: 40.118275 },
  { lng: 44.53437, lat: 40.084433 },
  { lng: 44.490794, lat: 40.11136 },
  { lng: 44.4611434936523, lat: 40.111338470459 },
  { lng: 44.440298, lat: 40.129738 },
  { lng: 44.425848, lat: 40.157997 },
  { lng: 44.431624, lat: 40.173799 },
  { lng: 44.436774, lat: 40.187758 },
  { lng: 44.437353, lat: 40.189373 },
  { lng: 44.438751, lat: 40.192963 },
  { lng: 44.416904, lat: 40.218741 },
];

export const FINGERPRINT = 'FINGERPRINT';

export const IS_LUYSER = 'IS_LUYSER';

export const LUYSER = 'LUYSER';

export const IS_BANALI = 'IS_BANALI';

export const IS_PAY4ME = 'IS_PAY4ME';

export const PAY4ME = 'PAY4ME';

export const OPENTOK_AUTH_ERROR = 'OT_AUTHENTICATION_ERROR';

export const MONTHLY_REPAYMENT_LIMIT = {
  OCL: 40000,
  OVL: 40000,
};

export const DSTI_LIMIT = 0.5;

export const DISABLED_REDIRECT_PATHS = [
  '/home',
  '/loan/confirmation',
  '/terms-and-conditions',
  '/bnpl/purchase-orders',
  '/ovil/orders',
];

export const OCL_AMOUNT_LIMIT = 300000;

export const LOAN_TYPE_OPTIONS = [
  {
    id: 0,
    text: i18n.t('loan_types.OCL'),
    value: LOAN_TYPES.OCL.id,
  },
  {
    id: 1,
    text: i18n.t('loan_types.OVL'),
    value: LOAN_TYPES.OVL.id,
  },
  {
    id: 2,
    text: i18n.t('loan_types.REML'),
    value: LOAN_TYPES.REML.id,
  },
];

export const PAY4ME_OPTIONS = [
  {
    id: 0,
    text: i18n.t('loan_types.OBL'),
    value: LOAN_TYPES.OBL.id,
  },
];

export const REAL_ESTATE_OPTIONS = [
  {
    id: 0,
    text: i18n.t('loan_subtypes.REML'),
    value: LOAN_TYPES.REML.id,
  },
];

export const REFERRAL_CODE_INPUTS_RANGE = 6;

export const REML_BOUNDARY_VALUES = {
  REAL_ESTATE_MIN_ALLOWED_PRICE: 2230000,
  REAL_ESTATE_MAX_ALLOWED_PRICE: 90000000,
  MAX_LOAN_AMOUNT: 60000000,
  MIN_LOAN_AMOUNT: 2000000,
};

export const IMAGE_MIME_TYPES = '.jpg,.jpeg,.png,.bmp';

export const CASH_ME_IOS_URL =
  'https://apps.apple.com/am/app/cashme-by-gc/id1596243667';

export const CASH_ME_ANDROID_URL =
  'https://play.google.com/store/apps/details?id=com.globalcreditcjsc.cashme';

export const CURRENCIES = {
  AMD: 'AMD',
};

export const REML_SOLVENCY_BOUNDARY_VALUES = {
  REAL_ESTATE_PRICE: 55000000,
  MORTGAGE_AMOUNT: 35000000,
  NET_SALARY_PERCENTAGE_60: 0.6,
  NET_SALARY_PERCENTAGE_45: 0.45,
};

export const HAS_ERROR_QUERY_STRING = 'hasError=true';

export const ENTER_KEY = {
  NAME: 'Enter',
  CODE: 13,
};

export const PAY4ME_URL = 'https://pay4me.am/';

export const DOWNLOAD_APP_LINK = 'https://onelink.to/cktffd';

export const IS_MOBILE_DEVICE_WIDTH = 580;

export const ORDER_STATUSES = {
  PENDING: 'PENDING',
  REJECTED: 'REJECTED',
  APPROVED: 'APPROVED',
  EXPIRED: 'EXPIRED',
};
