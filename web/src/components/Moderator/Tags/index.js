import React, { Component } from 'react';
import { Label } from 'semantic-ui-react';
import { getTags } from '../../../redux/ducks/moderator';
import { compose } from 'redux';
import { connect } from 'react-redux';
import styles from './index.module.scss';

const TAGS_ACTIVE_COLORS = {
  REJECT: styles.active_tag_reject, // red
  APPROVE: styles.active_tag_approve, // green
};

const TAGS_PASSIVE_COLORS = {
  REJECT: styles.passive_tag_reject, // light red
  APPROVE: styles.passive_tag_approve, // light green
};

class Tags extends Component {
  constructor(props) {
    super(props);
    this.state = {
      tags: [],
    };
  }

  componentDidMount() {
    this.props.getTags();
  }

  static getDerivedStateFromProps(props) {
    return { tags: props.tags };
  }

  handleAddition = tag => {
    let tags = [...this.state.tags];
    let descriptionToAdd = '';
    let descriptionsToRemove = [];

    tags.forEach(i => {
      if (i.id === tag.id) {
        i.isActive = !i.isActive;
        descriptionToAdd = i.isActive ? tag.description : '';
        descriptionsToRemove.push(!i.isActive ? tag.description : '');
      } else if (i.type !== tag.type && !!i.isActive) {
        descriptionsToRemove.push(i.description);
        i.isActive = false;
      }
    });

    const activeTags = tags.filter(i => {
      return i.isActive;
    });

    this.props.onSetActiveTags({
      descriptionToAdd,
      descriptionsToRemove,
      activeTags,
    });
    this.setState({ tags });
  };

  render() {
    const { tags = [] } = this.state;
    return (
      <div id={styles.tags}>
        {tags.map(tag => {
          return (
            <Label
              as="a"
              key={tag.id}
              className={
                tag.isActive
                  ? TAGS_ACTIVE_COLORS[tag.type]
                  : TAGS_PASSIVE_COLORS[tag.type]
              }
              onClick={() => this.handleAddition(tag)}
            >
              {tag.name}
            </Label>
          );
        })}
      </div>
    );
  }
}

function mapStateToProps(state) {
  const {
    moderator: {
      tags: { data: tags },
    },
  } = state;

  return { tags };
}

const mapDispatchToProps = dispatch => {
  return {
    getTags: () => dispatch(getTags()),
  };
};

export default compose(
  connect(
    mapStateToProps,
    mapDispatchToProps
  )
)(Tags);
