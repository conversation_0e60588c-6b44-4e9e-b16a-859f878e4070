import React, { Component } from 'react';
import { withRouter, Switch, Route, Redirect } from 'react-router-dom';
import { withNamespaces } from 'react-i18next';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { Menu } from 'semantic-ui-react';
import UIfx from 'uifx';

import Queued from './Queued';
import AssignedDetails from './AssignedDetails';
import PageTitleNotification from './PageTitleNotification';

import { logout } from '../../redux/ducks/login';
import { startPoll, stopPoll } from '../../redux/ducks/moderator';

import { MODERATOR_POLL_TIMEOUT } from '../../constants';

import notificationAudio from '../../audio/notification.mp3';
import BrowserNotification from './BrowserNotification';

import styles from './index.module.scss';
import { BASE_URL } from '../../config/config';

class Moderator extends Component {
  constructor(props) {
    super(props);

    this.state = {
      activeItem: 'queued',
    };

    this.titleNotification = new PageTitleNotification();
    this.browserNotifications = new BrowserNotification();
  }

  componentDidMount() {
    window.addEventListener('beforeunload', () => {
      this.props.stopPoll();
    });

    this.props.startPoll(MODERATOR_POLL_TIMEOUT);
  }

  componentWillUnmount() {
    this.props.stopPoll();
  }

  componentDidUpdate(prevProps) {
    const { queued, assigned } = this.props;

    this.titleNotification.setTitle(`Cash Me (${queued.length})`);

    if (queued.length && !assigned.length) {
      this.titleNotification.start('New request');
    } else {
      this.titleNotification.stop();
    }

    if (queued.length > prevProps.queued.length) {
      this.playNotificationSound();
      this.createBrowserNotification();
    }
  }

  playNotificationSound() {
    new UIfx(notificationAudio, {
      volume: 1.0,
    }).play();
  }

  createBrowserNotification() {
    const { t } = this.props;

    const title = t('moderator.new_request');
    const body = t('moderator.open');
    const url = `${BASE_URL}/moderator/queued/`;

    this.browserNotifications.create(title, body, url);
  }

  handleItemClick = (e, { name }) => {
    const { history } = this.props;

    history.push(`/moderator/${name}`);

    this.setState({ activeItem: name });
  };

  render() {
    const { logout, t } = this.props;
    const { activeItem } = this.state;

    return (
      <div>
        <Menu
          pointing={true}
          secondary={true}
          size="large"
          className={styles.header}
        >
          <Menu.Menu position="right">
            <Menu.Item
              as="a"
              name="logout"
              onClick={logout}
              className={styles.logout}
            />
          </Menu.Menu>
        </Menu>
        <Menu
          borderless
          vertical
          stackable
          fixed="left"
          className={styles.sidebar}
        >
          <Menu.Item
            name="queued"
            className={styles.sidebar_item}
            active={activeItem === 'queued'}
            onClick={this.handleItemClick}
          >
            <div className={styles.sidebar_item_alignment_container}>
              <span>{t('moderator.menu.dashboard')}</span>
            </div>
          </Menu.Item>

          <Menu.Item
            name="logout"
            onClick={logout}
            className={styles.sidebar_item}
          >
            <div className={styles.sidebar_item_alignment_container}>
              <span>{t('moderator.menu.logout')}</span>
            </div>
          </Menu.Item>
        </Menu>

        <div id={styles.content}>
          <Switch>
            <Route path="/moderator/queued" component={Queued} />
            <Route path="/moderator/assigned/:id" component={AssignedDetails} />

            <Redirect from="/" to="/moderator/queued" />
          </Switch>
        </div>
      </div>
    );
  }
}

function mapStateToProps(state) {
  const {
    moderator: {
      queued: { data: queued },
      assigned: { data: assigned },
    },
  } = state;

  return { queued, assigned };
}

const mapDispatchToProps = dispatch => {
  return {
    logout: () => dispatch(logout()),
    startPoll: timeout => dispatch(startPoll(timeout)),
    stopPoll: () => dispatch(stopPoll()),
  };
};

export default compose(
  withNamespaces('translations'),
  withRouter,
  connect(
    mapStateToProps,
    mapDispatchToProps
  )
)(Moderator);
