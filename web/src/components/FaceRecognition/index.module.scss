@import '../../styles/colors';
@import '../../styles/sizes';

.invisible {
  left: -10000px;
  position: absolute;
}

#face_recognition {
  margin: 10px;
  text-align: center;
  .hidden {
    display: none !important;
  }
  .person_contour {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    position: relative;
    width: 100%;
    height: 100%;
    bottom: 0;
    overflow: hidden;
    display: inline-block;
    box-sizing: unset;
    &::before {
      content: '';
      display: block;
      width: 30%;
      height: 0;
      padding-bottom: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      z-index: 2;
      transform: translate(-50%, -50%);
      bottom: 0;
      border: solid 3000px rgba(0, 0, 0, 0.7);
      border-radius: 50%;
    }
    .warning_text {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 2;
      p {
        color: $red;
        text-transform: uppercase;
        margin: 0;
        font-size: 18px;
        font-weight: bold;
        a {
          text-decoration: underline;
        }
      }
      span {
        color: $white;
        font-size: 13px;
      }
    }
  }

  .camera_resolution_error {
    color: $pink;
  }

  .instructions {
    margin: 0 auto;
    max-width: 889px;
    min-height: 80px;
    display: flex;
    flex-direction: row;

    .icon_space {
      background: $orange;
      width: 83px;
      min-height: 100%;
      border-top-left-radius: 8px;
      border-bottom-left-radius: 8px;
      color: white;
      display: flex;

      .info_icon {
        margin: auto;
        height: 45%;
      }
    }

    .info_space {
      background: $main;
      min-height: 100%;
      width: 806px;
      border-top-right-radius: 8px;
      border-bottom-right-radius: 8px;
      color: white;
      display: flex;
      font-size: 18px;
      justify-content: center;

      .info_header {
        width: 100%;
        padding: 10px;
        line-height: 23px;
        a {
          text-decoration: underline;
          color: #fff;
        }
      }
    }
  }

  .face_recognition_camera_container {
    position: relative;
    margin: 30px auto;
    width: 40%;

    video {
      display: block;
    }

    .face_recognition_person_layer {
      position: absolute;
      top: 0;
      bottom: 0px;
      left: 0;
      right: 0;
      background-repeat: no-repeat;
      background-position: center;

      img {
        height: 100%;
      }
    }

    .face_recognition_webcam {
      width: 100%;
    }

    .record_button_container {
      position: absolute;
      height: 50px;
      width: 100%;
      bottom: -50px;
      background: #0000004d;
      z-index: 1;

      img {
        height: 40px;
        z-index: 2;
        margin-top: 5px;
        cursor: pointer;
      }
    }

    &.with_margin {
      margin-top: 30px;
    }
  }

  .face_recognition_camera_container_hidden {
    position: absolute;
  }

  .error_message {
    margin-top: 90px;
    margin-bottom: 30px;

    :global(.server_error) {
      font-size: 14px;
      white-space: pre-wrap;
    }
  }

  .user_waiting_message {
    color: $pink;
    font-size: 14px;
  }

  .connection_error_message {
    color: $pink;
    font-size: 14px;
  }

  .face_recognition_capture {
    margin-bottom: 10px;
  }

  @media screen and (max-width: $tablet-width) {
    .face_recognition_camera_container {
      width: 60%;
      .person_contour {
        &::before {
          width: 70%;
          padding-top: 50%;
        }
      }
      .tablet_person_contour {
        &::before {
          width: 35%;
          padding-top: 2%;
        }
      }
    }
  }

  @media screen and (max-width: $lowres-tablet-width) {
    .instructions {
      width: 95%;
    }

    .face_recognition_camera_container {
      max-width: 600px;
      width: 100%;
      height: auto;
      .person_contour {
        &::before {
          width: 45%;
          padding-top: 18%;
        }
      }
      .tablet_person_contour {
        &::before {
          width: 35%;
          padding-top: 0%;
        }
      }
    }
  }

  @media screen and (max-width: $mobile-width) {
    .instructions {
      width: 100%;
      .info_space {
        font-size: 14px;
        .info_header {
          width: 100%;
          padding: 5px;
          line-height: 19px;
        }
      }

      .icon_space {
        .info_icon {
          margin: auto 5px;
        }
      }
    }

    .face_recognition_camera_container {
      width: 300px;
      height: auto;
      margin: 10px auto;
      .person_contour {
        &::before {
          width: 77%;
          padding-top: 50%;
        }
      }
    }
  }
}

#overlay {
  margin-top: 20px;
  background-color: white;

  .overlay_content {
    position: relative;
    width: 60%;
    margin: auto;

    .overlay_header {
      font-size: 32px;
      color: $main;
    }

    .change_browser_overlay_text {
      font-size: 16px;
      color: $main;
      line-height: 1.5;

      .change_browser_logo {
        width: 25px;
        margin: -7px 0px;
      }
    }

    .overlay_text {
      font-size: 16px;
      color: $main;
    }

    .instructions_container {
      position: relative;

      .invisible_loader {
        display: none;
      }
    }

    .video_loader {
      position: absolute;
      top: 20%;
      left: 40%;
    }

    .video_loader_change_browser {
      position: absolute;
      top: 40%;
      left: 40%;
    }

    .instructions_video {
      display: block;
      margin: 20px auto;
      width: 400px;
      height: 320px;
    }

    .instructions_image {
      display: block;
      margin: 20px auto;
      width: 400px;
    }

    .hide_button {
      margin-top: 10px;
      margin-bottom: 10px;
    }
  }

  @media screen and (max-width: $tablet-width) {
    .overlay_content {
      .video_loader {
        left: 32%;
      }

      .video_loader_change_browser {
        top: 40%;
        left: 32%;
      }
    }
  }

  @media screen and (max-width: $lowres-tablet-width) {
    .overlay_content {
      position: relative;
      width: 100%;

      .instructions_video {
        width: 290px;
      }

      .video_loader {
        left: 35%;
      }

      .video_loader_change_browser {
        top: 40%;
        left: 35%;
      }

      .instructions_image {
        width: 290px;
      }
    }
  }

  @media screen and (max-width: $mobile-width) {
    .overlay_content {
      .video_loader {
        left: 20%;
      }

      .video_loader_change_browser {
        top: 40%;
        left: 20%;
      }
    }
  }
}
