import React, { Component } from 'react';
import { Image, Modal, Checkbox } from 'semantic-ui-react';
import { withNamespaces, Trans } from 'react-i18next';
import { Formik, Form } from 'formik';

import GCButton from '../../GCButton';
import Validation from '../../Validation';
import warnModalSchema from '../../../validation/schemas/warnModalSchema';

import cash_me from '../../../svgs/cash_me.svg';
import warn from '../../../svgs/warn_modal.svg';

import classnames from 'classnames';

import styles from './index.module.scss';

class WarnModal extends Component {
  render() {
    const { open, t, closeModal } = this.props;

    return (
      <Modal
        centered={true}
        dimmer="inverted"
        id={styles.warn_modal}
        open={open}
        onClose={closeModal}
        closeOnDimmerClick={false}
      >
        <Image wrapped size="small" src={cash_me} className={styles.logo} />
        <Modal.Content image scrolling className={styles.content}>
          <Modal.Description className={styles.description}>
            <Formik
              initialValues={{
                confirmCheck: false,
              }}
              onSubmit={closeModal}
              validationSchema={warnModalSchema}
            >
              {props => {
                const { values } = props;

                return (
                  <div className={styles.main_text}>
                    <div>
                      <Image src={warn} className={styles.warn_icon} />
                      <div
                        className={classnames(
                          styles.warn_text,
                          styles.red_text
                        )}
                      >
                        {t('face_recognition.warn_modal.header')}
                      </div>
                      <Trans
                        defaults={`face_recognition.warn_modal.text1`}
                        components={[
                          <span className={styles.red_text}>text</span>,
                          <br />,
                        ]}
                      />
                    </div>
                    <Form>
                      <Validation name="confirmCheck" showMessage={false}>
                        <Checkbox
                          className={styles.confirm_text}
                          label={t(
                            'face_recognition.warn_modal.confirm_understand'
                          )}
                          checked={values.confirmCheck}
                        />
                      </Validation>
                      <div>
                        <GCButton
                          primary={true}
                          type="submit"
                          className={styles.confirm_button}
                        >
                          {t('face_recognition.capture')}
                        </GCButton>
                      </div>
                    </Form>
                  </div>
                );
              }}
            </Formik>
          </Modal.Description>
        </Modal.Content>
      </Modal>
    );
  }
}

export default withNamespaces('translations')(WarnModal);
