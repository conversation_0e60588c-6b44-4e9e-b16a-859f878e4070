import React, { Component } from 'react';
import { withNamespaces, Trans } from 'react-i18next';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import Webcam from 'react-webcam';
import querystring from 'query-string';
import classnames from 'classnames';
import {
  isAndroid,
  isIOS,
  isChrome,
  isFirefox,
  isSafari,
  isIE,
  isEdge,
  isMobileSafari,
  isMobile,
  browserName,
  isTablet,
} from 'react-device-detect';

import ScreenshotErrorModal from './ScreenshotErrorModal';
import WarnModal from './WarnModal';
import CashMeLoader from '../CashMeLoader';
import GCButton from '../GCButton';

import ServerError from '../Validation/ServerError';
import info_icon from '../../svgs/info-icon.svg';
import recordSvg from '../../svgs/record.svg';
import instructionsFace from '../../svgs/instructions_face.png';
import safariLogo from '../../svgs/safari_logo.svg';
import chromeLogo from '../../svgs/chrome_logo.svg';
import videoLoader from '../../svgs/video_loader.svg';

import allowAndroidChrome from '../../instructions/allow_android_chrome.mp4';
import allowIosSafari from '../../instructions/allow_ios_safari.mp4';
import allowChrome from '../../instructions/allow_chrome.mp4';
import allowFirefox from '../../instructions/allow_firefox.mp4';
import allowSafari from '../../instructions/allow_safari.mp4';
import allowEdge from '../../instructions/allow_edge.mp4';

import unblockIosSafari from '../../instructions/unblock_ios_safari.mp4';
import unblockAndroidChrome from '../../instructions/unblock_android_chrome.mp4';
import unblockChrome from '../../instructions/unblock_chrome.mp4';
import unblockFirefox from '../../instructions/unblock_firefox.mp4';
import unblockSafari from '../../instructions/unblock_safari.mp4';
import unblockEdge from '../../instructions/unblock_edge.mp4';
import changeIosBrowser from '../../instructions/change_ios_browser.mp4';
import changeAndroidBrowser from '../../instructions/change_android_browser.mp4';

import {
  CHECK_CAMERA_STATE_INTERVAL,
  FORBIDDEN_ERROR_CODE,
  UNSUPPORTED_BROWSERS,
  SAMSUNG_DEFAULT_BROWSER,
  WEBCAM_VIDEO_CONSTRAINTS,
  STREAM_RECORDING_DURATION,
  STREAM_RECORDING_UNMOUNT_DURATION,
  OK_STATUS_CODE,
} from '../../constants';

import styles from './index.module.scss';
import OpenTok from './OpenTok';
import { v4 as uuidv4 } from 'uuid';
import _ from 'lodash';
import { PHONE_NUMBER } from '../../config/config';
import Log from '../../logs/Log';
import {
  getFingerprintToken,
  isREML,
  setFingerprintToken,
} from '../../helpers/common';
import { storeEkengPhoto } from '../../redux/ducks/faceRecognition';

class FaceRecognition extends Component {
  constructor(props) {
    super(props);

    this.openTok = new OpenTok();

    this.state = {
      showOverlay: true,
      cameraEnabled: false,
      cameraBlocked: false,
      renderWebcam: true,
      checkStateInterval: null,
      cameraResolutionError: false,
      showContinueButton: false,
      continueButtonLoading: false,
      userWaitingMessage: false,
      videoConstraints: {
        ...WEBCAM_VIDEO_CONSTRAINTS,
        facingMode: this.getFacingMode(),
      },
      minScreenshotWidth: 0,
      minScreenshotHeight: 0,
      showScreenshotErrorModal: false,
      warnModal: false,
      screenshot: null,
    };
  }

  componentDidMount() {
    this.allowRecognition();
  }

  componentWillUnmount() {
    this.stopOpenTokStream(true);
  }

  componentDidUpdate(prevProps) {
    const { history, faceRecognition, location } = this.props;

    if (
      faceRecognition.status &&
      prevProps.faceRecognition.status !== this.props.faceRecognition.status &&
      faceRecognition.status === OK_STATUS_CODE
    ) {
      this.startOpenTokStream();
    }

    if (
      !faceRecognition.loading &&
      prevProps.faceRecognition.error !== this.props.faceRecognition.error &&
      faceRecognition.error
    ) {
      if (faceRecognition.error.status_code === FORBIDDEN_ERROR_CODE) {
        history.push({
          pathname: '/verification-failure',
        });
      }

      this.setState({
        continueButtonLoading: false,
        userWaitingMessage: false,
      });
    }

    if (
      faceRecognition.data &&
      prevProps.faceRecognition.data !== faceRecognition.data &&
      faceRecognition.data.isDetected &&
      !faceRecognition.loading &&
      !faceRecognition.error
    ) {
      const params = querystring.parse(location.search);

      const pathname = this.getRedirectPathname();

      history.push({
        pathname,
        search: querystring.stringify(params),
      });

      return;
    }

    if (faceRecognition.isOffline) {
      this.props.recognise({});
    }
  }

  getRedirectPathname() {
    let pathname = '/loan/loan-amount';

    // In real estate loan cases we should redirect users to the real estate details page in the beginning
    if (isREML()) {
      pathname = '/loan/real-estate-details';
    }

    return pathname;
  }

  capture = () => {
    const { video } = this.getTracks(this.webcamRef.stream);

    if (video) {
      const mediaStreamVideo = video[0];
      const { width, height } = this.getVideoResolution(mediaStreamVideo);

      const isReml = isREML();

      this.setState(
        {
          minScreenshotHeight: width,
          minScreenshotWidth: height,
          warnModal: !isReml,
        },
        () => {
          const screenshot = this.openTok.getScreenshot();

          if (!screenshot) {
            // it means we have got an error during the stream processing (e.g. session was destroyed, network error)
            // hence we need to reload the page
            return this.setState({
              showScreenshotErrorModal: true,
              warnModal: false,
            });
          }

          if (isReml) {
            this.props.recognise({
              citizen_image: screenshot,
            });
          }

          this.setState({ screenshot });
        }
      );
    }
  };

  getTracks(stream) {
    return _.groupBy(stream.getTracks(), 'kind');
  }

  getVideoResolution = video => {
    let width = null;
    let height = null;

    if (video && video.getSettings) {
      width = video.getSettings().width;
      height = video.getSettings().height;
    }

    return {
      width,
      height,
    };
  };

  getFacingMode() {
    return isMobile || isTablet ? { exact: 'user' } : 'environment';
  }

  hideOverlay = () => {
    this.setState(
      {
        continueButtonLoading: true,
        userWaitingMessage: true,
      },
      () => {
        this.props.storeEkengPhoto();
      }
    );
  };

  isLandScape = () => {
    const video = this.openTok.getVideo();

    if (video) {
      const { offsetWidth, offsetHeight } = video;

      return offsetWidth > offsetHeight;
    }
  };

  getAllowInstructions = () => {
    if (isAndroid) {
      return allowAndroidChrome;
    } else if (isChrome) {
      return allowChrome;
    } else if (isMobileSafari) {
      return allowIosSafari;
    } else if (isFirefox) {
      return allowFirefox;
    } else if (isSafari) {
      return allowSafari;
    } else if (isEdge) {
      return allowEdge;
    }
  };

  getUnblockInstructions = () => {
    if (isAndroid) {
      return unblockAndroidChrome;
    } else if (isMobileSafari) {
      return unblockIosSafari;
    } else if (isChrome) {
      return unblockChrome;
    } else if (isFirefox) {
      return unblockFirefox;
    } else if (isSafari) {
      return unblockSafari;
    } else if (isEdge) {
      return unblockEdge;
    }
  };

  getInstructionVideos = () => {
    if (this.state.cameraBlocked) {
      return this.getUnblockInstructions();
    }

    return this.getAllowInstructions();
  };

  noCameraText = () => {
    const { t } = this.props;

    if (isIOS && !isSafari) {
      return (
        <div className={styles.instructions_container}>
          <div className={styles.change_browser_overlay_text}>
            <Trans
              defaults={`face_recognition.change_browser_ios`}
              components={[
                <img
                  key="safariLogo"
                  alt="safariLogo"
                  src={safariLogo}
                  className={styles.change_browser_logo}
                />,
              ]}
            />
          </div>
          <video
            className={styles.instructions_video}
            src={changeIosBrowser}
            autoPlay
            loop
            muted
            playsInline
            onCanPlay={() => {
              this.setState({
                videoLoaded: true,
              });
            }}
            onLoadStart={() => {
              this.setState({
                videoLoaded: false,
              });
            }}
          />
          <img
            className={classnames({
              [styles.video_loader_change_browser]: true,
              [styles.invisible_loader]: this.state.videoLoaded,
            })}
            src={videoLoader}
            alt="videoLoader"
          />
        </div>
      );
    } else if (isIE) {
      return (
        <div className={styles.change_browser_overlay_text}>
          <Trans
            defaults={`face_recognition.change_browser_ie`}
            components={[
              <img
                key="chromeLogo"
                alt="chromeLogo"
                src={chromeLogo}
                className={styles.change_browser_logo}
              />,
            ]}
          />
        </div>
      );
    } else if (isAndroid) {
      if (
        UNSUPPORTED_BROWSERS.includes(browserName) ||
        (browserName === SAMSUNG_DEFAULT_BROWSER && this.state.cameraBlocked)
      ) {
        return (
          <div className={styles.instructions_container}>
            <div className={styles.change_browser_overlay_text}>
              <Trans
                defaults={`face_recognition.change_browser_ie`}
                components={[
                  <img
                    key="chromeLogo"
                    alt="chromeLogo"
                    src={chromeLogo}
                    className={styles.change_browser_logo}
                  />,
                ]}
              />
            </div>
            <video
              className={styles.instructions_video}
              src={changeAndroidBrowser}
              autoPlay
              loop
              muted
              playsInline
              onCanPlay={() => {
                this.setState({
                  videoLoaded: true,
                });
              }}
              onLoadStart={() => {
                this.setState({
                  videoLoaded: false,
                });
              }}
            />
            <img
              className={classnames({
                [styles.video_loader_change_browser]: true,
                [styles.invisible_loader]: this.state.videoLoaded,
              })}
              src={videoLoader}
              alt="videoLoader"
            />
          </div>
        );
      }
    }

    return (
      <div className={styles.instructions_container}>
        <div className={styles.overlay_text}>
          {t('face_recognition.turn_on_camera_text')}
        </div>
        <video
          ref={video => (this.video = video)}
          onCanPlay={() => {
            this.setState({
              videoLoaded: true,
            });
          }}
          onLoadStart={() => {
            this.setState({
              videoLoaded: false,
            });
          }}
          className={styles.instructions_video}
          src={this.getInstructionVideos()}
          autoPlay
          loop
          muted
          playsInline
        />
        <img
          className={classnames({
            [styles.video_loader]: true,
            [styles.invisible_loader]: this.state.videoLoaded,
          })}
          src={videoLoader}
          alt="videoLoader"
        />
      </div>
    );
  };

  checkCameraState = () => {
    const checkStateInterval = setInterval(async () => {
      const { facingMode } = this.state.videoConstraints;

      try {
        const resolution = await this.getWebCamResolution(facingMode);

        clearInterval(this.state.checkStateInterval);

        this.setState({
          videoConstraints: {
            width: resolution && resolution.width,
          },
          renderWebcam: true,
          checkStateInterval: null,
        });
      } catch (e) {
        // we bypass this exception as we wouldn’t like to log it.
      }
    }, CHECK_CAMERA_STATE_INTERVAL);

    this.setState({
      checkStateInterval,
    });
  };

  async startOpenTokStream() {
    try {
      Log.info('Start opentok streaming');

      this.openTok.init(this.webcamRef, this.state.videoConstraints);

      await this.openTok.startSession();

      await this.openTok.startRecording();

      Log.info('Opentok stream started');
    } catch (e) {
      Log.error({
        msg: 'Start stream failure',
        error: e,
      });

      if (e.name !== 'OT_AUTHENTICATION_ERROR') {
        // although we have got an auth error from tokbox we continue face recognition

        return this.setState({ showScreenshotErrorModal: true });
      }
    }

    this.setState({
      renderWebcam: true,
      showOverlay: false,
      continueButtonLoading: false,
      userWaitingMessage: false,
    });

    this.stopOpenTokStream();
  }

  async getWebCamResolution(facingMode) {
    const resolutions = {
      fullHdConstraints: {
        width: { min: 1600 },
        facingMode,
      },
      hdConstraints: {
        width: { min: 1280 },
        facingMode,
      },
      vgaConstraints: {
        width: { min: 640 },
        facingMode,
      },
      qvgaConstraints: {
        width: { min: 320 },
        facingMode,
      },
    };

    const data = Object.values(resolutions);

    for (const constraints of data) {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: constraints,
          audio: true,
        });

        if (stream) {
          const { video } = this.getTracks(stream);

          return video[0].getSettings();
        }
      } catch (e) {}
    }

    return Promise.reject();
  }

  stopOpenTokStream = (fromUnmount = false) => {
    const duration = fromUnmount
      ? STREAM_RECORDING_UNMOUNT_DURATION
      : STREAM_RECORDING_DURATION;

    setTimeout(() => {
      this.openTok.stopRecording(fromUnmount);
    }, duration);
  };

  closeWarnModal = () => {
    this.setState({
      warnModal: false,
    });

    this.props.recognise({
      citizen_image: this.state.screenshot,
    });
  };

  allowRecognition = async () => {
    const fingerprint = this.setFingerprint();

    this.props.allowRecognition(fingerprint);
  };

  setFingerprint = () => {
    let fingerprint = getFingerprintToken();

    if (!fingerprint) {
      fingerprint = uuidv4();
      setFingerprintToken(fingerprint);
    }

    return fingerprint;
  };

  render() {
    const { t, faceRecognition } = this.props;

    const {
      cameraEnabled,
      showOverlay,
      renderWebcam,
      cameraResolutionError,
      showContinueButton,
      continueButtonLoading,
      userWaitingMessage,
      showScreenshotErrorModal,
      warnModal,
    } = this.state;

    return (
      <div id={styles.face_recognition}>
        <CashMeLoader loading={faceRecognition && faceRecognition.loading} />

        <div
          id={styles.overlay}
          className={classnames({ [styles.hidden]: !showOverlay })}
        >
          <div className={styles.overlay_content}>
            <p className={styles.overlay_header}>
              {t('face_recognition.overlay_header')}
            </p>
            {!cameraEnabled ? (
              this.noCameraText()
            ) : (
              <div>
                <div className={styles.change_browser_overlay_text}>
                  <WarnModal
                    open={warnModal}
                    closeModal={this.closeWarnModal}
                  />
                  <Trans
                    defaults={`face_recognition.overlay_text`}
                    components={[
                      <img
                        key="recordSvg"
                        alt="recordSvg"
                        src={recordSvg}
                        className={styles.change_browser_logo}
                      />,
                    ]}
                  />
                </div>
                <img
                  className={styles.instructions_image}
                  src={instructionsFace}
                  alt="instructionsFace"
                />
                <div className={styles.user_waiting_message}>
                  {userWaitingMessage && (
                    <span>{t('face_recognition.waiting_message')}</span>
                  )}
                </div>
                {showContinueButton && (
                  <GCButton
                    loading={continueButtonLoading}
                    disabled={continueButtonLoading}
                    onClick={this.hideOverlay}
                    primary={true}
                    className={styles.hide_button}
                  >
                    {t('face_recognition.continue')}
                  </GCButton>
                )}
              </div>
            )}
          </div>
        </div>
        <div className={classnames({ [styles.invisible]: showOverlay })}>
          <div
            className={classnames(styles.instructions, {
              [styles.hidden]: isREML(),
            })}
          >
            <div className={styles.icon_space}>
              <img
                src={info_icon}
                className={styles.info_icon}
                alt="info_icon"
              />
            </div>
            <div className={styles.info_space}>
              <div className={styles.info_header}>
                {t(`face_recognition.instructions`)}
                <a href={'tel:' + PHONE_NUMBER}>{PHONE_NUMBER}</a>
              </div>
            </div>
          </div>
          <div
            className={classnames({
              [styles.face_recognition_camera_container_hidden]: showOverlay,
              [styles.face_recognition_camera_container]: true,
              [styles.with_margin]: isREML(),
            })}
          >
            <div className={styles.face_recognition_person_layer}>
              <div
                className={classnames({
                  [styles.person_contour]: true,
                  [styles.tablet_person_contour]:
                    (isTablet || isMobile) && this.isLandScape(),
                })}
              />
            </div>
            <div className={styles.record_button_container}>
              <div>
                <img onClick={this.capture} src={recordSvg} alt="record_logo" />
              </div>
            </div>
            {/*
              We use "webcam" to check the device web camera and to check the maximum resolution of the web camera the device supports
              TODO: we need to remove it
            */}
            {renderWebcam && (
              <Webcam
                ref={ref => (this.webcamRef = ref)}
                audio={true}
                className={styles.face_recognition_webcam}
                videoConstraints={this.state.videoConstraints}
                minScreenshotHeight={this.state.minScreenshotHeight}
                minScreenshotWidth={this.state.minScreenshotWidth}
                screenshotFormat="image/png"
                onUserMedia={() => {
                  this.setState({
                    cameraEnabled: true,
                    cameraBlocked: false,
                    showContinueButton: true,
                    checkStateInterval: null,
                  });
                }}
                onUserMediaError={e => {
                  this.setState(
                    {
                      cameraEnabled: false,
                      cameraBlocked: true,
                    },
                    this.checkCameraState(e)
                  );
                }}
              />
            )}
          </div>
        </div>
        <div className={styles.error_message}>
          <ServerError error={faceRecognition.error} />
          {cameraResolutionError && (
            <span className={styles.camera_resolution_error}>
              {t('face_recognition.camera_resolution_error')}
            </span>
          )}
        </div>
        <ScreenshotErrorModal open={showScreenshotErrorModal} />
      </div>
    );
  }
}

function mapStateToProps(state) {
  const { faceRecognition } = state;

  return { faceRecognition };
}

const mapDispatchToProps = dispatch => {
  return {
    storeEkengPhoto: () => dispatch(storeEkengPhoto()),
  };
};

export default compose(
  withRouter,
  withNamespaces('translations'),
  connect(
    mapStateToProps,
    mapDispatchToProps
  )
)(FaceRecognition);
