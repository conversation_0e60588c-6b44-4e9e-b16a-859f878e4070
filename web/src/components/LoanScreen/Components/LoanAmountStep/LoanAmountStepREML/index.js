import React, { Component } from 'react';
import { withNamespaces } from 'react-i18next';
import { Input } from 'semantic-ui-react';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import NumberFormat from 'react-number-format';
import { Form, Formik } from 'formik';
import querystring from 'query-string';
import classnames from 'classnames';
import moment from 'moment';
import { reduce, max } from 'lodash';

import { wizardLoanAmountREMLSchema } from '../../../../../validation/schemas/wizardLoanSchema';
import loanCalculatorREML from '../../../../../helpers/loanCalculatorREML';
import { setLoanType } from '../../../../../helpers/common';
import CitizenInfo from '../CitizenInfo';
import EstateInfo from './EstateInfo';
import LoanInfo from '../LoanInfo';
import Currency from '../../../../Currency';
import Slider from '../../../../Slider';
import GenericError from '../../../../GenericError';
import Validation from '../../../../Validation';
import {
  LOAN_TYPES,
  APR_DATE_FORMAT,
  FRACTION_NO,
  REML_BOUNDARY_VALUES,
} from '../../../../../constants';
import {
  roundUp,
  roundDown,
  round,
  percentage,
} from '../../../../../helpers/numberHelpers';
import CashMeLoader from '../../../../CashMeLoader';

import { approveLoan } from '../../../../../redux/ducks/loan';
import {
  fetchRealEstateDetails,
  fetchRealEstateDetailsMedia,
} from '../../../../../redux/ducks/realEstateDetails';
import { fetchCitizen } from '../../../../../redux/ducks/citizen';
import { fetchLoanConfigsForCitizen } from '../../../../../redux/ducks/loanConfigs';
import { withMortgageSolvency } from '../../../../HOC/withMortgageSolvency';

import { ReactComponent as HomeIcon } from '../../../../../svgs/home.svg';
import { ReactComponent as DramIcon } from '../../../../../svgs/amd.svg';

import styles from './index.module.scss';

class LoanAmountStepREML extends Component {
  constructor(props) {
    super(props);

    this.state = {
      loanDuration: 0,
      loanAmount: 0,
      creditOffer: {},
      creditOffers: {},
      prepayment: 0,
      minPrepaymentAmount: 0,
      maxPrepaymentAmount: 0,
      showLoanRejectMessage: false,
    };
  }

  componentDidMount() {
    const {
      fetchCitizen,
      fetchLoanConfigsForCitizen,
      fetchRealEstateDetails,
      fetchRealEstateDetailsMedia,
    } = this.props;

    fetchCitizen({
      loan_type_id: LOAN_TYPES.REML.id,
    });
    fetchRealEstateDetails();
    fetchRealEstateDetailsMedia();
    fetchLoanConfigsForCitizen(LOAN_TYPES.REML.id);
  }

  componentDidUpdate(prevProps, prevState) {
    const { citizen, loanConfigs } = this.props;
    const { loanDuration, loanAmount } = this.state;

    if (
      !this.isLoading() &&
      (prevProps.loanConfigs !== loanConfigs || prevProps.citizen !== citizen)
    ) {
      const {
        realEstateDetails: {
          data: { price },
        },
        citizen: {
          data: { credit: offers = [], acraLoansMonthlyRepayments, netSalary },
        },
        loanConfigs: {
          data: { annualRate },
        },
        calculateSolvencyHOC,
      } = this.props;

      const solvency = calculateSolvencyHOC(
        price,
        loanAmount,
        netSalary,
        acraLoansMonthlyRepayments
      );

      const creditOffers = offers.reduce((acc, m, i) => {
        const offer = Object.entries(m);
        const duration = offer[0][0];
        const credit = offer[0][1];

        const calculator = new loanCalculatorREML(
          credit.amount,
          annualRate,
          credit.serviceFeeRate,
          credit.duration
        );

        credit.appropriateAmount = calculator.calculateAppropriateAmount(
          solvency
        );
        acc[duration] = credit;

        return acc;
      }, {});

      const { gcm, gcp } = querystring.parse(this.props.location.search);

      const creditOffer = this.getCreditOffer(creditOffers, gcm);

      const minPrepaymentAmount = this.getMinPrepaymentAmount(creditOffer);

      const maxPrepaymentAmount = this.getMaxPrepaymentAmount();

      const prepayment = gcp || minPrepaymentAmount;

      this.setState(
        {
          creditOffer,
          creditOffers,
          prepayment,
          loanDuration: creditOffer.duration,
          loanAmount: price - prepayment,
          minPrepaymentAmount,
          maxPrepaymentAmount,
        },
        () => {
          this.checkRejection();
        }
      );
    } else if (
      (prevState.loanDuration && loanDuration !== prevState.loanDuration) ||
      (prevState.loanAmount && loanAmount !== prevState.loanAmount)
    ) {
      this.checkRejection();
    }
  }

  getMaxPrepaymentAmount = () => {
    const {
      realEstateDetails: {
        data: { price },
      },
      loanConfigs: {
        data: { maxPrepaymentPercentage },
      },
    } = this.props;

    const maxPrepaymentAmount = Math.min(
      price - REML_BOUNDARY_VALUES.MIN_LOAN_AMOUNT,
      percentage(maxPrepaymentPercentage, price)
    );

    return roundDown(maxPrepaymentAmount, 1000);
  };

  getMinPrepaymentAmount = creditOffer => {
    const {
      realEstateDetails: {
        data: { price },
      },
      loanConfigs: {
        data: { minPrepaymentPercentage },
      },
    } = this.props;

    const minPrepaymentAmount = percentage(minPrepaymentPercentage, price);

    const calculatedPrepayment =
      creditOffer.amount - creditOffer.appropriateAmount;

    const minPrepaymentAmountFinal =
      minPrepaymentAmount > calculatedPrepayment
        ? minPrepaymentAmount
        : calculatedPrepayment;

    if (
      price - minPrepaymentAmountFinal >
      REML_BOUNDARY_VALUES.MAX_LOAN_AMOUNT
    ) {
      return roundUp(price - REML_BOUNDARY_VALUES.MAX_LOAN_AMOUNT, 1000);
    }

    return roundUp(minPrepaymentAmountFinal, 1000);
  };

  checkRejection = () => {
    const showLoanRejectMessage = this.checkSolvency();

    if (showLoanRejectMessage) {
      window.scroll(0, 0);
    }

    this.setState({ showLoanRejectMessage });
  };

  checkSolvency = () => {
    const { loanAmount } = this.state;
    const {
      citizen: {
        data: { netSalary, acraLoansMonthlyRepayments },
      },
      realEstateDetails: {
        data: { price },
      },
      calculateSolvencyHOC,
    } = this.props;

    const solvency = calculateSolvencyHOC(
      price,
      loanAmount,
      netSalary,
      acraLoansMonthlyRepayments
    );
    const { monthlyPayment } = this.getScheduleSummary();

    return solvency < monthlyPayment;
  };

  getCreditOffer = (creditOffers, gcm) => {
    const {
      loanConfigs: {
        data: { initialTime },
      },
    } = this.props;
    const maxMonths = this.getMaxMonths(creditOffers);

    if (gcm) {
      return creditOffers[gcm];
    } else if (creditOffers[initialTime].length !== 0) {
      return creditOffers[initialTime];
    }

    return creditOffers[maxMonths];
  };

  isLoanUnavailable = () => {
    return false;
  };

  isLoading = () => {
    const {
      citizen: { loading },
      loanConfigs,
      realEstateDetails,
      loan,
    } = this.props;

    return (
      loading ||
      loan.loading ||
      loanConfigs.loading ||
      realEstateDetails.loading ||
      !loanConfigs.data
    );
  };

  getScheduleSummary = () => {
    const {
      loanDuration,
      loanAmount,
      creditOffer: { interestRate },
    } = this.state;
    const {
      loanConfigs: {
        data: { serviceFeeRate },
      },
    } = this.props;

    if (!loanAmount) {
      return {};
    }

    const calculator = new loanCalculatorREML(
      loanAmount,
      interestRate,
      serviceFeeRate,
      loanDuration
    );

    const scheduleSummary = calculator.scheduleSummary();

    return scheduleSummary;
  };

  getRates = () => {
    const {
      loanAmount,
      loanDuration,
      creditOffer: { interestRate },
    } = this.state;
    const {
      loanConfigs: {
        data: { withdrawalFeeRate, serviceFeeRate, withdrawalFeeMinAmount },
      },
    } = this.props;

    const calculator = new loanCalculatorREML(
      loanAmount,
      interestRate,
      serviceFeeRate,
      loanDuration
    );

    const withdrawalFeeAmount = calculator.calculateWithdrawalFeeAmount(
      loanAmount,
      withdrawalFeeRate,
      withdrawalFeeMinAmount
    );

    const apr = calculator.calculateAPR(
      moment().format(APR_DATE_FORMAT),
      withdrawalFeeAmount
    );

    return {
      interestRate,
      apr,
      withdrawalFeeAmount,
    };
  };

  onUpdateLoanDuration = (years, setFieldValue) => {
    const { creditOffers } = this.state;
    const {
      realEstateDetails: {
        data: { price },
      },
    } = this.props;
    const loanDuration = years * 12;
    const creditOffer = creditOffers[loanDuration];

    const minPrepaymentAmount = this.getMinPrepaymentAmount(creditOffer);
    const prepayment = Math.max(minPrepaymentAmount, this.state.prepayment);

    setFieldValue('loanDuration', loanDuration / 12);
    setFieldValue('prepayment', prepayment);

    this.setState({
      loanDuration,
      prepayment,
      loanAmount: price - prepayment,
      minPrepaymentAmount,
    });
  };

  composePrepaymentAmount = amount => {
    const { minPrepaymentAmount, maxPrepaymentAmount } = this.state;

    if (!amount) {
      amount = minPrepaymentAmount;
    }

    amount = round(amount, 1000);

    if (amount < minPrepaymentAmount) {
      return minPrepaymentAmount;
    } else if (amount > maxPrepaymentAmount) {
      return maxPrepaymentAmount;
    }

    return amount;
  };

  onBlurPrepayment = (setFieldValue, value) => {
    const {
      realEstateDetails: {
        data: { price },
      },
    } = this.props;

    const prepayment = this.composePrepaymentAmount(value);

    setFieldValue('prepayment', prepayment);
    this.setState({
      prepayment,
      loanAmount: price - prepayment,
    });
  };

  getMaxMonths = creditOffers => {
    let maxMonths = 0;
    let availableMonths = reduce(
      creditOffers,
      function(result, value, key) {
        if (value.length !== 0) {
          result.push(+key);
        }

        return result;
      },
      []
    );

    if (availableMonths.length !== 0) {
      maxMonths = max(availableMonths);
    }

    return maxMonths;
  };

  getMinMonths = creditOffers => {
    const {
      loanConfigs: {
        data: { maxMonths },
      },
    } = this.props;
    let minMonths = maxMonths;

    for (const duration in creditOffers) {
      if (creditOffers[duration].length !== 0 && duration < minMonths) {
        minMonths = duration;
      }
    }

    return minMonths;
  };

  getMinMaxMonths = () => {
    if (!this.isLoading()) {
      const { creditOffers } = this.state;
      const maxMonths = this.getMaxMonths(creditOffers);
      const minMonths = this.getMinMonths(creditOffers);

      return { minMonths, maxMonths };
    }

    return { minMonths: 0, maxMonths: 0 };
  };

  handleSubmit = values => {
    const {
      approveLoan,
      wizardBag: { goNext },
      history,
    } = this.props;
    const { loanAmount, prepayment } = this.state;

    if (this.checkSolvency()) {
      return;
    }

    const {
      total,
      months,
      monthlyPayment,
      lastMonthPayment,
    } = this.getScheduleSummary();

    const loanTypeId = LOAN_TYPES.REML.id;

    approveLoan(
      {
        prepayment,
        amount: loanAmount,
        monthlyPayment,
        total,
        months,
        lastMonthPayment,
        loanTypeId,
        submittedApr: values.apr,
      },
      () => {
        setLoanType(loanTypeId);
        history.push({
          pathname: '/',
        });
        goNext({
          gcp: parseInt(prepayment, 10),
          gcm: parseInt(months, 10),
        });
      }
    );
  };

  handleClose = () => {
    this.setState({
      showLoanRejectMessage: false,
    });
  };

  showLoanRejectError = () => {
    const { showLoanRejectMessage } = this.state;

    return showLoanRejectMessage;
  };

  findLoanName = () => {
    if (this.isLoading()) {
      return '';
    }

    const {
      citizen: {
        data: { loanSubtypeId },
      },
    } = this.props;

    const loanSubType = Object.values(LOAN_TYPES.REML).find(
      subType => subType.id === loanSubtypeId
    );

    return loanSubType.name;
  };

  getInitialValues = loanName => {
    const { loanDuration, prepayment, creditOffer } = this.state;
    const {
      loanConfigs: { data: loanConfigs = {} },
    } = this.props;

    const calculatedMinPrepayment =
      creditOffer.amount - creditOffer.appropriateAmount;
    const values = {
      isHuman: false,
      aprEnabled: true,
      apr: '',
      actualApr: '',
      prepayment:
        prepayment > calculatedMinPrepayment
          ? prepayment
          : calculatedMinPrepayment,
      loanDuration: (loanDuration || loanConfigs.initialTime) / 12,
    };

    values[`${loanName}AprApprovalCheckbox`] = false;

    return values;
  };

  getLoanInfo = formik => {
    const { loanAmount } = this.state;

    const {
      wizardBag: { step },
      citizen: { data: citizen = {} },
    } = this.props;

    const payment = this.getScheduleSummary();
    const { apr, interestRate, withdrawalFeeAmount } = this.getRates();

    const { setFieldValue } = formik;

    const loanName = this.findLoanName();

    return (
      <LoanInfo
        apr={apr}
        step={step}
        amount={loanAmount}
        citizen={citizen}
        payment={payment}
        loanName={loanName}
        monthlyPayment={payment.monthlyPayment}
        isLoanUnavailable={this.isLoanUnavailable}
        serviceFeeRate={interestRate}
        withdrawalFeeAmount={withdrawalFeeAmount}
        isLoading={this.isLoading}
        setFieldValue={setFieldValue}
      />
    );
  };

  render() {
    const {
      citizen: { data: citizen = {} },
      loanConfigs: { data: loanConfigs = {} },
      realEstateDetails: { data: realEstateDetails = {}, media },
      history,
      t,
    } = this.props;
    const { loanAmount, loanDuration } = this.state;
    const { minMonths, maxMonths } = this.getMinMaxMonths();
    const loanName = this.findLoanName();
    const composedRealEstateInfo = {
      realEstateDetails,
      media,
    };

    return (
      <div id={styles.loan_amount_form}>
        <CashMeLoader loading={this.isLoading() || !loanDuration} />

        {!this.isLoading() &&
          loanDuration && (
            <>
              <GenericError
                errorMsg={t(
                  'loan.steps.real_estate_loan.prepayment_rejection_message'
                )}
                onClose={this.handleClose}
                showError={this.showLoanRejectError()}
              />
              <CitizenInfo citizen={citizen} loading={this.isLoading()} />
              <Formik
                initialValues={this.getInitialValues(loanName)}
                onSubmit={this.handleSubmit}
                validationSchema={() =>
                  wizardLoanAmountREMLSchema(`${loanName}AprApprovalCheckbox`)
                }
                validateOnBlur={true}
                validateOnChange={false}
              >
                {formik => {
                  const { values, setFieldValue } = formik;

                  return (
                    <Form id={styles.real_estate}>
                      <div
                        className={classnames(
                          'ui form',
                          styles.real_estate_form
                        )}
                      >
                        <div className={classnames(styles.section_border_top)}>
                          {
                            <div>
                              <div className={styles.loan_amount_title}>
                                <HomeIcon />
                                <div>
                                  {t(
                                    'loan.steps.real_estate_loan.loan_amount_title'
                                  )}
                                </div>
                              </div>
                            </div>
                          }
                        </div>
                        <EstateInfo
                          composedRealEstateInfo={composedRealEstateInfo}
                          history={history}
                        />

                        <div
                          className={classnames('field', styles.loan_amount)}
                        >
                          <label>
                            <span>
                              {t(
                                'loan.steps.loan_amount.real_estate_info.loan_amount'
                              )}
                            </span>
                          </label>

                          <div className={styles.loan_amount_input}>
                            <Currency
                              hasSign={true}
                              value={loanAmount}
                              precision={FRACTION_NO}
                            />
                          </div>
                        </div>

                        <div
                          className={classnames(
                            'field',
                            styles.prepayment_price
                          )}
                        >
                          <label htmlFor="prepayment">
                            <span className={styles.prepayment_price_title}>
                              {t(
                                'loan.steps.loan_amount.real_estate_info.prepayment'
                              )}
                            </span>
                          </label>
                          <Validation name="prepayment" showMessage={false}>
                            <div className={styles.prepayment_price_amount}>
                              <NumberFormat
                                customInput={Input}
                                onValueChange={e => {
                                  setFieldValue('prepayment', e.floatValue);
                                }}
                                onBlur={e => {
                                  this.onBlurPrepayment(
                                    setFieldValue,
                                    values.prepayment
                                  );
                                }}
                                value={values.prepayment}
                                placeholder="25,000,000"
                                autoComplete="off"
                                type="tel"
                                name="prepayment"
                                allowNegative={false}
                                thousandSeparator={true}
                                maxLength={12}
                                label={{
                                  basic: true,
                                  content: <DramIcon />,
                                }}
                              />
                            </div>
                          </Validation>
                        </div>

                        <div className={styles.time_slider}>
                          <div className={styles.time_slider_title}>
                            <span>
                              {t(
                                'loan.steps.loan_amount.real_estate_info.choose_loan_time'
                              )}
                            </span>
                          </div>

                          <Slider
                            onUpdate={value =>
                              this.onUpdateLoanDuration(value, setFieldValue)
                            }
                            min={minMonths / 12}
                            max={maxMonths / 12}
                            step={loanConfigs.timeStep / 12}
                            initialValue={values.loanDuration}
                            showTicks={true}
                          />
                        </div>

                        {this.getLoanInfo(formik)}
                      </div>
                    </Form>
                  );
                }}
              </Formik>
            </>
          )}
      </div>
    );
  }
}

function mapStateToProps(state) {
  const { citizen, loanConfigs, realEstateDetails, loan } = state;

  return { citizen, loanConfigs, realEstateDetails, loan };
}

const mapDispatchToProps = dispatch => {
  return {
    fetchCitizen: loanTypeId => dispatch(fetchCitizen(loanTypeId)),
    fetchRealEstateDetails: () => dispatch(fetchRealEstateDetails()),
    fetchRealEstateDetailsMedia: () => dispatch(fetchRealEstateDetailsMedia()),
    fetchLoanConfigsForCitizen: loanTypeId =>
      dispatch(fetchLoanConfigsForCitizen(loanTypeId)),
    approveLoan: (data, cb) => dispatch(approveLoan(data, cb)),
  };
};

export default compose(
  withRouter,
  withNamespaces('translations'),
  withMortgageSolvency,
  connect(
    mapStateToProps,
    mapDispatchToProps
  )
)(LoanAmountStepREML);
