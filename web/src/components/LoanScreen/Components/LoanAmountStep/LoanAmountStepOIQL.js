import React, { Component } from 'react';
import { connect } from 'react-redux';
import { withNamespaces } from 'react-i18next';
import { withRouter } from 'react-router-dom';
import { compose } from 'redux';
import { Formik, Form } from 'formik';
import moment from 'moment';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import querystring from 'query-string';

import LoanCalculatorOIQL from '../../../../helpers/loanCalculatorOIQL';
import GCButton from '../../../GCButton';
import Navigation from '../../../CreditWizard/Navigation';
import LoanRejectionText from '../LoanRejectionText';
import AprApproval from '../../../AprApproval';
import { wizardLoanAmountSchema } from '../../../../validation/schemas/wizardLoanSchema';
import RoundedAvatar from '../../../RoundedAvatar';
import { APR_DATE_FORMAT, BIRTH_DATE_FORMAT } from '../../../../constants';
import CashMeLoader from '../../../CashMeLoader';

import { fetchCitizen } from '../../../../redux/ducks/citizen';
import { fetchLoanConfigsForCitizen } from '../../../../redux/ducks/loanConfigs';
import { approveLoan } from '../../../../redux/ducks/loan';
import { fetchPipeTypes } from '../../../../redux/ducks/pipes';

import styles from './index.module.scss';

class LoanAmountStepOIQL extends Component {
  constructor(props) {
    super(props);

    this.state = {
      citizen: {},
    };
  }

  componentDidMount() {
    window.scroll(0, 0);

    this.props.fetchCitizen();
    this.props.fetchLoanConfigsForCitizen();
    this.props.fetchPipeTypes();
  }

  componentDidUpdate(prevProps) {
    const { citizen } = this.props;

    if (prevProps.citizen !== citizen) {
      this.setState({
        citizen: citizen.data || {},
      });
    }
  }

  getScheduleSummary(
    amount,
    monthlyPayment,
    interestRate,
    serviceFeeRate,
    duration
  ) {
    const calculator = new LoanCalculatorOIQL(
      amount,
      interestRate,
      serviceFeeRate,
      duration
    );
    return calculator.scheduleSummary(monthlyPayment);
  }

  handleSubmit = values => {
    const {
      approveLoan,
      wizardBag: { goNext },
      loanConfigs: {
        data: { serviceFeeRate },
      },
      pipe,
    } = this.props;

    const {
      citizen: {
        credit: { interestRate, duration },
      },
    } = this.state;

    const { price: amount, monthlyPayment } = pipe;

    const { total, months, lastMonthPayment } = this.getScheduleSummary(
      amount,
      monthlyPayment,
      interestRate,
      serviceFeeRate,
      duration
    );

    approveLoan(
      {
        amount,
        monthlyPayment,
        total,
        months,
        lastMonthPayment,
        pipe_type_id: pipe.id,
        submittedApr: values.apr,
      },
      () => {
        goNext({ pid: pipe.id });
      }
    );
  };

  fullName = () => {
    return `${this.state.citizen.firstName} ${this.state.citizen.lastName}`
      .trim()
      .toLowerCase();
  };

  pipeName = p => {
    if (!p) {
      return '';
    }
    const category = p.category ? ` Category ${p.category}` : '';

    return `${p.name}${category}`;
  };

  isLoanUnavailable() {
    const { citizen } = this.state;

    return (
      citizen.credit && (!citizen.credit.amount || !citizen.credit.duration)
    );
  }

  extractPassportNumber = passports => {
    if (passports) {
      const p =
        passports &&
        (passports.BIOMETRIC_PASSPORT ||
          passports.NON_BIOMETRIC_PASSPORT ||
          passports.ID_CARD);
      return p.passportNumber;
    }
  };

  isLoading() {
    const {
      citizen: { loading },
      loan,
      loanConfigs,
    } = this.props;

    return loading || loan.loading || loanConfigs.loading;
  }

  goHome = () => {
    const { history } = this.props;

    history.push({
      pathname: '/',
    });
  };

  getApr = () => {
    const { loanConfigs, pipe } = this.props;

    const { citizen } = this.state;

    if (!loanConfigs.data || !pipe || !citizen) {
      return null;
    }

    const interestRate =
      citizen && citizen.credit && citizen.credit.interestRate;
    const duration = citizen && citizen.credit && citizen.credit.duration;

    const config = loanConfigs && loanConfigs.data;
    const serviceFeeRate = config && config.serviceFeeRate;

    const { price: amount, monthlyPayment } = pipe;

    const { months } = this.getScheduleSummary(
      amount,
      monthlyPayment,
      interestRate,
      serviceFeeRate,
      duration
    );

    const calculator = new LoanCalculatorOIQL(
      amount,
      interestRate,
      serviceFeeRate,
      months
    );
    return calculator.calculateAPR(
      monthlyPayment,
      moment().format(APR_DATE_FORMAT)
    );
  };

  render() {
    const {
      t,
      wizardBag: { step },
      citizen: { loading },
      pipe,
    } = this.props;
    const { citizen } = this.state;

    const fullNameClasses = classnames(styles.user_name, {
      [styles.user_name_hide]: this.isLoading(),
    });

    const apr = this.getApr();

    return (
      <Formik
        initialValues={{
          isHuman: true,
          aprApprovalCheckbox: false,
          aprEnabled: !!apr,
          apr: '',
          actualApr: '',
        }}
        enableReinitialize
        onSubmit={this.handleSubmit}
        validationSchema={wizardLoanAmountSchema}
        validateOnBlur={true}
        validateOnChange={false}
      >
        {props => {
          return (
            <Form id={styles.loan_amount_form}>
              <CashMeLoader loading={this.isLoading()} />

              <div className={styles.user_info}>
                <RoundedAvatar
                  loading={loading}
                  photo={citizen.photo}
                  firstName={citizen.firstName}
                />
                <div className={fullNameClasses}>
                  <span className={fullNameClasses}>{this.fullName()}</span>
                </div>
                <div className={styles.user_datas}>
                  <div className={styles.user_data_row}>
                    <span className={styles.user_data}>
                      {t('loan.steps.loan_amount.passport')}
                    </span>
                    <span className={styles.user_data}>
                      {this.extractPassportNumber(citizen.passports)}
                    </span>
                  </div>
                  <div className={styles.user_data_row}>
                    <span className={styles.user_data}>
                      {t('loan.steps.loan_amount.born')}
                    </span>
                    <span className={styles.user_data}>
                      {moment(citizen.birthDate).format(BIRTH_DATE_FORMAT)}
                    </span>
                  </div>
                  <div className={styles.user_data_row}>
                    <span className={styles.user_data}>
                      {t('loan.steps.loan_amount.social_card')}
                    </span>
                    <span className={styles.user_data}>
                      {citizen.passports &&
                        citizen.passports.SOC_CARD.passportNumber}
                    </span>
                  </div>
                  <div className={styles.user_data_row}>
                    <span className={styles.user_data}>
                      {t('loan.steps.loan_amount.pipe_model')}
                    </span>
                    <span className={styles.user_data}>
                      {this.pipeName(pipe)}
                    </span>
                  </div>
                  <div className={styles.user_data_row}>
                    <span className={styles.user_data}>
                      {t('loan.steps.loan_amount.apr_rate')}
                    </span>
                    <span className={styles.user_data}>{apr}%</span>
                  </div>
                </div>
              </div>

              {this.isLoanUnavailable() ? (
                <LoanRejectionText />
              ) : (
                !!apr && <AprApproval apr={apr} />
              )}

              <Navigation>
                {this.isLoanUnavailable() ? (
                  <GCButton
                    type="button"
                    onClick={this.goHome}
                    className={styles.previous_step_button}
                  >
                    {t(step.previous)}
                  </GCButton>
                ) : (
                  <GCButton type="submit" primary={true} arrowIcon={true}>
                    {t(step.next)}
                  </GCButton>
                )}
              </Navigation>
            </Form>
          );
        }}
      </Formik>
    );
  }
}

LoanAmountStepOIQL.propTypes = {
  wizardBag: PropTypes.object,
};

function mapStateToProps(state, ownProps) {
  const { citizen, loanConfigs, loan, pipes: { data = [] } = {} } = state;
  const { pid } = querystring.parse(ownProps.location.search);
  const pipe = data.find(p => {
    return p.id === parseInt(pid, 10);
  });

  return { citizen, loanConfigs, loan, pipe };
}

const mapDispatchToProps = dispatch => {
  return {
    fetchCitizen: () => dispatch(fetchCitizen()),
    fetchLoanConfigsForCitizen: () => dispatch(fetchLoanConfigsForCitizen()),
    approveLoan: (data, cb) => dispatch(approveLoan(data, cb)),
    fetchPipeTypes: () => dispatch(fetchPipeTypes()),
  };
};

export default compose(
  withRouter,
  withNamespaces('translations'),
  connect(
    mapStateToProps,
    mapDispatchToProps
  )
)(LoanAmountStepOIQL);
