@import '../../../../../styles/colors';
@import '../../../../../styles/sizes';

#vehicle_info_modal {
  width: 40%;
  color: $wet-asphalt;
  padding: 30px 15px;
  margin-top: 10%;

  .logo {
    width: 116px;
    position: absolute;
    left: 30px;
    top: 30px;
  }

  .cancel {
    position: absolute;
    cursor: pointer;
    right: 30px;
    width: 20px;
    height: 20px;
    margin: 0;
  }

  .vehicle_info_title {
    font-size: 19px;
    color: $black;
    padding-bottom: 10px;
  }

  .modal_container {
    width: 100%;
  }

  .vehicle_container {
    max-width: 100%;
    margin: 40px auto 0;
    text-align: left;
    background-color: $white;

    .vehicle_info_error {
      padding-bottom: 12px;
      height: 30px;

      :global(.server_error) {
        margin: 0;
      }
    }

    :global .vehicle_input_container {
      border: 1px solid #dedede;
      border-radius: 6px;
      padding: 10px 0;
    }

    .vehicle_text {
      font-size: 11px;
      font-family: 'DejaVu Sans Book';
      font-style: oblique;
      color: $secondary;
      padding-bottom: 10px;
      padding-top: 3px;
    }

    .trade_amount_container {
      margin: 0 auto;

      .currency {
        display: flex;
        flex-direction: column;

        input {
          width: 220px;
          font-size: 24px;
          font-weight: bold;
          height: 36px;
          border: none !important;
          font-family: 'DejaVu Sans Book';
          padding-left: 30px;
          padding-right: 5px;
        }

        input:focus {
          outline: none;
          border: none;
        }

        .dram_icon {
          width: 20px;
          height: 23px;
          fill: $main;
          margin-top: 3px;
          margin-left: 5px;
        }

        .currency_input {
          width: 265px;

          div:first-child {
            width: 45px;
            padding: 0 12px 0 0;
            border: none;
            position: relative;
          }
        }

        .vehicle_number_input_label {
          display: inline-flex;
          margin-left: 12px;
          height: 36px;

          .label_container {
            padding-top: 2.5px;
            padding-bottom: 7.5px;
          }

          .vehicle_number_input_label_flag {
            margin: 5px 0 0 0;
          }

          .vehicle_number_input_label_text {
            margin-top: 3px;
          }

          .vehicle_input_label_seperator {
            margin-top: 3px;
            height: 30px;
            margin-left: 8px;
            border-left: 2px solid $light-gray;
          }
        }

        .currency_text {
          display: block;
          font-size: 12px;
          font-style: italic;
          margin-left: 10px;
        }
      }
    }

    .vehicle_amount_section {
      width: 100%;
      padding: 20px 0 13px 17px;
      background-color: $main;
    }
  }

  .vehicle_info_button {
    display: flex;
    justify-content: center;

    button {
      background-color: $main;
    }
  }

  @media screen and (max-width: $laptop-width) {
    margin-top: 20%;
    width: 50%;
  }

  @media screen and (max-width: $mobile-width) {
    width: 100%;
    margin-top: 0px;
    top: 0;
    height: 100vh;
    overflow: hidden;
    padding-bottom: 0;

    .modal_container {
      height: 100vh;
      min-height: unset;
    }
  }
}
