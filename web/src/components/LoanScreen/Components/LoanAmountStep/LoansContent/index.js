import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withNamespaces } from 'react-i18next';
import LoanCalculator from '../../../../../helpers/loanCalculator';
import Slider from '../../../../Slider';
import LoansAmount from '../../../../LoansAmount';
import { Formik, Form } from 'formik';
import querystring from 'query-string';
import moment from 'moment';

import { wizardLoanAmountSchema } from '../../../../../validation/schemas/wizardLoanSchema';
import { getLoanType, setLoanType, isOBL } from '../../../../../helpers/common';
import { handlePreventEnterKey } from '../../../../../helpers/input';
import {
  OCL_AMOUNT_LIMIT,
  MONTHLY_REPAYMENT_LIMIT,
  APR_DATE_FORMAT,
  LOAN_TYPES,
} from '../../../../../constants';
import VehicleInfo from '../VehicleInfo';
import LoanInfo from '../LoanInfo';

import { ReactComponent as CurrencyIcon } from '../../../../../svgs/currency-in-hands.svg';
import { ReactComponent as CarIcon } from '../../../../../svgs/car-in-hands.svg';
import { ReactComponent as CheckIcon } from '../../../../../svgs/check-main.svg';

import styles from './index.module.scss';

const MONTHLY_PAYMENT_STEP = 1000;

class LoansContent extends Component {
  constructor(props) {
    super(props);

    this.state = {
      currentOvl: null,
      amount: 0,
      monthlyPayment: 0,
      touchedCount: -1,
      initialMonthlyPayment: 0,
    };
  }

  componentDidMount() {
    const {
      citizen: { data: citizen = {} },
      loan,
      changeMaxAvailableAmount,
    } = this.props;
    let { currentOvl, amount } = this.state;

    if (loan.key === LOAN_TYPES.OCL.name) {
      amount = +citizen.credit.ocl.amount;
    }

    if (loan.key === LOAN_TYPES.OVL.name) {
      if (citizen.credit.ovl) {
        const { gcvn } = querystring.parse(window.location.search);
        currentOvl = citizen.credit.ovl.find(
          c =>
            gcvn
              ? c.vehicleInfo && c.vehicleInfo.number === gcvn
              : c.rejected === false
        );
      }

      amount = currentOvl ? +currentOvl.amount : 0;
    }

    if (loan.key === LOAN_TYPES.OBL.name && citizen.credit.obl) {
      this.setState({
        amount: citizen.credit.obl.amount,
      });
    }

    if (amount) {
      changeMaxAvailableAmount(loan.key, amount);
    }

    if (!isOBL()) {
      const [monthlyMin, monthlyMax] = this.countMonthlyMinMax(amount);

      const monthlyPayment = this.countInitialMonthlyPayment(
        monthlyMin,
        monthlyMax
      );
      const {
        selectedAmount,
        selectedMonthlyPayment,
      } = this.getSelectedAmountAndMonthlyPayment();

      this.setState({
        currentOvl,
        amount: selectedAmount || amount,
        monthlyPayment,
        initialMonthlyPayment: selectedMonthlyPayment,
      });
    }
  }

  changeCurrentOvl = currentOvl => {
    const { changeMaxAvailableAmount } = this.props;

    this.setState({
      currentOvl,
      amount: currentOvl.amount,
    });

    changeMaxAvailableAmount(LOAN_TYPES.OVL.name, +currentOvl.amount);
  };

  getLoanType = () => {
    const { loan } = this.props;

    return LOAN_TYPES[loan.key.toUpperCase()].id;
  };

  updateActualApr = setFieldValue => {
    const { apr } = this.getRates();

    setFieldValue('actualApr', apr);
  };

  isLoading = () => {
    const {
      citizen: { loading },
      loan,
      loanConfigs,
    } = this.props;

    return loading || loan.loading || loanConfigs.loading;
  };

  handleRadioChange = value => {
    const {
      citizen: { data: citizen = { credit: {} } },
      changeMaxAvailableAmount,
    } = this.props;

    const currentOvl = citizen.credit.ovl.find(
      c => c.vehicleInfo && c.vehicleInfo.number === value
    );

    this.setState({
      currentOvl,
    });
    changeMaxAvailableAmount(LOAN_TYPES.OVL.name, +currentOvl.amount);
  };

  getRates = () => {
    const { amount, monthlyPayment } = this.state;
    const maxMonths = this.getMaxMonths();
    const interestRate = this.getInterestRate();
    if (!interestRate) {
      return {};
    }

    const serviceFeeRate = this.getServiceFeeRate();
    const withdrawalFeeRate = this.getWithdrawalFeeRate();

    const calculator = new LoanCalculator(amount, interestRate, maxMonths);
    const withdrawalFeeAmount = calculator.calculateWithdrawalFeeAmount(
      amount,
      withdrawalFeeRate
    );

    const apr = calculator.calculateAPR(
      monthlyPayment,
      moment().format(APR_DATE_FORMAT),
      withdrawalFeeAmount
    );

    return {
      serviceFeeRate,
      apr,
      withdrawalFeeAmount,
    };
  };

  getInterestRate = () => {
    const {
      citizen: { data: citizen = { credit: {} } },
      loan,
    } = this.props;

    switch (loan.key) {
      case LOAN_TYPES.OCL.name:
        return citizen.credit.ocl.interestRate;
      case LOAN_TYPES.OVL.name:
        const { currentOvl } = this.state;
        return currentOvl ? currentOvl.interestRate : 0;
      default:
        return 0;
    }
  };

  getServiceFeeRate = () => {
    const {
      citizen: { data: citizen = { credit: {} } },
      loanConfigs: { data: config = {} },
      loan,
    } = this.props;

    switch (loan.key) {
      case LOAN_TYPES.OCL.name:
        return (
          Math.round(
            (citizen.credit.ocl.interestRate -
              citizen.credit.ocl.serviceFeeRate) *
              100
          ) / 100
        );
      case LOAN_TYPES.OVL.name:
        return +config.ovl.serviceFeeRate - citizen.qrDiscount;
      default:
        return 0;
    }
  };

  getWithdrawalFeeRate = () => {
    const {
      loanConfigs: { data: config = {} },
      loan,
    } = this.props;

    switch (loan.key) {
      case LOAN_TYPES.OCL.name:
        return +config.ocl.withdrawalFeeRate;
      case LOAN_TYPES.OVL.name:
        return +config.ovl.withdrawalFeeRate;
      default:
        return 0;
    }
  };

  getMaxMonths = () => {
    const {
      loanConfigs: { data: config = {} },
      loan,
      citizen: { data: citizen = { credit: {} } },
    } = this.props;

    switch (loan.key) {
      case LOAN_TYPES.OCL.name:
        return +citizen.credit.ocl.duration || config.ocl.maxMonths;
      case LOAN_TYPES.OVL.name:
        const { currentOvl } = this.state;
        return currentOvl ? +currentOvl.duration : config.ovl.maxMonths;
      default:
        return config[loan.key].maxMonths;
    }
  };

  getMinMonths = () => {
    const {
      loanConfigs: { data: config = {} },
      loan,
    } = this.props;
    switch (loan.key) {
      case LOAN_TYPES.OCL.name:
        return +config.ocl.minMonths;
      case LOAN_TYPES.OVL.name:
        return +config.ovl.minMonths;
      default:
        return +config[loan.key].minMonths;
    }
  };

  getMinAmount = () => {
    const {
      loanConfigs: { data: config = {} },
      loan,
    } = this.props;

    return +config[loan.key].minAmount;
  };

  getMaxAvailableAmount = () => {
    const {
      citizen: { data: citizen = { credit: {} } },
      loan,
    } = this.props;

    switch (loan.key) {
      case LOAN_TYPES.OCL.name:
        return +citizen.credit.ocl.amount;
      case LOAN_TYPES.OBL.name:
        return +citizen.credit.obl.amount;
      case LOAN_TYPES.OVL.name:
        const { currentOvl } = this.state;
        return currentOvl ? +currentOvl.amount : 0;
      default:
        return 0;
    }
  };

  handleSubmit = values => {
    const {
      approveLoan,
      wizardBag: { goNext },
      history,
    } = this.props;
    const { amount } = this.state;

    const loanTypeId = this.getLoanType();

    if (isOBL()) {
      approveLoan(
        {
          amount,
          loanTypeId,
        },
        () => {
          setLoanType(loanTypeId);
          history.push({
            pathname: '/',
          });
          goNext({
            gca: parseInt(amount, 10),
          });
        }
      );
      return;
    }

    const { currentOvl, monthlyPayment } = this.state;
    const { number, tradeAmount, techPassport } =
      (currentOvl && currentOvl.vehicleInfo) || {};
    const { months, lastMonthPayment, total } = this.getScheduleSummary();

    approveLoan(
      {
        amount,
        monthlyPayment,
        total,
        months,
        lastMonthPayment,
        loanTypeId,
        vehicleNumber: number,
        tradeAmount: tradeAmount,
        techPassport: techPassport,
        submittedApr: values.apr,
      },
      () => {
        setLoanType(loanTypeId);
        history.push({
          pathname: '/',
        });
        goNext({
          gca: parseInt(amount, 10),
          gcm: parseInt(monthlyPayment, 10),
          gcvn: number,
          gcva: tradeAmount,
          gcvp: techPassport,
        });
      }
    );
  };

  countInitialMonthlyPayment = (monthlyMin, monthlyMax) => {
    const { monthlyPayment, touchedCount } = this.state;
    const {
      selectedMonthlyPayment,
    } = this.getSelectedAmountAndMonthlyPayment();

    if (touchedCount > 0) {
      if (monthlyPayment < monthlyMin) {
        return monthlyMin;
      } else if (monthlyPayment > monthlyMax) {
        return monthlyMax;
      }
    } else {
      return selectedMonthlyPayment || monthlyMin;
    }

    return monthlyPayment;
  };

  onUpdateAmount = (amount, setFieldValue) => {
    const { activeIndex } = this.props;

    if (activeIndex === -1) {
      return;
    }

    const [monthlyMin, monthlyMax] = this.countMonthlyMinMax(amount);

    const monthlyPayment = this.countInitialMonthlyPayment(
      monthlyMin,
      monthlyMax
    );

    this.setState(
      { amount, monthlyPayment, initialMonthlyPayment: monthlyPayment },
      () => {
        this.updateActualApr(setFieldValue);
      }
    );
  };

  onUpdateMonthly = (monthlyPayment, setFieldValue) => {
    const { onChange } = this.props;
    let { touchedCount } = this.state;
    const {
      selectedMonthlyPayment,
    } = this.getSelectedAmountAndMonthlyPayment();

    if (touchedCount < 0 && selectedMonthlyPayment) {
      monthlyPayment = selectedMonthlyPayment;
    }

    this.setState(
      {
        monthlyPayment,
        initialMonthlyPayment: monthlyPayment,
        touchedCount: ++touchedCount,
      },
      () => {
        onChange(this.state);
        this.updateActualApr(setFieldValue);
      }
    );
  };

  countMonthlyMinMax = amount => {
    const { dstiRepayment, dstiIncome } = this.props;

    const maxMonths = this.getMaxMonths();
    const minMonths = this.getMinMonths();
    const interestRate = this.getInterestRate();
    const calculator = new LoanCalculator(amount, interestRate, maxMonths);
    const {
      allowedMaxMonthly,
      osmDisabled,
      dstiDisabled,
    } = this.allowedMaxMonthly(amount);

    return calculator.monthlyMinMax(
      allowedMaxMonthly,
      osmDisabled,
      dstiRepayment,
      dstiIncome,
      dstiDisabled,
      minMonths
    );
  };

  allowedMaxMonthly = amount => {
    const {
      osm,
      isNewOvlCustomer,
      loan: { key },
    } = this.props;

    if (key === LOAN_TYPES.OVL.name) {
      return {
        allowedMaxMonthly: osm - MONTHLY_REPAYMENT_LIMIT.OVL,
        // For OVL osm - enabled if new customer
        osmDisabled: !isNewOvlCustomer,
        // For OVL dsti - disabled
        dstiDisabled: true,
      };
    }

    return {
      allowedMaxMonthly: osm - MONTHLY_REPAYMENT_LIMIT.OCL,
      // For OCL osm - enabled if credit amount limit is greater than ocl credit amount limit
      osmDisabled: !(amount > OCL_AMOUNT_LIMIT),
      // For OCL osm - enabled if amount is lower than ocl credit amount limit
      dstiDisabled: !(amount <= OCL_AMOUNT_LIMIT),
    };
  };

  getScheduleSummary = () => {
    const { monthlyPayment } = this.state;
    const interestRate = this.getInterestRate();
    const maxMonths = this.getMaxMonths();
    const { amount } = this.state;

    const calculator = new LoanCalculator(amount, interestRate, maxMonths);
    return calculator.scheduleSummary(monthlyPayment);
  };

  getSelectedAmountAndMonthlyPayment() {
    const { loan } = this.props;
    const { amount } = this.state;

    const [monthlyMin, monthlyMax] = this.countMonthlyMinMax(amount);

    const loanTypeId = getLoanType();

    let selectedAmount = null;
    let selectedMonthlyPayment = null;

    if (LOAN_TYPES[loan.key.toUpperCase()].id === loanTypeId) {
      const { gca, gcm } = querystring.parse(window.location.search);

      selectedAmount = Number(gca);
      selectedMonthlyPayment = Number(gcm);

      if (selectedMonthlyPayment > monthlyMax) {
        selectedMonthlyPayment = monthlyMax;
      }
      if (selectedMonthlyPayment < monthlyMin) {
        selectedMonthlyPayment = monthlyMin;
      }
    }

    return {
      selectedAmount,
      selectedMonthlyPayment,
    };
  }

  getSuggestionMonthlyFee = formik => {
    const {
      t,
      disabled,
      loanConfigs: { data: config = {} },
      loan,
    } = this.props;
    const { initialMonthlyPayment, currentOvl } = this.state;

    const maxAvailableAmount = this.getMaxAvailableAmount();
    const { selectedAmount } = this.getSelectedAmountAndMonthlyPayment();

    if (!maxAvailableAmount) {
      return null;
    }

    const minAvailableAmount = this.getMinAmount();
    const { amount, monthlyPayment } = this.state;
    const [monthlyMin, monthlyMax] = this.countMonthlyMinMax(amount);

    const loanStep = +config[loan.key].amountStep;

    const { setFieldValue } = formik;

    const realMonthlyMax = this.getRealMonthlyMax(amount, monthlyMax);

    return (
      <>
        <div className={styles.loan_amount}>
          <LoansAmount
            onUpdateAmount={value => {
              this.onUpdateAmount(value, setFieldValue);
            }}
            min={minAvailableAmount}
            max={maxAvailableAmount}
            step={loanStep}
            selectedAmount={selectedAmount}
            currentOvl={currentOvl}
          />
        </div>
        <div className={styles.slider_group__question_two}>
          {t('suggestions.monthly_fee')}
        </div>
        <Slider
          disabled={disabled}
          onUpdate={value => this.onUpdateMonthly(value, setFieldValue)}
          min={monthlyMin}
          max={monthlyMax}
          realMonthlyMax={realMonthlyMax}
          step={MONTHLY_PAYMENT_STEP}
          initialValue={initialMonthlyPayment || monthlyPayment}
        />
      </>
    );
  };

  getRealMonthlyMax = (amount, monthlyMax) => {
    const interestRate = this.getInterestRate();
    const maxMonths = this.getMaxMonths();

    const calculator = new LoanCalculator(amount, interestRate, maxMonths);
    const payment = calculator.scheduleSummary(monthlyMax);

    return payment.months === 1 ? payment.monthlyPayment : monthlyMax;
  };

  loanInfoObl = (loan, formik) => {
    const { amount } = this.state;
    const { setFieldValue } = formik;
    const {
      isLoanUnavailable,
      wizardBag: { step },
      loanConfigs: { data: config = {} },
      citizen: { data: citizen = {} },
    } = this.props;

    return (
      <LoanInfo
        step={step}
        apr={citizen.apr}
        amount={amount}
        dailyServiceFeeRate={config.obl.dailyServiceFeeRate}
        transactionFeeAmount={this.getTransactionFeeAmount(config, amount)}
        citizen={citizen}
        loanName={loan.key}
        isLoanUnavailable={isLoanUnavailable}
        isLoading={this.isLoading}
        setFieldValue={setFieldValue}
      />
    );
  };

  getTransactionFeeAmount = (config, amount) => {
    const feeValue = 'transactionFee' + amount;

    return config.obl[feeValue];
  };

  loanInfoCommon = (loan, formik) => {
    const { amount, monthlyPayment } = this.state;
    const { isLoanUnavailable } = this.props;
    const maxAvailableAmount = this.getMaxAvailableAmount();

    if (isLoanUnavailable() || !maxAvailableAmount) {
      return;
    }

    const {
      wizardBag: { step },
      citizen: { data: citizen = {} },
    } = this.props;

    const payment = this.getScheduleSummary();

    const { serviceFeeRate, apr, withdrawalFeeAmount } = this.getRates();

    const { setFieldValue } = formik;

    return (
      <LoanInfo
        apr={apr}
        step={step}
        amount={amount}
        citizen={citizen}
        payment={payment}
        loanName={loan.key}
        monthlyPayment={monthlyPayment}
        isLoanUnavailable={isLoanUnavailable}
        serviceFeeRate={serviceFeeRate}
        withdrawalFeeAmount={withdrawalFeeAmount}
        isLoading={this.isLoading}
        setFieldValue={setFieldValue}
      />
    );
  };

  getLoanInfo = (loan, formik) => {
    return isOBL()
      ? this.loanInfoObl(loan, formik)
      : this.loanInfoCommon(loan, formik);
  };

  getInitialValues = key => {
    const values = {
      isHuman: false,
      vehicleNumber: '',
      aprEnabled: true,
      apr: '',
      actualApr: '',
    };

    values[`${key}AprApprovalCheckbox`] = false;

    return values;
  };

  vehicleInfo = formik => {
    const { currentOvl } = this.state;

    return (
      <VehicleInfo
        {...this.props}
        formik={formik}
        currentOvl={currentOvl}
        changeCurrentOvl={this.changeCurrentOvl}
        handleRadioChange={this.handleRadioChange}
      />
    );
  };

  getListIcon = (type, active) => {
    if (active) {
      return <CheckIcon />;
    }

    switch (type) {
      case 'car':
        return <CarIcon />;
      case 'currency':
        return <CurrencyIcon />;
      default:
        return;
    }
  };

  render() {
    const { loan } = this.props;

    return (
      <div className={styles.loan_content}>
        <Formik
          initialValues={this.getInitialValues(loan.key)}
          onSubmit={this.handleSubmit}
          validationSchema={() =>
            wizardLoanAmountSchema(`${loan.key}AprApprovalCheckbox`)
          }
          validateOnBlur={true}
          validateOnChange={false}
        >
          {formik => {
            return (
              <Form onKeyDown={handlePreventEnterKey}>
                {!isOBL() &&
                  loan.key === LOAN_TYPES.OVL.name &&
                  this.vehicleInfo(formik)}

                {!isOBL() && this.getSuggestionMonthlyFee(formik)}

                {this.getLoanInfo(loan, formik)}
              </Form>
            );
          }}
        </Formik>
      </div>
    );
  }
}

LoansContent.propTypes = {
  onChange: PropTypes.func,
};

LoansContent.defaultProps = {
  onChange: () => {},
};

export default withNamespaces('translations')(LoansContent);
