import React, { Component } from 'react';
import { connect } from 'react-redux';
import { withNamespaces } from 'react-i18next';
import { withRouter } from 'react-router-dom';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import querystring from 'query-string';

import i18n from '../../../../i18n';
import LoansList from './LoansList';
import CitizenInfo from './CitizenInfo';
import LoanRejectionText from '../LoanRejectionText';
import { LOAN_TYPES } from '../../../../constants';
import CashMeLoader from '../../../CashMeLoader';
import InternalServerError from '../../../InternalServerError';
import ReferralCodeInfo from '../../../ReferralCode/ReferralCodeInfo';

import { fetchCitizen } from '../../../../redux/ducks/citizen';
import { fetchLoanConfigsForCitizen } from '../../../../redux/ducks/loanConfigs';
import { approveLoan } from '../../../../redux/ducks/loan';

import styles from './index.module.scss';
import { getLoanType, isOBL } from '../../../../helpers/common';

class LoanAmountStep extends Component {
  constructor(props) {
    super(props);

    window.recaptchaOptions = {
      lang: i18n.language,
    };

    this.state = {};
  }

  componentDidMount() {
    window.scroll(0, 0);

    const { fetchCitizen, fetchLoanConfigsForCitizen } = this.props;
    const loanTypeId =
      getLoanType() != null ? getLoanType() : LOAN_TYPES.COMMON.id;
    const { gcva, gcvn, gcvp } = querystring.parse(this.props.location.search);

    fetchCitizen({
      loan_type_id: loanTypeId,
      vehicle_number: gcvn,
      tech_passport: gcvp,
      trade_amount: gcva,
    });

    fetchLoanConfigsForCitizen(loanTypeId);
  }

  isLoanUnavailable = () => {
    const {
      citizen: { data: citizen = { credit: {} } },
    } = this.props;
    const OvlCredits =
      citizen.credit.ovl &&
      citizen.credit.ovl.filter(credit => !credit.rejected);

    if (isOBL() && citizen.credit.obl) {
      return citizen.credit.obl.length === 0;
    }

    return (!OvlCredits || !OvlCredits.length) && !citizen.credit.ocl;
  };

  isLoading() {
    const {
      citizen: { loading },
      loan,
      loanConfigs,
    } = this.props;

    return loading || loan.loading || loanConfigs.loading;
  }

  isCitizenUnavailable = () => {
    return !!this.props.citizen.error;
  };

  extractPassportNumber = passports => {
    if (passports) {
      const p =
        passports &&
        (passports.BIOMETRIC_PASSPORT ||
          passports.NON_BIOMETRIC_PASSPORT ||
          passports.ID_CARD);
      return p.passportNumber;
    }
  };

  renderLoanDetails() {
    const {
      citizen: { data: citizen = {} },
    } = this.props;

    if (this.isLoanUnavailable() && !citizen.allowTrade) {
      return;
    }

    return (
      <div>
        <LoansList
          {...this.props}
          osm={citizen.osm}
          dstiRepayment={citizen.dsti}
          dstiIncome={Math.max(citizen.dstiIncome, citizen.salary)}
          isNewOvlCustomer={citizen.isNewOvlCustomer}
          loading={this.isLoading()}
          isLoanUnavailable={this.isLoanUnavailable}
        />
      </div>
    );
  }

  render() {
    const {
      t,
      citizen: { data: citizen = { credit: {} } },
    } = this.props;

    return (
      <div id={styles.loan_amount_form}>
        {!this.isLoanUnavailable() &&
          !this.isCitizenUnavailable() &&
          !isOBL() && <ReferralCodeInfo />}
        <CashMeLoader loading={this.isLoading()} />

        {!this.isLoading() && (
          <>
            {this.isCitizenUnavailable() ? (
              <InternalServerError />
            ) : (
              <>
                <CitizenInfo citizen={citizen} loading={this.isLoading()} />
                <div
                  className={classnames({
                    [styles.section_border_top]:
                      !this.isLoanUnavailable() || citizen.allowTrade,
                  })}
                >
                  {!this.isLoanUnavailable() && (
                    <div>
                      <div className={styles.available_amount_text}>
                        {t('loan.steps.loan_amount.available_amount')}
                      </div>
                    </div>
                  )}
                </div>

                {this.isLoanUnavailable() &&
                  !citizen.allowTrade && <LoanRejectionText />}

                <div className={styles.slider_group}>
                  {this.renderLoanDetails()}
                </div>
              </>
            )}
          </>
        )}
      </div>
    );
  }
}

LoanAmountStep.propTypes = {
  wizardBag: PropTypes.object,
};

function mapStateToProps(state) {
  const { citizen, loanConfigs, loan } = state;

  return { citizen, loanConfigs, loan };
}

const mapDispatchToProps = dispatch => {
  return {
    fetchCitizen: loanTypeId => dispatch(fetchCitizen(loanTypeId)),
    fetchLoanConfigsForCitizen: loanTypeId =>
      dispatch(fetchLoanConfigsForCitizen(loanTypeId)),
    approveLoan: (data, cb) => dispatch(approveLoan(data, cb)),
  };
};

export default compose(
  withRouter,
  withNamespaces('translations'),
  connect(
    mapStateToProps,
    mapDispatchToProps
  )
)(LoanAmountStep);
