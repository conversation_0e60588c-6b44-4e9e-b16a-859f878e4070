import React from 'react';
import _ from 'lodash';
import { Image } from 'semantic-ui-react';
import { withNamespaces } from 'react-i18next';

import rejection_icon from '../../../../svgs/rejection.svg';
import styles from './index.module.scss';

const LoanRejectionText = ({ t }) => {
  return (
    <div className={styles.loan_rejection_text}>
      <Image src={rejection_icon} />
      <div className={styles.header}>
        {t('loan.steps.loan_amount.rejection_text')}
      </div>
      <div className={styles.desc}>
        <div className={styles.title}>
          {t('loan.steps.loan_amount.rejection_text_reason')}
        </div>
        <ul>
          {_.range(0, 5).map((el, index) => (
            <li key={index}>
              {t(`loan.steps.loan_amount.rejection_text_${el}`)}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default withNamespaces('translations')(LoanRejectionText);
