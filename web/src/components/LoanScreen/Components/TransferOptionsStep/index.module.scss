@import '../../../../styles/colors';
@import '../../../../styles/sizes';

#bank_info {
  padding: 10px;

  .transfer_methods {
    display: none;
  }

  .transfer_menu {
    margin-top: 40px;
    min-height: 400px;

    & > div:first-child {
      width: 100%;
      margin: 0 auto;
      height: 70px;
    }

    & > div::after {
      display: none;
    }

    & > div {
      justify-content: center;
      border: none;
      box-shadow: none;
      margin-bottom: 5px;
      padding: 0;

      a:global(.active) {
        box-shadow: 0 0 6px 2px $black-with-opacity;

        .transfer {
          .transfer_title {
            font-weight: bold;
          }
        }
      }

      a {
        padding: 0;
        margin: 0 15px;
        width: min-content;
        transition: box-shadow 0.2s;

        .transfer {
          padding: 10px 18px;
          display: flex;
          flex-direction: row;
          min-height: 60px;

          img {
            width: fit-content;
            margin: auto;
          }

          .transfer_title {
            width: fit-content;
            font-family: 'DejaVu Sans Book';
            margin: 12px 10px;
            font-size: 12px;
            line-height: initial;
          }
        }

        .transfer_title {
          font-weight: lighter;
        }
      }

      a:hover {
        box-shadow: 0 0 6px 1px $secondary;
      }
    }
  }

  @media screen and (max-width: $tablet-width) {
    .transfer_menu {
      & > div {
        a {
          margin: 0 5px;

          .transfer {
            flex-direction: column;
            text-align: center;
            .transfer_title {
              margin-top: 5px;
            }
          }
        }
      }
    }
  }

  @media screen and (max-width: $lowres-tablet-width) {
    .transfer_menu {
      & > div {
        a {
          width: 110px;

          @supports (-webkit-overflow-scrolling: touch) {
            width: 130px;
          }

          .transfer {
            padding: 5px;

            .transfer_title {
              font-size: 9px;
              margin: 0;
              margin-top: 3px;
            }
          }
        }
      }
    }
  }

  @media screen and (max-width: $mobile-width) {
    .transfer_menu {
      margin-top: 0;
    }

    .transfer_menu > div:first-child {
      display: none;
    }

    .transfer_methods {
      position: relative;
      border: 1px solid rgba(34, 36, 38, 0.15);
      height: 45px;
      font-size: 13px;
      display: block;
      margin-top: 26px;

      .transfer_methods_icon {
        position: absolute;
        top: 15px;
        left: 92%;
      }

      div {
        display: flex;
        flex-direction: row;

        img {
          height: 20px;
        }

        .transfer_text {
          font-weight: 100;
          margin: auto 13px;

          @supports (-webkit-overflow-scrolling: touch) {
            line-height: inherit;
          }
        }

        :global(.active) {
          .transfer_text {
            color: $secondary;
          }
        }
      }
    }

    .transfer_methods > div:last-child {
      display: none;
    }

    .wizard_navigation {
      .previous_step_button {
        display: none;
      }

      .previous_step_arrow {
        display: block;
        height: 35px;
        fill: $secondary;
        margin: 20px auto;
      }
    }
  }
}
