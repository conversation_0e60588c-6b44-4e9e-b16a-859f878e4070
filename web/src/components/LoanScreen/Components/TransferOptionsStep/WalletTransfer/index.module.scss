@import '../../../../../styles/sizes';
@import '../../../../../styles/colors';
@import '../../../../../styles/mixins';

.wallet_transfer {
  .wallet_transfer_block {
    text-align: center;
    margin-top: 30px;
    margin-bottom: 72px;

    .wallet_logo {
      height: 80px;
      position: relative;

      .idram_logo {
        width: 250px;
        height: 75px;
      }

      .easypay_logo {
        width: 250px;
        height: 50px;
        position: absolute;
        bottom: 5px;
        margin-left: auto;
        margin-right: auto;
        left: 0;
        right: 0;
      }
    }

    .wallet_container {
      @include account_number_input;

      margin-bottom: -40px;

      .confirmation_block {
        margin: 50px auto 0px;
        text-align: center;

        .cardTransfer_confirmation_text {
          text-align: left;
          font-size: 9px;
          line-height: initial;
          font-style: oblique;
          font-weight: bold;

          label {
            padding-top: 3px;
            padding-left: 25px;
            font-size: 9px;
            line-height: initial;
            font-style: oblique;
            color: $main-text-color;
            font-weight: bold;
          }

          label::after {
            padding-top: 2px;
            font-size: 10px;
            color: $white;
            border-radius: 2px;
            background-color: $secondary;
          }
        }
      }
    }
  }

  @media screen and (max-width: $mobile-width) {
    .wallet_transfer_block {
      .wallet_logo {
        img {
          width: 300px;
        }
      }
    }
  }
}

#wallet_container {
  text-align: center;
  margin-top: 35px;

  .wallet_header {
    font-size: 20px;
    margin-bottom: 20px;
    color: $main;
    line-height: 25px;
  }

  .select_wallet_logo {
    margin-top: 20px;
    margin-bottom: 50px;

    img {
      margin-left: 50px;
    }
  }

  .wallet_transfer_menu {
    margin-top: 0;

    & > div {
      border: none;
    }
  }

  .wallet_container_img {
    height: 20px;
  }

  .transfer_text {
    display: inline-block;
    font-weight: 100;
    margin: auto 13px;
    width: 200px;
  }

  .wallet_transfer_menu > div:first-child {
    display: none;
  }

  .wallet_transfer_methods {
    width: 400px;
    height: 44px;

    :global(.default.text) {
      margin-top: 5px;
    }

    div:first-child {
      display: flex;
      margin-top: 2px;
    }

    .transfer_methods_icon {
      position: absolute;
      top: 15px;
      left: 92%;
    }
  }

  @media screen and (max-width: $mobile-width) {
    .wallet_transfer_methods {
      width: 100%;
    }
  }
}
