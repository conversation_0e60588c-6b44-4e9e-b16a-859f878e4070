@import '../../../../styles/colors';
@import '../../../../styles/sizes';

#car_verification {
  width: 80%;
  margin: auto;
  text-align: center;

  .car_verification_info {
    max-width: 850px;
    margin: 30px auto;
    text-align: center;
    font-size: 25px;
    line-height: 1.5;
    color: $main;
  }

  .car_verification_container {
    width: 700px;
    margin: 50px auto;
    text-align: left;
    position: relative;

    .car_verification_dashes {
      height: 85%;
      border-left: 2px dashed $main;
      margin-left: 25px;
      position: absolute;
      top: 0;
      z-index: 1;
    }

    .car_verification_number_circle {
      display: inline-block;
      box-shadow: $black 0px 0px 7px -3px;
      width: 53px;
      height: 53px;
      border-radius: 100%;
      font-size: 25px;
      padding: 17px;
      text-align: center;
      color: $main;
      position: absolute;
      z-index: 5;
      background: $white;
    }

    .car_verification_item {
      .car_verification_section {
        display: inline-block;
        padding: 17px 15px;
        width: 90%;
        margin-left: 60px;
        margin-top: -10px;

        .car_verification_section_title {
          font-size: 25px;
          color: $main;
          line-height: 40px;
        }

        .car_verification_section_content {
          position: relative;
          border: 1px solid $light-gray;
          margin-top: 20px;
          padding: 20px;
          box-shadow: $black 0px 0px 10px -6px;
          height: fit-content;

          .car_verification_menu {
            text-align: center;

            .car_verification_section_dates {
              width: 170px;
              height: 52px;
              margin: 10px;
              border: 1px solid $darky-gray;
              border-radius: 8px;
              color: $darky-gray;

              i {
                font-size: 25px;
                margin: 12px 12px 12px 5px;
              }

              .car_verification_section_dates_text {
                text-align: left;
                font-size: 14px;
                line-height: 1.5;
              }
            }

            .car_verification_section_dates:global(.active) {
              background-color: $main;
              border: none;
              color: $white;

              .car_verification_section_dates_text {
                .car_verification_section_dates_value {
                  opacity: 0.5;
                }
              }
            }
          }

          .car_verification_section_content_map {
            height: 300px;
            position: relative;
          }

          .car_verification_section_content_address {
            margin-top: 10px;
            height: 10px;
            text-align: left;
          }

          .car_verification_section_content_map {
            .map_container > div > div:first-child {
              margin-top: 60px;
            }

            .map_container {
              .center_marker {
                position: absolute;
                background: url(http://maps.gstatic.com/mapfiles/markers/marker.png)
                  no-repeat;
                top: 50%;
                left: 50%;
                z-index: 1;
                margin-left: -10px;
                height: 34px;
                width: 20px;
                cursor: pointer;
              }
            }
          }

          .time_dropdowns_container {
            margin: 20px auto;
            text-align: center;

            .time_dropdown {
              width: 47%;
              display: inline-block;

              .dropdown {
                padding: 12px 16px;
                height: 42px;
                line-height: 17px;
              }

              .time_dropdown_icon {
                float: right;
              }
            }

            .dropdowns_divider {
              display: inline-block;
              margin: 10px;
            }
          }
        }

        .car_verification_section_content_textarea {
          width: 100%;
          height: 90px;
          resize: none;
          margin-top: 20px;
          font-size: 16px;
        }
      }
    }
  }
}

@media screen and (max-width: $tablet-width) {
  #car_verification {
    width: 90%;
  }
}

@media screen and (max-width: $lowres-tablet-width) {
  #car_verification {
    width: 100%;

    .car_verification_info {
      display: none;
    }

    .car_verification_container {
      width: 100%;
      margin: 0 auto;

      .car_verification_dashes {
        display: none;
      }

      .car_verification_number_circle {
        width: 50px;
        height: 50px;
        font-size: 20px;
        padding: 16px;
        left: 15px;
      }

      .car_verification_item {
        text-align: center;

        .car_verification_section {
          display: block;
          width: 100%;
          margin: 0;

          .car_verification_section_title {
            text-align: left;
            margin-left: 70px;
            line-height: 15px;
            font-size: 20px;
          }

          .car_verification_section_content {
            margin-top: 40px;

            .car_verification_menu {
              width: 100%;
              justify-content: center;
              margin: 0;

              .car_verification_section_dates {
                width: 30%;
                justify-content: center;

                i {
                  font-size: 20px;
                  margin: 0px 5px 0px 0px;
                }
              }
            }

            .time_dropdowns_container {
              .time_dropdown {
                width: 46%;
              }
            }
          }

          .car_verification_section_content_textarea {
            margin-top: 40px;
          }
        }
      }
    }
  }
}

@media screen and (max-width: $mobile-width) {
  #car_verification {
    .car_verification_container {
      .car_verification_number_circle {
        width: 25px;
        height: 25px;
        font-size: 14px;
        padding: 4px;
        left: 15px;
      }

      .car_verification_item {
        .car_verification_section {
          padding: 13px 15px;

          .car_verification_section_title {
            font-size: 14px;
            margin-left: 40px;
            line-height: 0px;
          }

          .car_verification_section_content {
            padding: 10px;
            margin-top: 30px;

            .car_verification_menu {
              width: 100%;
              justify-content: center;
              margin: 0;

              .car_verification_section_dates {
                height: 30px;
                border-radius: 4px;
                margin: 0 5px 0 5px;

                i {
                  font-size: 10px;
                }

                .car_verification_section_dates_text {
                  font-size: 10px;
                  line-height: 1.2;
                }
              }
            }

            .car_verification_section_content_map {
              height: 200px;
            }

            .car_verification_section_content_address {
              margin-top: 0px;
              font-size: 10px;
              margin-bottom: 10px;
              height: 15px;
            }

            .time_dropdowns_container {
              .time_dropdown {
                width: 45%;

                .dropdown {
                  div:first-child {
                    width: 70%;
                  }
                }
              }
            }
          }

          .car_verification_section_content_textarea {
            margin-top: 30px;
          }
        }
      }
    }
  }
}
