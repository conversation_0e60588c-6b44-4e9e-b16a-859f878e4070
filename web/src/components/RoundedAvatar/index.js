import React, { Component } from 'react';
import { ReactComponent as DefaultAvatar } from '../../svgs/default-avatar.svg';

import styles from './index.module.scss';

class RoundedAvatar extends Component {
  photo = () => {
    const { photo, firstName, size } = this.props;

    return photo ? (
      <div
        className={size === 'xs' ? styles.avatar_xs : styles.avatar}
        style={{
          backgroundImage: `url(data:image/png;base64,${photo})`,
        }}
      />
    ) : (
      <div className={size === 'xs' ? styles.no_avatar_xs : styles.no_avatar}>
        {firstName ? firstName[0] : ''}
      </div>
    );
  };

  render() {
    const { loading, size } = this.props;

    return (
      <div className={styles.avatar_container}>
        {loading ? (
          <DefaultAvatar className={size === 'xs' ? styles.avatar_xs : ''} />
        ) : (
          this.photo()
        )}
      </div>
    );
  }
}

RoundedAvatar.defaultProps = {
  size: 'md',
};

export default RoundedAvatar;
