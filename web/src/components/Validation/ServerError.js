import React, { Component } from 'react';
import { withNamespaces } from 'react-i18next';
import { compose } from 'redux';

class ServerError extends Component {
  render() {
    const { t, error = {} } = this.props;

    if (!error || !(error.code || error.errors)) {
      return null;
    }

    let errorCode = error.code;

    if (!error.code) {
      const firstKey = Object.keys(error.errors)[0];

      // Taking first error from validation errors with its first message.
      errorCode = error.errors[firstKey][0].code;

      // Error code is not number means that validation case error code was note provided by API.
      // Returning standard internal error text to avoid showing inappropriate messages.
      if (isNaN(errorCode))
        return (
          <div className="server_error">
            {t(`loan.internal_server_error_text`)}
          </div>
        );
    }

    return (
      <div className="server_error">{t(`validation_errors.${errorCode}`)}</div>
    );
  }
}

export default compose(withNamespaces('translations'))(ServerError);
