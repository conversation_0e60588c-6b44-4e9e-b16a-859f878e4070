@import '../../../styles/colors';

#referral_code_modal {
  padding: 10px 0 20px 0;
  border-radius: 10px;
  width: 400px;
  max-width: calc(100% - 10px);

  .header,
  .content,
  .actions {
    display: flex;
    justify-content: center;
  }

  .header {
    border-bottom: none;

    .logo {
      width: 110px;
    }
  }

  .content {
    position: relative;
    padding: 10px;
    text-align: center;
    flex-direction: column;

    .description {
      width: 100%;
      margin-bottom: 20px;
      padding: 0 !important;
      text-align: left;
      font-size: 13px;
      max-height: 272px;
      overflow-y: auto;
    }

    .referral_code_section {
      justify-content: center;
      margin: 10px 0;
    }
  }

  :global(.toggle-description-btn) {
    color: $main;
    justify-content: center;
    display: flex;
  }

  :global(.server_error) {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    margin-top: unset;
    margin-bottom: unset;
  }

  .close_modal {
    width: 16px;
    height: 16px;
    position: absolute;
    cursor: pointer;
    top: 10px;
    right: 10px;
  }

  .actions {
    padding-top: 10px;
    border-top: none;
    background: white;
    flex-direction: column;
    align-items: center;

    .confirmation_text {
      margin-bottom: 25px;
      display: flex;
      align-items: center;

      label {
        padding-left: 25px;
        text-align: left;
        font-size: 11px;
        line-height: initial;
        font-style: oblique;
        color: $main-text-color;
        font-weight: bold;
      }

      label::after {
        padding-top: 3px;
        font-size: 10px;
        color: $white;
        border-radius: 2px;
        background-color: $orange;
      }
    }

    :global(.ui.inverted.dimmer) {
      border-radius: 10px;
    }

    button {
      width: 67%;
      font-size: 18px;
      margin: unset;
      font-weight: 500;
    }

    .get_referral_code_btn {
      background-color: $orange;
    }

    .copy_referral_code_btn {
      background-color: transparent;
      color: $orange;
      border: 1px solid $orange;
    }

    .copied_referral_code_btn {
      background-color: transparent;
      color: $light-green;
      border: 1px solid $light-green;
    }
  }
}
