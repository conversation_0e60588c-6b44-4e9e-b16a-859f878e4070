import React from 'react';
import { Checkbox, Modal } from 'semantic-ui-react';
import querystring from 'query-string';
import classnames from 'classnames';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import { Trans, withNamespaces } from 'react-i18next';
import { connect } from 'react-redux';
import ShowMoreText from 'react-show-more-text';
import _ from 'lodash';

import GCButton from '../../GCButton';
import CodeInput from '../CodeInput';
import ServerError from '../../Validation/ServerError';
import {
  DOCUMENT_POLLING_INTERVAL,
  DOCUMENT_POLLING_TIMEOUT,
} from '../../../constants';
import CashMeLoader from '../../CashMeLoader';

import {
  generateReferralAgreement,
  getReferralCode,
  verifyReferralCode,
} from '../../../redux/ducks/referralCode';

import Logo from '../../../svgs/cash_me.svg';
import Close from '../../../svgs/close.svg';

import styles from './index.module.scss';

class GetReferralCodeModal extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      documentPollingHandle: null,
      documentGenerationFailure: false,
      isConfirmationChecked: false,
      isCodeCopied: false,
      open: false,
    };

    this.startPollingOnce = _.once(this.startPolling);
  }

  componentDidMount() {
    this.generateReferralAgreement();
    this.getReferralCode();
  }

  componentDidUpdate(prevProps, prevState, snapshot) {
    const { referralCode } = this.props;

    if (
      referralCode &&
      referralCode.path === null &&
      !this.state.documentPollingHandle
    ) {
      this.startPollingOnce();
    }

    if (referralCode && prevProps.referralCode !== referralCode) {
      this.setState({ open: true });

      if (referralCode.path !== null) {
        this.stopPolling();
      }
    }
  }

  componentWillUnmount() {
    clearInterval(this.state.documentPollingHandle);
  }

  startPolling = () => {
    const documentPollingHandle = setInterval(
      this.getReferralCode,
      DOCUMENT_POLLING_INTERVAL
    );

    // Setting timeout to abort polling after some time
    setTimeout(() => {
      this.stopPolling();
      this.setState({
        documentGenerationFailure: true,
      });
    }, DOCUMENT_POLLING_TIMEOUT);

    this.setState({
      documentPollingHandle,
    });
  };

  stopPolling() {
    clearInterval(this.state.documentPollingHandle);
    this.setState({
      documentPollingHandle: false,
    });
  }

  getReferralCode = () => {
    const { getReferralCode } = this.props;
    const hash = this.getHash();

    getReferralCode({
      hash,
    });
  };

  generateReferralAgreement = () => {
    const { generateReferralAgreement } = this.props;
    const hash = this.getHash();

    generateReferralAgreement({
      hash,
    });
  };

  handleVerifyReferralCode = () => {
    const { verifyReferralCode } = this.props;
    const hash = this.getHash();

    verifyReferralCode({
      hash,
    });
  };

  handleOnChangeCheckbox = () => {
    const { isConfirmationChecked } = this.state;

    this.setState({
      isConfirmationChecked: !isConfirmationChecked,
    });
  };

  handleCloseModal = () => {
    this.setState({
      open: false,
    });
  };

  getHash() {
    const { location } = this.props;
    const { h } = querystring.parse(location.search);

    return h;
  }

  handleCopyReferralCode = () => {
    this.setState({ isCodeCopied: true });
  };

  getTransComponents = () => {
    return [
      <p>text</p>,
      <i>text</i>,
      <b>text</b>,
      <ul>
        <li>text</li>
        <li>text</li>
        <li>text</li>
      </ul>,
      <br />,
    ];
  };

  renderActions = () => {
    const { t, referralCode } = this.props;
    const { isConfirmationChecked, isCodeCopied } = this.state;

    return referralCode && referralCode.verified ? (
      <CopyToClipboard
        onCopy={this.handleCopyReferralCode}
        text={referralCode.code}
      >
        <GCButton
          className={classnames(
            isCodeCopied
              ? styles.copied_referral_code_btn
              : styles.copy_referral_code_btn
          )}
          onClick={this.handleCopyReferralCode}
          type="submit"
          primary={true}
        >
          {isCodeCopied
            ? t('referral_code.copied_code_btn')
            : t('referral_code.copy_code_btn')}
        </GCButton>
      </CopyToClipboard>
    ) : (
      <>
        <Checkbox
          className={styles.confirmation_text}
          label={t('referral_code.confirmation_text')}
          checked={isConfirmationChecked}
          onChange={this.handleOnChangeCheckbox}
        />
        <GCButton
          className={styles.get_referral_code_btn}
          onClick={this.handleVerifyReferralCode}
          type="submit"
          primary={true}
          disabled={!isConfirmationChecked}
        >
          {t('referral_code.get_code_btn')}
        </GCButton>
      </>
    );
  };

  render() {
    const { t, referralCode, loading, error } = this.props;
    const { open, documentPollingHandle } = this.state;

    const code =
      referralCode && referralCode.verified ? referralCode.code : null;

    return (
      <Modal
        id={styles.referral_code_modal}
        dimmer={'inverted'}
        open={open}
        onClose={this.handleCloseModal}
        closeIcon={
          <img className={styles.close_modal} src={Close} alt="close" />
        }
      >
        <CashMeLoader
          loading={loading || documentPollingHandle}
          circleLoader={true}
        />

        <Modal.Header className={styles.header}>
          <img className={styles.logo} src={Logo} alt="Logo" />
        </Modal.Header>

        <Modal.Content className={styles.content}>
          <Modal.Description className={styles.description}>
            <ShowMoreText
              lines={3}
              more={t('referral_code.show_more')}
              less={t('referral_code.show_less')}
              anchorClass="toggle-description-btn"
              expanded={false}
            >
              <Trans
                defaults="referral_code.description"
                components={this.getTransComponents()}
              />
            </ShowMoreText>
          </Modal.Description>
          <div className={styles.referral_code_section}>
            <CodeInput code={code} />
          </div>
          <ServerError error={error} />
          {referralCode &&
            referralCode.path && (
              <label>
                <a
                  target="_blank"
                  rel="noopener noreferrer"
                  href={referralCode.fullPath}
                >
                  {t('referral_code.agreement')}
                </a>
              </label>
            )}
        </Modal.Content>

        <Modal.Actions className={styles.actions}>
          {this.renderActions()}
        </Modal.Actions>
      </Modal>
    );
  }
}

function mapStateToProps(state) {
  const { data: referralCode, error, loading } = state.referralCode;

  return {
    referralCode,
    error,
    loading,
  };
}

const mapDispatchToProps = dispatch => {
  return {
    generateReferralAgreement: data =>
      dispatch(generateReferralAgreement(data)),
    getReferralCode: data => dispatch(getReferralCode(data)),
    verifyReferralCode: data => dispatch(verifyReferralCode(data)),
  };
};

export default compose(
  withRouter,
  withNamespaces('translations'),
  connect(
    mapStateToProps,
    mapDispatchToProps
  )
)(GetReferralCodeModal);
