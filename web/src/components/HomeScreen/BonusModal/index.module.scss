@import '../../../styles/colors';
@import '../../../styles/sizes';

#bonus_modal {
  padding: 10px 0;
  border-radius: 10px;
  box-shadow: 10px;
  width: 400px;
  max-width: calc(100% - 20px);

  .close_modal {
    width: 16px;
    height: 16px;
    position: absolute;
    cursor: pointer;
    top: 10px;
    right: 10px;
  }

  .bonus_content {
    padding: 10px;

    .discount {
      position: relative;

      .discount_background {
        width: 100%;
      }

      .discount_value {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 90px;
        height: 90px;
        background: #8f3021;
        font-weight: bold;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 40px;
        color: $white;

        span {
          font-weight: normal;
          font-size: 25px;
          position: relative;
          top: -15px;
        }
      }
    }

    .discount_description {
      font-size: 16px;
      text-align: center;
    }

    .discount_apply {
      display: flex;
      justify-content: center;
      padding-top: 30px;

      button {
        background-color: $orange;
      }
    }
  }
}
