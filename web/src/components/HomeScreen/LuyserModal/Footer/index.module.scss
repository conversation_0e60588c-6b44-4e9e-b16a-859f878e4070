@import '../../../../styles/colors';
@import '../../../../styles/sizes';

.luyser_footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .logo {
    svg {
      fill: $gray-blue;
      width: 101px;
      height: 27px;
    }
  }

  .info {
    display: flex;
    max-width: 250px;

    .socials {
      .icon {
        margin: 0 6px;
        font-size: 24px;
        color: $gray-blue;
      }
    }

    .terms {
      max-width: 140px;
      margin-left: 10px;
      color: $gray-blue;
      font: normal normal normal 10px/14px DejaVu Sans;
      display: flex;
      flex-direction: column;
      justify-content: end;

      a {
        color: $gray-blue;
      }
    }
  }

  @media screen and (max-width: $mobile-width) {
    flex-direction: column;

    .info {
      margin-top: 12px;
      flex-direction: column;
      align-items: center;

      .socials {
        margin-bottom: 12px;
      }

      .terms {
        text-align: center;
        margin: 0 auto;

        a {
          text-decoration: underline;
        }
      }
    }
  }

  @media (orientation: landscape) {
    @media screen and (max-height: 600px) {
      padding: 10px 0;
    }
  }
}
