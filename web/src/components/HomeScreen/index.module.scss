@import '../../styles/colors';
@import '../../styles/sizes';

#home_screen {
  text-align: center;
  padding: 0;
  z-index: 1;

  .main_container {
    color: $white;
    position: relative;
    background-image: url('../../svgs/background_image.jpg');
    background-repeat: no-repeat;
    background-size: cover;
    height: fit-content;

    .browser_support {
      padding: 15%;
    }

    .inner_container {
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      display: flex;
      height: 100%;
      position: relative;
      padding: 2% 0 5% 0;

      .homescreen_container {
        width: 100%;

        form {
          :global(.server_error) {
            max-width: 435px;
            line-height: 16px;
            margin: 5px auto 12px;
          }
        }

        .address_input,
        .loan_types_dropdown {
          background-color: $white;
          width: 435px;
          border-radius: 8px;
          margin-bottom: 10px;
          font-size: 18px;

          input {
            padding: 1px 10px;
            height: 50px;
            border-radius: 8px;
            font-family: 'Deja<PERSON>u Sans Book';
          }
        }

        .address_input {
          font-size: 16px;
        }

        :global(.disabled) {
          background-color: $lighty-gray;
          opacity: 1;
        }

        .loan_types_dropdown {
          height: 50px;
          line-height: 32px;

          i {
            padding: 15px;
            display: flex;
            align-items: center;
            font-size: 16px;
          }

          div {
            span {
              font-size: 16px;
            }
          }
        }

        .loan_sub_select {
          margin-top: 10px;
        }

        .form_columns {
          margin: auto;
          width: 100%;

          .form_info {
            width: 40%;
            float: left;
            margin-left: 80px;
            margin-top: 80px;
            text-align: left;

            .form_info_text {
              font-size: 38px;
              width: 100%;
              line-height: 50px;
              padding: 10px;
              margin-top: 30px;
            }

            svg {
              fill: $white;
              width: 20em;
              height: 5em;
            }
          }

          .form_title {
            font-size: 24px;
            width: 435px;
            margin: 10px auto;
            text-align: left;
          }

          .form_fields {
            width: 435px;
            margin-right: 50px;
            float: right;
          }

          .information_text {
            font-size: 7px;
            margin: 0 auto 15px;
            width: 435px;
            text-align: left;
          }
        }

        .vehicle_number {
          margin-bottom: 20px;
        }

        .document_number_title,
        .input_title {
          font-size: 14px;
          width: 435px;
          margin: 10px auto;
          text-align: left;
        }

        .confirmation_text {
          width: 435px;

          .confirmation_text_link {
            color: white;
            text-decoration: underline;
          }
        }

        .confirmation_text,
        .information_text {
          font-style: oblique;
          margin-bottom: 10px;
          line-height: normal;

          label {
            padding-left: 3em;
            font-size: 9px;
            text-align: left;
            color: $white;
            float: left;
          }

          label::after {
            top: 3px;
            font-size: 10px;
            color: $secondary;
          }
        }

        .confirmation_button {
          margin-bottom: 40px;
        }

        .oiql_form {
          min-height: 625px;

          .oiql_container {
            padding-top: 60px;
            text-align: center;
            max-width: 600px;
            margin: 0 auto;
            min-width: 336px;

            .pipe_dropdown {
              width: 435px;
              margin: 0 auto;
              margin-bottom: 20px;
              height: 50px;
              padding: 16px;
              border-radius: 8px;

              i {
                padding: 16px;
              }
            }
          }

          .information_text {
            font-size: 7px;
            margin: 0 auto 15px;
            width: 435px;
          }

          .confirmation_text {
            width: 435px;
            display: flex;
            margin: 0 auto 20px auto;
          }
        }

        .oasl_form {
          min-height: 625px;

          .oasl_container {
            padding-top: 60px;
            text-align: center;
            max-width: 600px;
            margin: 0 auto;
            min-width: 336px;

            .oasl_dropdown {
              width: 435px;
              margin: 0 auto;
              margin-bottom: 20px;
              height: 50px;
              padding: 16px;
              border-radius: 8px;

              i {
                padding: 16px;
              }
            }
          }

          .information_text {
            font-size: 7px;
            margin: 0 auto 15px;
            width: 435px;
          }

          .confirmation_text {
            width: 435px;
            display: flex;
            margin: 0 auto 20px auto;
          }
        }

        .bnpl_purchase_order_form {
          min-height: 625px;

          .bnpl_purchase_order_container {
            padding-top: 60px;
            text-align: center;
            max-width: 600px;
            margin: 0 auto;

            .purchase_order_amount {
              input {
                width: 435px;
                font-size: 30px;
                height: 50px;
                float: right;
                border: none !important;
                border-radius: 8px;
              }
            }
          }

          .information_text {
            font-size: 7px;
            margin: 0 auto 15px;
            width: 435px;
          }

          .confirmation_text {
            width: 435px;
            display: flex;
            margin: 0 auto 20px auto;
          }
        }

        .ovil_order_form {
          min-height: 625px;

          .ovil_order_container {
            padding-top: 60px;
            text-align: center;
            max-width: 600px;
            margin: 0 auto;

            .order_amount {
              input {
                width: 435px;
                font-size: 30px;
                height: 50px;
                float: right;
                border: none !important;
                border-radius: 8px;
              }
            }

            .vehicle_details {
              display: flex;
              justify-content: center;

              .ovil_dropdown {
                display: flex;
                justify-content: center;
                width: 435px;
                font-size: 25px;
                border: none !important;
                border-radius: 8px;
                -webkit-border-radius: 8px;
                -moz-border-radius: 8px;
                -ms-border-radius: 8px;
                -o-border-radius: 8px;
                height: 50px;
                line-height: 34px;

                i {
                  padding: 15px;
                  display: flex;
                  align-items: center;
                  font-size: 16px;
                }

                div {
                  span {
                    font-size: 16px;
                  }
                }

                input {
                  height: 50px;
                  padding: 8px 16px;
                  text-indent: 5px;
                }
              }
            }
          }

          .information_text {
            font-size: 7px;
            margin: 0 auto 15px;
            width: 435px;
          }

          .confirmation_text {
            width: 435px;
            display: flex;
            margin: 0 auto 20px auto;
          }
        }
      }
    }
  }

  .web_device {
    background-image: url('../../svgs/background_image_web.png');
    background-position: 0 0;
    position: relative;
  }

  .confirmation_text_web_devices {
    position: absolute;
    bottom: 8%;
    left: 17.5%;
    z-index: 9;
    font-size: 1.1vw;
    color: black;

    a {
      margin-left: 8px;
    }
  }

  .info_section {
    margin-top: 24.21px;

    .info_title {
      line-height: initial;
      font-size: 30px;
      color: $main;
    }

    .reasons_body {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      flex-direction: row;
      justify-content: space-evenly;
      margin-top: 15px;
      margin-bottom: 66px;

      .reasons_member {
        margin: 0 auto;
      }

      .reason_icon {
        min-height: 60px;
      }

      .reason_title {
        color: $main;
        font-size: 16px;
        margin-bottom: 12px;
        margin-top: 30px;
      }

      .reason_description {
        color: $main-text-color;
        width: 270px;
        font-size: 16px;
      }

      & > div {
        margin-top: 35px;
      }
    }

    .process_body {
      .process_explanation {
        display: flex;
        flex-direction: row;
        justify-content: center;
        margin-top: 40px;

        .arrow {
          margin: 5px -6em 0 -6em;
          height: 30px;
          width: 200px;
        }

        .process_title {
          margin-top: 18px;
          font-size: 16px;
          color: $secondary-text-color;
        }

        .process_description {
          color: $main-text-color;
          font-size: 10px;
        }

        & > div {
          width: 20em;
        }
      }
    }

    .questions {
      padding: 25px 12% 40px 7%;
      margin-top: 52px;
      width: 100%;
      background: $cloud-white;
      color: $secondary;

      .questions_body {
        margin-top: 21px;
        margin-left: 15%;
        margin-right: 13%;

        .question {
          border-bottom: 1px solid $white;
          color: $secondary;

          .question_title {
            font-family: 'DejaVu Sans Book';
            height: fit-content;
            padding: 15px 0;
            color: $secondary;

            .arrow {
              fill: $main;
              float: right;
              transition: transform 0.3s;
            }

            .question_title_text {
              font-size: 16px;
              width: 95%;
              text-align: left;
              display: inline-block;
            }
          }

          .question_answer {
            font-size: 12px;
            padding: 0 40px;
            padding-bottom: 19px;
          }
        }
      }
    }
  }

  @media screen and (max-width: $laptop-width) {
    .main_container {
      padding: 2% 0 10% 0;
      .inner_container {
        .homescreen_container {
          .form_columns {
            .form_fields {
              width: 45%;
            }
          }

          .confirmation_button {
            margin-bottom: 10px;
          }
        }

        .oasl_form {
          .confirmation_text {
            width: 40%;
          }
        }
      }
    }

    .confirmation_text_web_devices {
      left: 27%;
    }

    .info_section {
      .questions {
        padding-left: 0%;
        padding-right: 5%;
      }
    }
  }

  @media screen and (max-width: $tablet-width) {
    .main_container {
      .inner_container {
        .homescreen_container {
          .form_columns {
            width: 100%;
            display: flex;
            flex-direction: column;

            .form_info {
              width: 500px;
              text-align: center;
              margin: 20px auto;

              .form_info_text {
                font-size: 30px;
                margin-bottom: 30px;
              }

              svg {
                width: 15em;
                height: 4em;
              }
            }

            .confirmation_text {
              width: 515px;
            }

            .form_fields {
              width: 100%;
            }
          }
        }
      }
    }

    .info_section {
      .reasons_body {
        .reasons_member {
          margin: 15px auto;
        }
      }

      .questions {
        .questions_body {
          margin-left: 5%;
          margin-right: 5%;
        }
      }
    }
  }

  @media screen and (max-width: $lowres-tablet-width) {
    .main_container {
      background-position: 0;

      .inner_container {
        .homescreen_container {
          .form_columns {
            .form_info {
              width: 435px;

              .form_info_text {
                font-size: 18px;
                line-height: 1.2;
                margin-top: 20px;
              }

              svg {
                width: 13em;
              }
            }

            .confirmation_text {
              width: 435px;

              label {
                font-size: 8px;
              }
            }

            .confirmation_button {
              font-size: 20px;
              height: 47px;
              width: 212px;
              margin-bottom: 27px;
            }
          }
        }

        .oasl_form {
          .confirmation_text {
            width: 60%;
          }
        }
      }
    }

    .info_section {
      margin-top: 30px;

      .info_title {
        font-size: 24px;
      }

      .reasons_body {
        margin-top: 0;
        margin-bottom: 23px;

        .reasons_member {
          margin: 15px auto;
        }

        .reason_icon {
          min-height: 65px;
        }

        .reason_title {
          font-size: 16px;
          margin-bottom: 7px;
          margin-top: 5px;
        }

        .reason_description {
          width: auto;
          padding: 0px 15px;
        }

        & > div {
          width: 80%;
        }
      }

      .process_body {
        .process_graph {
          margin: 0;

          .arrow {
            margin: 20px 10px;
            width: 20%;
          }

          .process_icons {
            width: 10%;
          }
        }

        .process_explanation {
          flex-direction: column;

          .process_title {
            font-size: 16px;
          }

          .arrow {
            display: none;
          }

          .process_description {
            font-size: 12px;
          }

          & > div {
            width: none;
            margin: 15px auto;
          }
        }
      }

      .questions {
        padding: 25px 4% 10px 4%;
        margin-top: 15px;

        .questions_body {
          margin-left: 0;
          margin-right: 0;

          .question {
            .question_answer {
              font-size: 12px;
              padding: 0 20px;
            }

            .question_title {
              height: fit-content;
              padding: 6px 0;

              & > img {
                float: right;
                height: 15px;
              }

              .question_title_text {
                font-size: 14px;
                line-height: 20px;
              }

              svg {
                width: 8px;
                margin-top: -5px;
              }
            }
          }
        }
      }
    }
  }

  @media screen and (max-width: $mobile-width) {
    .main_container {
      .inner_container {
        flex-direction: column;
        padding: 1% 2%;

        .homescreen_container {
          form {
            :global(.server_error) {
              max-width: 300px;
            }
          }

          .address_input {
            width: 300px;
            font-size: 16px;
            margin-bottom: 5px;

            input {
              height: 38px;
            }
          }

          .loan_types_dropdown {
            width: 300px;
            margin-bottom: 5px;
            font-size: 16px;
            height: 38px;
            line-height: 22px;

            i {
              height: 36px;
              font-size: 14px;
            }

            div {
              span {
                font-size: 14px;
              }
            }
          }

          .form_columns {
            width: 100%;

            .form_info {
              width: 300px;

              .form_info_text {
                margin-top: 20px;
                margin-bottom: 20px;
              }
            }

            .form_fields {
              width: 100%;

              .form_title {
                width: 300px;
                font-size: 18px;
                text-align: center;
              }

              .document_number_title,
              .input_title {
                width: 300px;
                font-size: 12px;
                margin: 5px auto;
                padding-left: 1px;
              }

              .information_text {
                width: 300px;
                font-size: 7px;
              }

              .confirmation_text {
                width: 300px;
              }
            }
          }

          .oiql_form {
            .document_number_title {
              width: 300px;
            }

            .oiql_container {
              min-width: 300px;

              .pipe_dropdown {
                width: 300px;
                height: 38px;
                min-height: 30px;
                padding: 10px 16px;

                i {
                  padding: 10px;
                }
              }
            }

            .confirmation_text {
              width: 300px;
            }

            .information_text {
              width: 300px;
              font-size: 7px;
            }
          }

          .oasl_form {
            .document_number_title {
              width: 300px;
            }

            .oasl_container {
              min-width: 300px;

              .oasl_dropdown {
                width: 300px;
                height: 38px;
                min-height: 30px;
                padding: 10px 16px;

                i {
                  padding: 10px;
                }
              }
            }

            .information_text {
              width: 300px;
              font-size: 7px;
            }

            .confirmation_text {
              width: 300px;
            }
          }

          .bnpl_purchase_order_form {
            .document_number_title {
              width: 300px;
            }

            .bnpl_purchase_order_container {
              min-width: 300px;

              .purchase_order_amount {
                input {
                  width: 300px;
                  font-size: 22px;
                  height: 38px;
                }
              }
            }

            .confirmation_text {
              width: 300px;
            }

            .information_text {
              width: 300px;
              font-size: 7px;
            }
          }

          .ovil_order_form {
            .ovil_order_container {
              min-width: 300px;

              .order_amount {
                input {
                  width: 300px;
                  font-size: 22px;
                  height: 38px;
                }
              }

              .vehicle_details {
                .ovil_dropdown {
                  width: 300px;
                  margin-bottom: 5px;
                  font-size: 16px;
                  height: 38px;
                  line-height: 22px;

                  i {
                    height: 36px;
                    font-size: 14px;
                  }

                  div {
                    span {
                      font-size: 14px;
                    }
                  }

                  input {
                    height: 38px;
                    text-indent: 2px;
                  }
                }
              }
            }

            .confirmation_text {
              width: 300px;
            }

            .information_text {
              width: 300px;
              font-size: 7px;
            }
          }
        }
      }
    }

    .confirmation_text_web_devices {
      left: 21%;
      bottom: 7%;
    }

    .info_section {
      margin-top: 0px;

      .reasons_body {
        .reasons_member {
          margin: 15px auto;
        }
      }

      .questions {
        display: inline-block;
        margin-top: 0px;

        .questions_body {
          .question {
            .question_answer {
              font-size: 10px;
            }

            .question_title {
              .question_title_text {
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }
}
