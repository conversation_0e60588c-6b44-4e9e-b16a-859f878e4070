import React, { Component } from 'react';
import { withNamespaces } from 'react-i18next';

import { DOWNLOAD_APP_LINK, IS_MOBILE_DEVICE_WIDTH } from '../../../constants';

import Phone from '../../../svgs/phone.png';
import { ReactComponent as Download } from '../../../svgs/download.svg';
import Logo from '../../Logo';

import styles from './index.module.scss';

class AppInfo extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isMobileDevice:
        window.innerWidth < IS_MOBILE_DEVICE_WIDTH ||
        (window.innerHeight < window.innerWidth && window.innerHeight < 600),
    };
  }

  listenToResize = () => {
    this.setState({
      isMobileDevice:
        window.innerWidth < IS_MOBILE_DEVICE_WIDTH ||
        (window.innerHeight < window.innerWidth && window.innerHeight < 600),
    });
  };

  componentDidMount() {
    window.addEventListener('resize', this.listenToResize);
    window.addEventListener('orientationchange', this.listenToResize);
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this.listenToResize);
    window.removeEventListener('orientationchange', this.listenToResize);
  }

  render() {
    const { t } = this.props;

    const downloadLink = () => {
      window.open(DOWNLOAD_APP_LINK);
    };

    return (
      <div className={styles.app_info_container}>
        <div className={styles.flex_container}>
          <div className={styles.info_container}>
            <div className={styles.logo_wrapper}>
              <Logo />
            </div>
            <div className={styles.text_btn_container}>
              <p className={styles.title}>{t('info_modal.info_text')}</p>
              <div className={styles.btn_container} onClick={downloadLink}>
                <div className={styles.download_btn}>
                  <Download width={30} height={30} />
                </div>
                <p className={styles.btn_text}>
                  {this.state.isMobileDevice
                    ? t('info_modal.info_short_download_now')
                    : t('info_modal.info_download_now')}
                </p>
              </div>
            </div>
          </div>
          <div className={styles.image_container}>
            <img src={Phone} className={styles.image} alt="" />
          </div>
        </div>
        <div className={styles.footer}>
          <p className={styles.footerText}>
            {t('info_modal.footer_text')} <br />
            <a
              className={styles.footerLink}
              href={t('info_modal.global_credit_link')}
              target="_blank"
              rel="noopener noreferrer"
            >
              {t('info_modal.link_name')}
            </a>
          </p>
        </div>
      </div>
    );
  }
}

export default withNamespaces('translations')(AppInfo);
