@import '../../../styles/colors';
@import '../../../styles/sizes';
@import '../../../styles/mixins';

:global {
  .ui.modals.dimmer {
    padding: 0 !important;

    .content {
      background: rgba(255, 255, 255, 0);
      padding: 0 !important;
      line-height: 1;
    }
  }

  #root {
    height: -webkit-fill-available;
  }
}

#pay4me_modal {
  width: 100%;
  overflow-y: hidden;
  overflow-x: hidden;
  height: -webkit-fill-available;
  color: $white;
  padding: 0;
  margin: 0;
  position: relative;
  background: $main;
  background-image: url('../../../svgs/pay4me-background.jpg');
  background-position: 0 34%;
  background-repeat: no-repeat;
  background-size: cover;

  #pay4me_content {
    top: 0;
    left: 500px;
    width: 100%;
  }

  .close {
    width: 20px;
    height: 20px;
    position: absolute;
    cursor: pointer;
    top: 20px;
    right: 20px;

    path {
      fill: $white;
    }
  }

  .modal_container {
    display: flex;
    flex-direction: column;
    background: transparent
      linear-gradient(270deg, rgba(143, 19, 10, 0) 25%, $red 100%) 0% 0%
      no-repeat padding-box;
    height: 100%;

    .info_content {
      height: 100%;
      width: 80%;
      padding: 95px 0 0 75px;
      position: relative;

      .pay4me_logo {
        width: 280px;
      }

      .title {
        margin-top: 31px;
        display: flex;
        flex-direction: column;
        line-height: 35px;

        .limit {
          font-size: 25px;
          display: inline-block;
          text-transform: uppercase;
        }

        .limit_number {
          font-size: 50px;
          display: inline-block;
          font-weight: bold;
        }
      }

      .apply_btn {
        @include pay4me_modal_button;
      }

      .transfer_text {
        width: 520px;
        margin: 20px 0;
        color: $white;
        font-size: 18px;
        font-weight: bold;
      }
    }

    .step_content {
      background: transparent
        linear-gradient(272deg, rgba(245, 0, 0, 0) 0%, $dark-red 100%) 0% 0%
        no-repeat padding-box;
      width: 100%;
      bottom: 48px;

      .description .description_blocks {
        float: left;
        margin-top: 3px;
        padding: 20px 3%;
        border-right: 4px dotted $white;

        p {
          margin: 0;
        }
      }

      .description .description_blocks:last-child {
        border: none;
      }

      .description .step_number {
        font: normal normal bold 26px/25px Montserrat;
        text-align: left;
        color: $white;
      }

      .description .steps {
        font: normal normal bold 18px/25px Montserrat;
        margin-left: 12px;
      }

      .description .text {
        text-align: left;
        font: normal normal normal 19px/33px Montserrat;
        color: $white;
      }
    }
  }
}

@media screen and (orientation: portrait) {
  #pay4me_modal {
    background-position: 50% -47px;

    .modal_container .info_content .apply_btn {
      position: absolute;
      margin-top: 290px;
      padding: 45px 0 50px 20px;
    }

    .modal_container .step_content {
      background: none;
      height: 100%;

      .description .description_blocks {
        float: none;
        border-left: 4px dotted $white;
        border-right: none;
        margin: 0 70px 4px 2px;
        padding: 1px 30px;
        height: 60px;

        p {
          margin: 0;
          padding: 0;
          margin-left: 20px;
          font-size: 36px;
          display: inline-block;
        }
      }

      .description .description_blocks:last-child {
        border-left: 4px dotted $white;
      }
    }
  }

  @media screen and (max-width: $tablet-width) {
    #pay4me_modal .modal_container .info_content {
      .apply_btn {
        padding: 30px;
        width: 360px;
        font-size: 30px;
      }

      .transfer_text {
        font-size: 24px;
      }
    }

    #pay4me_modal .modal_container .step_content .description {
      margin-left: 70px;
      font: normal normal normal 26px/25px Montserrat;
    }
  }

  @media screen and (max-width: $lowres-tablet-width) {
    #pay4me_modal {
      background-position: 65% -47px;

      .modal_container .info_content {
        padding: 45px 0 50px 20px;

        .pay4me_logo {
          width: 150px;
        }

        .title {
          margin-top: 10px;

          .limit {
            font-size: 15px;
          }

          .limit_number {
            font-size: 30px;
          }
        }

        .apply_btn {
          margin-top: 100px;
          width: 200px;
          padding: 10px;
          font-size: 13px;
        }

        .transfer_text {
          font-size: 13px;
          width: 300px;
          margin-top: 10px;
        }
      }

      .modal_container .step_content {
        margin-left: 5px;

        .description {
          margin-left: 10px;

          .description_blocks {
            margin: 2px 0 0 10px;
            height: 35px;
            bottom: 135px;
            padding: 0;

            p {
              font-size: 23px;
            }

            .step_number {
              font: normal normal bold 15px/15px Montserrat;
            }

            .steps {
              font: normal normal bold 16px/15px Montserrat;
            }

            .text {
              font: normal normal normal 16px/15px Montserrat;
            }
          }
        }
      }
    }
  }

  @media screen and (max-width: 320px) {
    #pay4me_modal {
      background-position: 65% -47px;

      .modal_container .info_content {
        padding: 18px 0 48px 20px;

        .pay4me_logo {
          width: 120px;
        }

        .title {
          margin-top: 0;
        }

        .apply_btn {
          margin-top: 125px;
        }

        .transfer_text {
          width: 265px;
        }
      }

      .modal_container .step_content .description .description_blocks {
        margin: 0 0 3px 10px;
        height: 30px;

        p {
          margin-left: 10px;
        }
      }
    }
  }
}

@media (orientation: landscape) {
  #pay4me_modal .modal_container .step_content .description {
    display: flex;
    justify-content: space-around;

    .description_blocks {
      padding: 15px 4% 15px 0;
    }
  }
  @media screen and (max-height: 768px) {
    #pay4me_modal .modal_container .info_content {
      padding: 41px 0 0 75px;

      .pay4me_logo {
        width: 170px;
      }

      .title {
        margin-top: 0;
        line-height: 42px;

        .limit {
          font-size: 14px;
        }

        .limit_number {
          font-size: 25px;
        }
      }

      .apply_btn {
        padding: 10px;
        font-size: 13px;
        margin-top: 10px;
        width: 170px;
      }

      .transfer_text {
        font-size: 13px;
        width: 370px;
      }
    }

    #pay4me_modal .modal_container .step_content .description {
      .description_blocks {
        padding: 3px 2% 0 3px;

        .step_number {
          font: normal normal bold 13px/20px Montserrat;
        }

        .steps {
          font: normal normal bold 13px/20px Montserrat;
        }

        .text {
          font: normal normal normal 11px/20px Montserrat;
        }
      }
    }
  }

  @media screen and (max-height: 410px) {
    #pay4me_modal .modal_container .info_content {
      padding: 10px 0 0 30px;

      .pay4me_logo {
        width: 110px;
      }

      .title {
        margin-top: 0;
        line-height: 20px;

        .limit {
          font-size: 12px;
        }

        .limit_number {
          font-size: 22px;
        }
      }

      .apply_btn {
        padding: 8px;
        font-size: 11px;
        margin-top: 5px;
        width: 200px;
      }

      .transfer_text {
        font-size: 11px;
        width: 370px;
        margin: 10px 0;
      }
    }

    #pay4me_modal
      .modal_container
      .step_content
      .description
      .description_blocks {
      padding: 0px 9px 0px 0;

      .step_number {
        font: normal normal bold 13px/20px Montserrat;
      }

      .steps {
        font: normal normal bold 13px/20px Montserrat;
      }

      .text {
        font: normal normal normal 11px/20px Montserrat;
      }
    }
  }
}
