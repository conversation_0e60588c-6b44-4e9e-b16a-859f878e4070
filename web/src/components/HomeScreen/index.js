import React, { Component } from 'react';
import { Segment } from 'semantic-ui-react';
import { isMobileOnly } from 'react-device-detect';
import classnames from 'classnames';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { Trans } from 'react-i18next';

import LoanForm from './Components/LoanForm';
import LoanFormOIQL from './Components/LoanFormOIQL';
import LoanFormOASL from './Components/LoanFormOASL';
import PurchaseOrderFormBNPL from './Components/PurchaseOrderBNPL/PurchaseOrderFormBNPL';
import OVILOrderForm from './Components/LoanApplicationOrderOVIL/OrderFormOVIL';
import ChoosingReasons from './Components/ChoosingReasons';
import WorkingProcess from './Components/WorkingProcess';
import Questions from './Components/Questions';
import InfoBlock from './InfoBlock';
import InfoModal from './InfoModal';
import APRInformation from './Components/APRInformation';
import BonusModal from './BonusModal';
import GetReferralCodeModal from '../ReferralCode/GetReferralCodeModal';
import LuyserModal from './LuyserModal';
import BanaliModal from './BanaliModal';
import Pay4meModal from './Pay4meModal';
import CashMeLoader from '../CashMeLoader';
import {
  isOIQL,
  isOASL,
  isProd,
  getLoanType,
  getQrToken,
  issetReferralCode,
  isBanali,
  getReToken,
  isBrowserAllowedLoanType,
  isDesktopDeviceBrowser,
  setSessionStorageItem,
  getPay4meToken,
  isSourcePay4me,
  isBNPL,
  isOVL,
} from '../../helpers/common';
import {
  HOME_SCREEN_PDFS,
  IS_BANALI,
  IS_LUYSER,
  IS_PAY4ME,
  LOAN_TYPES,
} from '../../constants';

import { fetchDiscount } from '../../redux/ducks/citizen';
import { fetchRealEstateDeveloperCompany } from '../../redux/ducks/realEstateDeveloperCompanies';
import { fetchPay4me } from '../../redux/ducks/pay4me';

import styles from './index.module.scss';

class HomeScreen extends Component {
  constructor(props) {
    super(props);

    this.reToken = getReToken();
    this.pay4meToken = getPay4meToken();

    this.state = {
      showBonusModal: false,
      showReferralCodeModal: false,
      showLuyserModal: false,
      showBanaliModal: false,
      showPay4meModal: false,
      developerCompanyId: null,
      isPay4me: false,
    };
  }

  componentDidMount() {
    const {
      fetchDiscount,
      fetchRealEstateDeveloperCompany,
      fetchPay4me,
    } = this.props;
    const token = getQrToken();

    if (this.reToken) {
      fetchRealEstateDeveloperCompany({ token: this.reToken });
    }

    if (isSourcePay4me() && this.pay4meToken) {
      fetchPay4me({ token: this.pay4meToken });
    }

    if (token) {
      const loanType = getLoanType();

      fetchDiscount({
        token,
        loan_type_id: loanType || LOAN_TYPES.COMMON.id,
      });
    }

    if (isBanali()) {
      this.setState({ showBanaliModal: true });
      setSessionStorageItem(IS_BANALI, true);
    }

    if (issetReferralCode()) {
      this.setState({
        showReferralCodeModal: true,
      });
    }
  }

  componentDidUpdate(prevProps, prevState) {
    const { discount, realEstateDeveloperCompanies, pay4me } = this.props;
    const { showLuyserModal, showPay4meModal } = this.state;

    if (discount && prevProps.discount !== discount) {
      this.setState({ showBonusModal: !!discount });
    }

    if (showLuyserModal !== prevState.showLuyserModal) {
      const rootElm = document.getElementById('root');

      showLuyserModal === true
        ? rootElm.classList.add('disable-scrolling')
        : rootElm.classList.remove('disable-scrolling');
    }

    if (showPay4meModal !== prevState.showPay4meModal) {
      const rootElm = document.getElementById('root');

      showPay4meModal === true
        ? rootElm.classList.add('disable-scrolling')
        : rootElm.classList.remove('disable-scrolling');
    }

    if (prevProps.pay4me !== pay4me && !pay4me.loading && pay4me.data) {
      this.setState(
        {
          isPay4me: pay4me.data.isPay4me,
        },
        () => {
          if (this.isPay4me()) {
            this.setState({ showPay4meModal: true });
            setSessionStorageItem(IS_PAY4ME, true);
          }
        }
      );
    }

    if (
      prevProps.realEstateDeveloperCompanies !== realEstateDeveloperCompanies &&
      !realEstateDeveloperCompanies.loading &&
      realEstateDeveloperCompanies.data
    ) {
      this.setState(
        {
          developerCompanyId: realEstateDeveloperCompanies.data.companyId,
        },
        () => {
          if (this.isLuyser()) {
            this.setState({ showLuyserModal: true });
            setSessionStorageItem(IS_LUYSER, true);
          }
        }
      );
    }
  }

  closeBonusModal = () => {
    this.setState({
      showBonusModal: false,
    });
  };

  closeLuyserModal = () => {
    this.setState({
      showLuyserModal: false,
    });
  };

  closeBanaliModal = () => {
    this.setState({
      showBanaliModal: false,
    });
  };

  closePay4meModal = () => {
    this.setState({
      showPay4meModal: false,
    });
  };

  getForm = () => {
    if (isOIQL()) {
      return <LoanFormOIQL />;
    } else if (isOASL()) {
      return <LoanFormOASL />;
    } else if (isBNPL()) {
      return <PurchaseOrderFormBNPL />;
    } else if (isOVL()) {
      return <OVILOrderForm />;
    }

    return (
      <LoanForm
        isLuyser={this.isLuyser()}
        isPay4me={this.isPay4me()}
        developerCompanyId={this.state.developerCompanyId}
      />
    );
  };

  getMainContainerClassnames = () => {
    if (isProd() && isDesktopDeviceBrowser() && !isBrowserAllowedLoanType()) {
      return classnames(styles.main_container, styles.web_device);
    }

    return styles.main_container;
  };

  agreementDesktop = () => {
    if (isProd() && isDesktopDeviceBrowser() && !isBrowserAllowedLoanType()) {
      return (
        <div className={styles.confirmation_text_web_devices}>
          <Trans
            defaults={`document_number.confirmation_text_web_devices`}
            components={[
              <a
                target="_blank"
                rel="noopener noreferrer"
                href={HOME_SCREEN_PDFS.CASHME}
              >
                text
              </a>,
            ]}
          />
        </div>
      );
    }
  };

  isLuyser() {
    const { realEstateDeveloperCompanies } = this.props;
    if (
      !realEstateDeveloperCompanies.loading &&
      realEstateDeveloperCompanies.data
    ) {
      return (
        this.state.developerCompanyId === LOAN_TYPES.REML.PREML.PREML_LUYSER.id
      );
    }
  }

  isPay4me() {
    const { pay4me } = this.props;

    if (!pay4me.loading && pay4me.data) {
      return this.state.isPay4me;
    }
  }

  isLoading() {
    const { loading, realEstateDeveloperCompanies, pay4me } = this.props;

    return loading || realEstateDeveloperCompanies.loading || pay4me.loading;
  }

  render() {
    const { discount } = this.props;
    const {
      showBonusModal,
      showReferralCodeModal,
      showLuyserModal,
      showBanaliModal,
      showPay4meModal,
    } = this.state;

    return (
      <Segment id={styles.home_screen} vertical>
        <CashMeLoader loading={this.isLoading()} />

        <InfoBlock />
        <InfoModal />
        {!showBonusModal &&
          !this.isLuyser() &&
          !isBanali() &&
          !isBNPL() &&
          !isOVL() && <APRInformation />}
        <BonusModal
          closeModal={this.closeBonusModal}
          open={showBonusModal}
          discount={discount}
        />

        <LuyserModal
          closeModal={this.closeLuyserModal}
          open={showLuyserModal}
          reToken={this.reToken}
        />

        <BanaliModal
          closeModal={this.closeBanaliModal}
          open={showBanaliModal}
        />

        <Pay4meModal
          closeModal={this.closePay4meModal}
          open={showPay4meModal}
        />

        {showReferralCodeModal && <GetReferralCodeModal />}

        <div className={this.getMainContainerClassnames()}>
          <div className={styles.inner_container}>{this.getForm()}</div>

          {this.agreementDesktop()}
        </div>

        {!isBNPL() &&
          !isOVL() && (
            <div className={styles.info_section}>
              {!isMobileOnly && <ChoosingReasons />}
              {!isMobileOnly && <WorkingProcess />}
              <Questions />
            </div>
          )}
      </Segment>
    );
  }
}

function mapStateToProps(state) {
  const {
    citizen: { discount, loading },
    realEstateDeveloperCompanies,
    pay4me,
  } = state;

  return { discount, loading, realEstateDeveloperCompanies, pay4me };
}

const mapDispatchToProps = dispatch => {
  return {
    fetchDiscount: token => dispatch(fetchDiscount(token)),
    fetchPay4me: token => dispatch(fetchPay4me(token)),
    fetchRealEstateDeveloperCompany: loanTypeId =>
      dispatch(fetchRealEstateDeveloperCompany(loanTypeId)),
  };
};

export default compose(
  connect(
    mapStateToProps,
    mapDispatchToProps
  )
)(HomeScreen);
