import React, { Component } from 'react';
import { withNamespaces } from 'react-i18next';

import styles from '../index.module.scss';

import money_icon from '../../../svgs/money-icon.svg';
import blank_icon from '../../../svgs/blank-icon.svg';
import calendar_icon from '../../../svgs/calendar-icon.svg';
import laptop_icon from '../../../svgs/laptop-icon.svg';

class ChoosingReasons extends Component {
  render() {
    const { t } = this.props;

    return (
      <div>
        <span className={styles.info_title}>
          {t('document_number.why_choose_us')}
        </span>
        <div className={styles.reasons_body}>
          <div className={styles.reasons_member}>
            <img
              src={money_icon}
              alt="money_icon"
              className={styles.reason_icon}
            />
            <div className={styles.reason_title}>
              {t('document_number.short_reason_1')}
            </div>
            <div className={styles.reason_description}>
              {t('document_number.reason_1')}
            </div>
          </div>
          <div className={styles.reasons_member}>
            <img
              src={blank_icon}
              alt="blank_icon"
              className={styles.reason_icon}
            />
            <div className={styles.reason_title}>
              {t('document_number.short_reason_2')}
            </div>
            <div className={styles.reason_description}>
              {t('document_number.reason_2')}
            </div>
          </div>
          <div className={styles.reasons_member}>
            <img
              src={calendar_icon}
              alt="calendar_icon"
              className={styles.reason_icon}
            />
            <div className={styles.reason_title}>
              {t('document_number.short_reason_3')}
            </div>
            <div className={styles.reason_description}>
              {t('document_number.reason_3')}
            </div>
          </div>
          <div className={styles.reasons_member}>
            <img
              src={laptop_icon}
              alt="laptop_icon"
              className={styles.reason_icon}
            />
            <div className={styles.reason_title}>
              {t('document_number.short_reason_4')}
            </div>
            <div className={styles.reason_description}>
              {t('document_number.reason_4')}
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default withNamespaces('translations')(ChoosingReasons);
