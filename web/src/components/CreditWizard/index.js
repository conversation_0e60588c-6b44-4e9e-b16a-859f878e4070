import React, { Component } from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import { withNamespaces } from 'react-i18next';
import { Wizard, Steps, Step } from 'react-albus';
import { compose } from 'redux';
import querystring from 'query-string';
import classnames from 'classnames';

import WizardError from './WizardError';
import { EXPIRED_SUUID_ERROR_CODE, LOAN_TYPES } from '../../constants';
import { isOIQL, isOASL, isREML, getLoanType } from '../../helpers/common';
import { wizardErrorClear } from '../../redux/ducks/wizard';
import LoanAmountStep from '../LoanScreen/Components/LoanAmountStep';
import LoanAmountStepOIQL from '../LoanScreen/Components/LoanAmountStep/LoanAmountStepOIQL';
import LoanAmountStepOASL from '../LoanScreen/Components/LoanAmountStep/LoanAmountStepOASL';
import RealEstateDetails from '../LoanScreen/Components/RealEstateDetailsStep';
import LoanAmountStepREML from '../LoanScreen/Components/LoanAmountStep/LoanAmountStepREML';
import PersonalInfoStep from '../LoanScreen/Components/PersonalInfoStep';
import SmsValidationStep from '../LoanScreen/Components/SmsValidationStep';
import TransferOptionsStep from '../LoanScreen/Components/TransferOptionsStep';
import RealEstateTransferStep from '../LoanScreen/Components/RealEstateTransferStep';
import CarVerificationStep from '../LoanScreen/Components/CarVerificationStep';

import styles from './index.module.scss';

const LOAN_AMOUNT_STEP = {
  id: 'loan-amount',
  description: 'loan.steps.loan_amount.description',
  previous: 'loan.steps.loan_amount.previous',
  content: props => {
    return getAmountStepComponent(props);
  },
  next: 'loan.steps.loan_amount.next',
};
const PERSONAL_INFO_STEP = {
  id: 'personal-info',
  description: 'loan.steps.personal_info.description',
  content: props => {
    return <PersonalInfoStep {...props} />;
  },
  next: 'loan.steps.personal_info.next',
  previous: 'loan.steps.personal_info.previous',
};
const TRANSFER_OPTIONS_STEP = {
  id: 'transfer-options',
  description: 'loan.steps.transfer_options.description',
  content: props => {
    return <TransferOptionsStep {...props} />;
  },
  next: 'loan.steps.transfer_options.next',
  previous: 'loan.steps.transfer_options.previous',
};
const REAL_ESTATE_TRANSFER_OPTIONS_STEP = {
  id: 'transfer-options',
  description: 'loan.steps.transfer_options.description',
  content: props => {
    return <RealEstateTransferStep {...props} />;
  },
  next: 'loan.steps.transfer_options.next',
  previous: 'loan.steps.transfer_options.previous',
};
const REAL_ESTATE_DETAILS_STEP = {
  id: 'real-estate-details',
  description: 'loan.steps.real_estate_loan.wizard_description',
  previous: 'loan.steps.real_estate_loan.previous',
  content: props => {
    return <RealEstateDetails {...props} />;
  },
  next: 'loan.steps.real_estate_loan.next',
};
const SMS_VALIDATION_STEP = {
  id: 'sms-validation',
  description: 'loan.steps.sms_validation.description',
  content: props => {
    return <SmsValidationStep {...props} />;
  },
  next: 'loan.steps.sms_validation.next',
  previous: 'loan.steps.sms_validation.previous',
};
const CAR_VERIFICATION_STEP = {
  id: 'car-verification',
  description: 'loan.steps.car_verification.description',
  content: props => {
    return <CarVerificationStep {...props} />;
  },
  next: 'loan.steps.car_verification.next',
  previous: 'loan.steps.car_verification.previous',
};

const getAmountStepComponent = props => {
  if (isOIQL()) {
    return <LoanAmountStepOIQL {...props} />;
  } else if (isOASL()) {
    return <LoanAmountStepOASL {...props} />;
  } else if (isREML()) {
    return <LoanAmountStepREML {...props} />;
  }

  return <LoanAmountStep {...props} />;
};

class CreditWizard extends Component {
  constructor(props) {
    super(props);

    this.stepContainers = this.composeSteps();
  }

  componentDidMount() {
    const {
      history,
      match: { url },
      onChange,
      wizardErrorClear,
    } = this.props;

    wizardErrorClear();

    const step = this.pathToStep(this.stepContainers, history, url);

    if (!step) {
      this.redirectHome();
    }

    onChange(step);

    window.addEventListener('scroll', this.listenToScroll);
  }

  componentDidUpdate() {
    const {
      wizard: { error },
    } = this.props;

    if (error && error.code === EXPIRED_SUUID_ERROR_CODE) {
      this.redirectHome();
    }
  }

  componentWillUnmount() {
    window.removeEventListener('scroll', this.listenToScroll);
  }

  composeSteps = () => {
    let steps;
    switch (getLoanType()) {
      case LOAN_TYPES.REML.id:
        steps = [
          REAL_ESTATE_DETAILS_STEP,
          LOAN_AMOUNT_STEP,
          PERSONAL_INFO_STEP,
          REAL_ESTATE_TRANSFER_OPTIONS_STEP,
          SMS_VALIDATION_STEP,
        ];
        break;
      case LOAN_TYPES.OVL.id:
        steps = [
          LOAN_AMOUNT_STEP,
          PERSONAL_INFO_STEP,
          CAR_VERIFICATION_STEP,
          TRANSFER_OPTIONS_STEP,
          SMS_VALIDATION_STEP,
        ];
        break;
      case LOAN_TYPES.OBL.id:
        steps = [LOAN_AMOUNT_STEP, PERSONAL_INFO_STEP, SMS_VALIDATION_STEP];
        break;
      default:
        steps = [
          LOAN_AMOUNT_STEP,
          PERSONAL_INFO_STEP,
          TRANSFER_OPTIONS_STEP,
          SMS_VALIDATION_STEP,
        ];
    }

    return steps;
  };

  redirectHome = () => {
    const { history, wizardErrorClear } = this.props;

    history.push('/home');
    wizardErrorClear();
  };

  onClose = () => {
    this.props.wizardErrorClear();
  };

  listenToScroll = () => {
    const wizardContainer = document.getElementsByClassName(
      styles.wizard_container
    );
    const wizardPage = document.getElementsByClassName(styles.wizard_page);

    if (window.scrollY > 165) {
      wizardContainer[0].style.position = 'fixed';
      wizardPage[0].style.marginTop = '58px';
    } else {
      wizardContainer[0].style.position = 'relative';
      wizardPage[0].style.marginTop = '0px';
    }
  };

  pathToStep(stepContainers, history, url) {
    const pathname = history.location.pathname;
    const id = pathname.replace(url + '/', '');
    const [step] = stepContainers.filter(s => s.id === id);

    return step;
  }

  isActive(active, step, steps) {
    return steps.indexOf(step) === steps.indexOf(active);
  }

  isPrevious(active, step, steps) {
    return steps.indexOf(step) < steps.indexOf(active);
  }

  onBreadcrumbClick = (step, s, steps, history, url) => {
    if (this.isPrevious(step, s, steps)) {
      this.goTo(history, s, url, null);
    }
  };

  goTo = (history, step, url, params) => {
    this.push(history, step, url, params);
    this.props.onChange(step);
  };

  push(history, step, url, params) {
    const query = params
      ? `?${querystring.stringify(params)}`
      : history.location.search;
    const path = `${url}/${step.id}${query}`;
    history.push(path);
  }

  render() {
    const {
      t,
      history,
      match: { url },
      wizard: { error },
    } = this.props;

    const wizardStyles = (step, s, steps) =>
      classnames({
        [styles.active]: this.isActive(step, s, steps),
        [styles.previous]: this.isPrevious(step, s, steps),
      });

    return (
      <div>
        <Wizard
          history={history}
          basename={url}
          render={({ step, steps }) => (
            <div>
              <div className={styles.wizard_container}>
                <div className={styles.wizard}>
                  {steps.map((s, index) => {
                    const { id } = s;
                    const stepConfig = this.stepContainers.find(
                      c => c.id === id
                    );
                    return (
                      <a
                        href="#wizard"
                        className={wizardStyles(step, s, steps)}
                        key={id}
                        onClick={e => {
                          e.preventDefault();
                          this.onBreadcrumbClick(step, s, steps, history, url);
                        }}
                      >
                        <div className={styles.outer_arrow} />
                        <div className={styles.inner_content}>
                          <div className={styles.title}>
                            {t('loan.steps.title', [index + 1])}
                          </div>
                          <div className={styles.title_description}>
                            {t(stepConfig.description)}
                          </div>
                        </div>
                      </a>
                    );
                  })}
                </div>
                <WizardError error={error} onClose={this.onClose} />
              </div>
              <div className={styles.wizard_page}>
                <Steps step={step.id && step}>
                  {this.stepContainers.map(s => {
                    const { id } = s;
                    const StepContent = s.content;

                    const wizardBag = {
                      goNext: params => {
                        const index = this.stepContainers.indexOf(s);
                        const nextStep = this.stepContainers[index + 1];

                        this.goTo(history, nextStep, url, params);
                      },
                      goPrevious: params => {
                        const index = this.stepContainers.indexOf(s);
                        const previousStep = this.stepContainers[index - 1];

                        this.goTo(history, previousStep, url, params);
                      },
                      config: this.stepContainers,
                      step: s,
                    };

                    return (
                      <Step id={id} key={id}>
                        <StepContent wizardBag={wizardBag} />
                      </Step>
                    );
                  })}
                </Steps>
              </div>
            </div>
          )}
        />
      </div>
    );
  }
}

function mapStateToProps(state) {
  const { wizard } = state;

  return { wizard };
}

const mapDispatchToProps = dispatch => {
  return {
    wizardErrorClear: () => dispatch(wizardErrorClear()),
  };
};

export default compose(
  withRouter,
  connect(
    mapStateToProps,
    mapDispatchToProps
  ),
  withNamespaces('translations')
)(CreditWizard);
