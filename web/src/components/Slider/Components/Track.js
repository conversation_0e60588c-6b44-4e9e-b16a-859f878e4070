import React from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import { withNamespaces } from 'react-i18next';
import Currency from '../../Currency';
import { FRACTION_NO } from '../../../constants';
import { ReactComponent as CarIcon } from '../../../svgs/car.svg';

import styles from '../index.module.scss';

const Track = ({
  t,
  index,
  allowedMax,
  oclValue,
  toggleAmount,
  target,
  getTrackProps,
  domain: [min, max],
  realMax,
}) => {
  const classNames = classnames({
    [styles.slider__track_disabled]: allowedMax && index === 2,
    [styles.slider__track]: true,
    [styles.slider__hide_track]: !!toggleAmount,
  });

  let trackPercent = Math.floor(target.percent);
  let maxPercent = Math.floor((allowedMax - min) / ((realMax - min) / 100));
  const vehiclePercent =
    toggleAmount &&
    Math.floor((realMax - oclValue) / ((realMax - min) / 100) + 1);

  if (allowedMax && target.value > allowedMax) {
    trackPercent = maxPercent;
  }

  if (allowedMax === min) {
    maxPercent = 0;
    trackPercent = 0;
  }

  if (min === max) {
    maxPercent = 100;
    trackPercent = 100;
  }

  const togglePercent = 100 - vehiclePercent;

  return (
    <div>
      <div
        className={styles.slider__track_available}
        style={{
          width: `${maxPercent}%`,
        }}
      />
      <div
        className={styles.slider__track_available_line}
        style={{
          width: `${maxPercent}%`,
        }}
      />
      {toggleAmount && (
        <div>
          <div
            className={styles.toggle_amount}
            style={{
              left: `${togglePercent}%`,
            }}
          >
            <Currency
              hasSign={true}
              value={toggleAmount}
              precision={FRACTION_NO}
            />
          </div>
          <div
            className={styles.border_flag}
            style={{
              left: `${togglePercent + 1}%`,
            }}
          >
            <div className={styles.border_flag_top} />
          </div>
          <div
            className={styles.slider__track_vehicle_mortgage}
            style={{
              width: `${vehiclePercent - 1}%`,
              left: `${togglePercent + 1}%`,
            }}
          >
            <div className={styles.slider__track_vehicle_mortgage_text}>
              <CarIcon />
              {t('document_number.mortgage_loan')}
            </div>
          </div>
          <div
            className={styles.slider__track_vehicle_mortgage_track}
            style={{
              width: `${vehiclePercent}%`,
              left: `${togglePercent + 1}%`,
            }}
            {...getTrackProps()}
          />
        </div>
      )}
      <div
        className={classNames}
        style={{
          width: `${trackPercent}%`,
        }}
        {...getTrackProps()}
      />
    </div>
  );
};

Track.propTypes = {
  allowedMax: PropTypes.number,
  target: PropTypes.shape({
    id: PropTypes.string.isRequired,
    value: PropTypes.number.isRequired,
    percent: PropTypes.number.isRequired,
  }).isRequired,
  getTrackProps: PropTypes.func.isRequired,
};

export default withNamespaces('translations')(Track);
