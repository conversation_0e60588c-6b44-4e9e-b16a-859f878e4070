import PropTypes from 'prop-types';
import React from 'react';
import { connect } from 'react-redux';
import DesktopContainer from './DesktopContainer';
import { logout } from '../../redux/ducks/login';

const ResponsiveContainer = ({ children, logout, isAuthenticated }) => {
  return (
    <div>
      <DesktopContainer isAuthenticated={isAuthenticated} logout={logout}>
        {children}
      </DesktopContainer>
    </div>
  );
};

ResponsiveContainer.propTypes = {
  isAuthenticated: PropTypes.bool,
  logout: PropTypes.func,
  children: PropTypes.node,
};

const mapStateToProps = state => ({
  isAuthenticated: state.login.isAuthenticated,
});

const mapDispatchToProps = dispatch => {
  return {
    logout: () => dispatch(logout()),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(ResponsiveContainer);
