import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { Menu, Popup, Responsive, Segment } from 'semantic-ui-react';
import { withNamespaces } from 'react-i18next';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';

import { isOIQL, isOASL, isBNPL, isOVL } from '../../helpers/common';
import Footer from '../Footer';
import RedirectHomeConfirmModal from './RedirectHomeConfirmModal';
import { DISABLED_REDIRECT_PATHS } from '../../constants';

import { fetchMerchantDetails } from '../../redux/ducks/purchaseOrder';
import { fetchMerchantDetails as fetchMerchantOVILDetails } from '../../redux/ducks/loanApplicationOrder';
import { fetchServerTime } from '../../redux/ducks/appConfigs';

import Logo from '../../svgs/cash_me.svg';

import styles from './index.module.scss';

class DesktopContainer extends Component {
  menuHomeBNPLIndex = 0;
  menuPurchaseOrderBNPLIndex = 1;
  menuHomeOVILIndex = 2;
  menuOVILOrderIndex = 3;

  constructor(props) {
    super(props);

    this.state = {
      open: false,
      activeMenuIndex: this.getActiveMenuIndex(),
    };
  }

  componentDidMount() {
    this.props.fetchServerTime();
    if (isBNPL()) {
      this.props.fetchMerchantDetails();
    } else if (isOVL()) {
      this.props.fetchMerchantOVILDetails();
    }
  }

  componentDidUpdate(prevProps) {
    if (isBNPL() || isOVL()) {
      if (this.props.location.pathname !== prevProps.location.pathname) {
        this.setState({ activeMenuIndex: this.getActiveMenuIndex() });
      }
    }
  }

  handleToggleConfirmModal = () => {
    if (DISABLED_REDIRECT_PATHS.includes(window.location.pathname)) {
      this.handleConfirm();
    } else {
      this.setState({
        open: !this.state.open,
      });
    }
  };

  handleConfirm = () => {
    window.location.href = '/home';
  };

  handleMenuItemClick = (e, { path }) => {
    this.props.history.push(path);
  };

  getActiveMenuIndex = () => {
    let activeIndex = null;

    if (isBNPL()) {
      if (window.location.pathname.includes('/home')) {
        activeIndex = this.menuHomeBNPLIndex;
      } else if (window.location.pathname.includes('bnpl/purchase-orders')) {
        activeIndex = this.menuPurchaseOrderBNPLIndex;
      }
    } else if (isOVL()) {
      if (window.location.pathname.includes('/home')) {
        activeIndex = this.menuHomeOVILIndex;
      } else if (window.location.pathname.includes('ovil/orders')) {
        activeIndex = this.menuOVILOrderIndex;
      }
    }

    return activeIndex;
  };

  render() {
    const {
      children,
      isAuthenticated,
      logout,
      merchantDetailsBNPL,
      merchantDetailsOVIL,
      t,
    } = this.props;
    const { activeMenuIndex } = this.state;

    return (
      <Responsive id={styles.article}>
        <Segment
          id={styles.header}
          textAlign="center"
          style={{ padding: '0' }}
          vertical
        >
          <Menu
            pointing={true}
            secondary={true}
            size="large"
            className={styles.header__secondary_menu}
          >
            <Menu.Item
              header
              className={styles.header__logo}
              onClick={this.handleToggleConfirmModal}
            >
              <img className={styles.logo} src={Logo} alt="Logo" />
            </Menu.Item>
            {isAuthenticated && isBNPL() ? (
              <>
                <Menu.Item
                  name={t('bnpl.purchase_order.create')}
                  onClick={this.handleMenuItemClick}
                  className={styles.action_button}
                  index={this.menuHomeBNPLIndex}
                  active={activeMenuIndex === this.menuHomeBNPLIndex}
                  path={'/home'}
                />
                <Menu.Item
                  name={t('bnpl.purchase_order.view_list')}
                  onClick={this.handleMenuItemClick}
                  className={styles.action_button}
                  index={this.menuPurchaseOrderBNPLIndex}
                  active={activeMenuIndex === this.menuPurchaseOrderBNPLIndex}
                  path={'/bnpl/purchase-orders'}
                />

                {merchantDetailsBNPL && (
                  <div className={styles.merchant_details}>
                    <Popup
                      trigger={
                        <img
                          className={styles.logo}
                          src={merchantDetailsBNPL.icon}
                          alt="Logo"
                        />
                      }
                      header={merchantDetailsBNPL.name}
                    />
                  </div>
                )}
              </>
            ) : isOVL() ? (
              <>
                <Menu.Item
                  name={t('bnpl.purchase_order.create')}
                  onClick={this.handleMenuItemClick}
                  className={styles.action_button}
                  index={this.menuHomeOVILIndex}
                  active={activeMenuIndex === this.menuHomeOVILIndex}
                  path={'/home'}
                />
                <Menu.Item
                  name={t('bnpl.purchase_order.view_list')}
                  onClick={this.handleMenuItemClick}
                  className={styles.action_button}
                  index={this.menuOVILOrderIndex}
                  active={activeMenuIndex === this.menuOVILOrderIndex}
                  path={'/ovil/orders'}
                />

                {merchantDetailsOVIL && (
                  <div className={styles.merchant_details}>
                    <Popup
                      trigger={
                        <img
                          className={styles.logo}
                          src={merchantDetailsOVIL.icon}
                          alt="Logo"
                        />
                      }
                      header={merchantDetailsOVIL.name}
                    />
                  </div>
                )}
              </>
            ) : (
              ''
            )}
            {(isOIQL() || isOASL() || isBNPL() || isOVL()) &&
              isAuthenticated && (
                <Menu.Menu position="right">
                  <Menu.Item
                    as="a"
                    name={t('header.logout')}
                    onClick={logout}
                    className={styles.action_button}
                  />
                </Menu.Menu>
              )}
          </Menu>
        </Segment>

        <div id={styles.main}>{children}</div>
        <Footer serverTime={this.props.appConfigs.data.now} />
        <RedirectHomeConfirmModal
          isOpen={this.state.open}
          handleCancel={this.handleToggleConfirmModal}
          handleConfirm={this.handleConfirm}
        />
      </Responsive>
    );
  }
}

DesktopContainer.propTypes = {
  isAuthenticated: PropTypes.bool,
  logout: PropTypes.func,
  children: PropTypes.node,
};

const mapStateToProps = state => ({
  merchantDetailsBNPL: state.purchaseOrder.merchantDetails,
  merchantDetailsOVIL: state.OVILOrder.merchantDetails,
  appConfigs: state.appConfigs,
});

const mapDispatchToProps = dispatch => {
  return {
    fetchMerchantDetails: () => dispatch(fetchMerchantDetails()),
    fetchMerchantOVILDetails: () => dispatch(fetchMerchantOVILDetails()),
    fetchServerTime: () => dispatch(fetchServerTime()),
  };
};

export default compose(
  withRouter,
  withNamespaces('translations'),
  connect(
    mapStateToProps,
    mapDispatchToProps
  )
)(DesktopContainer);
