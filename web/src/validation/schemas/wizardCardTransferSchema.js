import * as Yup from 'yup';
import i18n from '../../i18n';
import './validators';
import { BANK_ACOUNT_NUM_MAX_LEN } from '../../constants';

const wizardCardDataSchema = Yup.object().shape({
  cardNumber: Yup.string()
    .test(
      'len',
      `Must be exactly ${BANK_ACOUNT_NUM_MAX_LEN} characters`,
      value => value && value.length === BANK_ACOUNT_NUM_MAX_LEN
    )
    .required(),
  year: Yup.string().required(),
  month: Yup.string().required(),
  isChecked: Yup.bool()
    .test('isChecked', i18n.t('validator.terms'), value => value === true)
    .required(i18n.t('validator.terms')),
});

export default wizardCardDataSchema;
