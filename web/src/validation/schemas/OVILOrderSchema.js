import * as Yup from 'yup';
import i18n from '../../i18n';
import './validators';

const OVILOrderSchema = Yup.object().shape({
  amount: Yup.string()
    .required()
    .purchaseOrderAmount(),
  documentNumber: Yup.string()
    .required()
    .passport()
    .idcard(),
  phoneNumber: Yup.string()
    .required()
    .phone(),
  mark: Yup.string().required(),
  model: Yup.string().required(),
  released: Yup.string().required(),
  vin: Yup.string().required(),
  firstIsChecked: Yup.bool()
    .test('isChecked', i18n.t('validator.terms'), value => value === true)
    .required(i18n.t('validator.terms')),
  secondIsChecked: Yup.bool()
    .test('isChecked', i18n.t('validator.terms'), value => value === true)
    .required(i18n.t('validator.terms')),
});

export default OVILOrderSchema;
