import { ARM_PHONE_CODES } from '../constants';

export const isPassport = value => {
  const passportRegex = /^[a-zA-Z]/;

  return passportRegex.test(value);
};

export const isPhone = value => {
  const pattern = `^(${ARM_PHONE_CODES.join('|')})[0-9]{6}$`;

  const phoneRegex = new RegExp(pattern);

  return phoneRegex.test(value);
};

export const isIdCard = value => {
  const idCardRegex = /^[0-9]/;

  return idCardRegex.test(value);
};
