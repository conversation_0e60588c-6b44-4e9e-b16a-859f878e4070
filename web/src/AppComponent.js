import React, { Component } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import { fetchAppStatus } from './redux/ducks/appConfigs';
import App from './containers/App';
import AppAuth from './containers/AppAuth';
import RenovationScreen from './components/RenovationScreen';
import CashMeLoader from './components/CashMeLoader';
import AppInfo from './components/HomeScreen/AppInfo';

import {
  isOIQL,
  isOASL,
  isREML,
  isOBL,
  isSourcePay4me,
  isWebAllowedLoanType,
  isBNPL,
  isOVL,
} from './helpers/common';

const AuthComponent = () => {
  if (isOIQL() || isOASL() || isBNPL() || isOVL()) {
    return <AppAuth />;
  }

  return <App />;
};

class AppComponent extends Component {
  componentDidMount() {
    this.props.fetchAppStatus();
  }

  render() {
    const {
      appConfigs: { appStatusLoading, data },
    } = this.props;

    if (appStatusLoading) {
      return <CashMeLoader loading={appStatusLoading} />;
    }

    if (data.disabledWEB && !isWebAllowedLoanType()) {
      return <AppInfo />;
    }

    const isAPPDisabled = data.disabledAPP;
    const isInternalBNPLDisabled = data.disabledINTERNALBNPL && isBNPL();
    const isOVILDisabled = data.disabledOVIL && isOVL();
    const isOIQLDisabled = data.disabledOIQL && isOIQL();
    const isOASLDisabled = data.disabledOASL && isOASL();
    const isREMLDisabled = data.disabledREML && isREML();
    const isOBLDisabled = data.disabledOBL && (isSourcePay4me() || isOBL());

    if (
      isAPPDisabled ||
      isOIQLDisabled ||
      isOASLDisabled ||
      isREMLDisabled ||
      isOBLDisabled ||
      isInternalBNPLDisabled ||
      isOVILDisabled
    ) {
      return <RenovationScreen />;
    }

    return AuthComponent();
  }
}

const mapStateToProps = state => {
  const { appConfigs } = state;

  return { appConfigs };
};

const mapDispatchToProps = dispatch => {
  return {
    fetchAppStatus: data => dispatch(fetchAppStatus(data)),
  };
};

export default compose(
  withRouter,
  connect(
    mapStateToProps,
    mapDispatchToProps
  )
)(AppComponent);
