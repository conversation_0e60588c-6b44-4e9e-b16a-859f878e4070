# image: docker:latest
# services:
#   - docker:dind
image: jona<PERSON><PERSON>/docker-and-compose:1.12.1-1.8.0
services:
  - docker:1.12.1-dind

before_script:
  - docker info
  - docker-compose version
  - cp env-example .env
  - sed -i -- "s/=false/=true/g" .env
  - cat .env
  - env | sort

build:5.6:php-fpm:
  variables:
    PHP_VERSION: "5.6"
  script:
    - docker-compose build php-fpm

build:7.0:php-fpm:
  variables:
    PHP_VERSION: "7.0"
  script:
    - docker-compose build php-fpm

build:7.1:php-fpm:
  variables:
    PHP_VERSION: "7.1"
  script:
    - docker-compose build php-fpm

build:7.2:php-fpm:
  variables:
    PHP_VERSION: "7.2"
  script:
    - docker-compose build php-fpm

build:5.6:workspace:
  variables:
    PHP_VERSION: "5.6"
  script:
    - docker-compose build workspace

build:7.0:workspace:
  variables:
    PHP_VERSION: "7.0"
  script:
    - docker-compose build workspace

build:7.1:workspace:
  variables:
    PHP_VERSION: "7.1"
  script:
    - docker-compose build workspace

build:7.2:workspace:
  variables:
    PHP_VERSION: "7.2"
  script:
    - docker-compose build workspace
