language: bash
sudo: required
services:
  - docker

env:
  matrix:
    - HUGO_VERSION=0.20.2

    - PHP_VERSION=5.6 BUILD_SERVICE=workspace
    - PHP_VERSION=7.0 BUILD_SERVICE=workspace
    - PHP_VERSION=7.1 BUILD_SERVICE=workspace
    - PHP_VERSION=7.2 BUILD_SERVICE=workspace

    - PHP_VERSION=5.6 BUILD_SERVICE=php-fpm
    - PHP_VERSION=7.0 BUILD_SERVICE=php-fpm
    - PHP_VERSION=7.1 BUILD_SERVICE=php-fpm
    - PHP_VERSION=7.2 BUILD_SERVICE=php-fpm

    - PHP_VERSION=hhvm BUILD_SERVICE=hhvm

    # - PHP_VERSION=5.6 BUILD_SERVICE=php-worker
    - PHP_VERSION=7.0 BUILD_SERVICE=php-worker
    - PHP_VERSION=7.1 BUILD_SERVICE=php-worker
    - PHP_VERSION=7.2 BUILD_SERVICE=php-worker

    - PHP_VERSION=NA BUILD_SERVICE=solr
    - PHP_VERSION=NA BUILD_SERVICE="mssql rethinkdb aerospike"
    - PHP_VERSION=NA BUILD_SERVICE="blackfire minio percona nginx caddy apache2 mysql mariadb postgres postgres-postgis neo4j mongo redis"
    - PHP_VERSION=NA BUILD_SERVICE="adminer phpmyadmin pgadmin"
    - PHP_VERSION=NA BUILD_SERVICE="memcached beanstalkd beanstalkd-console rabbitmq elasticsearch certbot mailhog maildev selenium jenkins proxy proxy2 haproxy"
    - PHP_VERSION=NA BUILD_SERVICE="kibana grafana laravel-echo-server"
    # - PHP_VERSION=NA BUILD_SERVICE="aws"

# Installing a newer Docker version
before_install:
  - curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -
  - sudo add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"
  - sudo apt-get update
  - sudo apt-get -y install docker-ce
  - docker version

script: ./travis-build.sh

deploy:
  provider: pages
  skip_cleanup: true
  local_dir: docs
  github_token: $GITHUB_TOKEN
  on:
    branch: master
    condition: -n "${HUGO_VERSION}"

notifications:
  email: false
