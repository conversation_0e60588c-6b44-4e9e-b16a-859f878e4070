	map $http_user_agent $is_bot {
    default 0;
    '~*bot' 1;
    '~*bingbot' 1;
    '~*yandex' 1;
    '~*baiduspider' 1;
    '~*twitterbot' 1;
    '~*facebookexternalhit' 1;
    '~*rogerbot' 1;
    '~*linkedinbot' 1;
    '~*embedly' 1;
    '~*SkypeUriPreview' 1;
    '~*quora link preview' 1;
    '~*showyoubot' 1;
    '~*outbrain' 1;
    '~*pinterest' 1;
    '~*slackbot' 1;
    '~*vkShare' 1;
    '~*TelegramBot' 1;
    '~*WhatsApp' 1;
    '~*W3C_Validator' 1;
    '~*RSiteAuditor' 1;
    '~*SiteCheckerBotCrawler' 1;
    '~*SeoSiteCheckup' 1;
    '~*SeobilityBot' 1;
    '~*google' 1;
    '~*googlebot' 1;
}

server {

    listen 80 default_server;
    listen [::]:80 default_server ipv6only=on;

    # For https
    listen 443 ssl default_server;
    listen [::]:443 ssl default_server ipv6only=on;
    ssl_certificate /etc/nginx/ssl/default.crt;
    ssl_certificate_key /etc/nginx/ssl/default.key;

    server_name localhost;
    root /var/www/public;

    location /nova {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    location /api {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    location /logs {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    location /gc-view {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    location /get {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    location /gc-verify-email {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    location /process-loan-repayment {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    location / {
        if ($is_bot = 1) {
           root /var/www/storage/app/html/seo;
           rewrite /  /index.html break;
        }
        try_files $uri $uri/ /index.html;
    }

    location  /static/ {
        try_files $uri $uri/ /index.html;
    }

    location  /assets/images/ {
        try_files $uri $uri/ /index.html;
    }

    location ~ \.php$ {
        try_files $uri /index.php =404;
        fastcgi_pass php-upstream;
        fastcgi_index index.php;
        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        #fixes timeouts
        fastcgi_read_timeout 600;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }

    location /.well-known/acme-challenge/ {
        root /var/www/letsencrypt/;
        log_not_found off;
    }
}
