const mix = require('laravel-mix');
require('laravel-mix-react-css-modules');
require('laravel-mix-svg');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */

mix
  .setPublicPath('public')
  .sourceMaps(true)
  .js('resources/static/app.js', 'build/js')
  .react('resources/telcellApp/src/root.js', 'build/js')
  .sass('resources/static/sass/app.scss', 'build/css')
  .sass('resources/telcellApp/src/index.scss', 'build/css')
  .reactCSSModules()
  .extract(['react']);

mix.svg({
  assets: ['resources/telcellApp/src/images/'], // a list of directories to search svg images
  output: 'resources/telcellApp/src/spriteSvg/svg.js', //  destination of the created js file.
});

mix.browserSync({
  proxy: {
    target: process.env.MIX_WEB_SERVER_CONTAINER,
  },
  port: process.env.MIX_BROWSERSYNC_HOST_PORT,
  ui: {
    port: process.env.MIX_BROWSERSYNC_UI_HOST_PORT,
  },
  files: [
    'resources/**/*.{js,jsx,php,html,css,scss}',
    'resources/telcellApp/src/i18n/**/*.{js,json}',
  ],
  open: false,
  notify: false,
});

mix.copyDirectory('resources/fonts', 'fonts');

if (mix.inProduction()) {
  mix.version();
}
