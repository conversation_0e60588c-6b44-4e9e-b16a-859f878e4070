<?php

return [
    'response_type' => 'mc_si_async_code',
    'scope' => 'openid mc_identity_nationalid_am_restricted',
    'token_endpoint_auth_methods_supported' => 'private_key_jwt',
    'jwt_algorithm' => 'RS512',
    'private_key_path' => 'imid/key.pem',
    'base_url' => env('IM_ID_BASE_URL'),
    'client_id' => env('IM_ID_CLIENT_ID'),
    'client_secret' => env('IM_ID_CLIENT_SECRET'),
    'user_info_url' => env('IM_ID_USER_INFO_URL'),
    'redirect_url' => env('IM_ID_REDIRECT_URL'),
    'provider_url' => env('IM_ID_PROVIDER_URL'),
];
