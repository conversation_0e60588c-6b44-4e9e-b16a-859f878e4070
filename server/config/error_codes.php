<?php

return [
    'FIELD_REQUIRED' => '0001',
    'INVALID_EMAIL' => '0002',
    'INVALID_PASSPORT' => '0003',
    'INVALID_SOC_CARD' => '0004',
    'INVALID_DOCUMENT_NUMBER' => '0005',
    'NOT_ALPHA_NUMERIC' => '0006',
    'INVALID_LENGTH' => '0007',
    'NOT_NUMERIC' => '0008',
    'NOT_BOOLEAN' => '0009',
    'DIGITS_BETWEEN' => '0010',
    'ARM_PHONE' => '0011',

    'CITIZEN_NOT_FOUND' => '4001',
    'INTERNAL_ERROR' => '4002',
    'PAYMENT_FAILURE' => '4003',
    'INVALID_VERIFICATION_CODE' => '4004',
    'INVALID_IDRAM_WALLET' => '4005',
    'CARD_NOT_FOUND' => '4006',
    'INVALID_NOTIFICATION_METHOD' => '4007',
    'INVALID_SUUID' => '4008',
    'INVALID_EASY_PAY_WALLET' => '4009',
    'PAYMENT_TRANSFER' => '4010',
    'VEHICLE_NOT_FOUND' => '4011',
    'IDENTIFICATION_FAILED' => '4012',
    'INVALID_TOKEN' => '4013',
    'LOGIN_FAILED' => '4014',
    'BLACKLISTED' => '4015',
    'CREDIT_CARD_BLACKLISTED' => '4016',
    'INVALID_TRADE_AMOUNT' => '4017',
    'CITIZEN_LOCKED' => '4018',
    'RESOURCE_LOCKED' => '4019',
    'CHECK_LIVENESS' => '4020',
    'COMPARE_FACE' => '4021',
    'LIVENESS_MANY_FACES' => '4022',
    'LIVENESS_MIN_RATE' => '4023',
    'AWS_STORE' => '4024',
    'LIVENESS_EYE_DISTANCE' => '4025',
    'LIVENESS_IMAGE_QUALITY' => '4026',
    'OPEN_TOK_SERVER_ERROR' => '4027',
    'INVALID_MODERATOR_LOAN' => '4028',
    'AWS_CONNECTION' => '4029',
    'NO_EKENG_PHOTO' => '4030',
    'LIVENESS_IMAGE_SIZE' => '4031',
    'VEHICLE_OWNER_IS_CITIZEN' => '4032',
    'INVALID_IDRAM_REQUEST_INFO' => '4033',
    'INVALID_UPAY_REQUEST_INFO' => '4044',
    'UNABLE_TO_TRANSFER_IDRAM_LOAN' => '4046',
    'FACE_NOT_FOUND' => '4045',
    'USER_NOT_FOUND' => '4050',
    'RESET_PASSWORD_FAILED' => '4051',
    'SAME_PHONE_NUMBER' => '4052',
    'INVALID_DOCUMENT' => '4053',
    'TRADE_CREDIT_EMPTY' => '4054',
    'INVALID_TELCELL_REQUEST_INFO' => '4055',
    'UNABLE_TO_TRANSFER_WALLET_LOAN' => '4056',
    'INVALID_REFERRAL_CODE_URL' => '4057',
    'INVALID_REFERRAL_CODE' => '4058',
    'REFERRAL_CODE_OWNER_IS_CITIZEN' => '4059',
    'INVALID_EASYPAY_REQUEST_INFO' => '4060',
    'INVALID_WALLET_AMOUNT' => '4062',
    'MODEL_NOT_FOUND' => '4063',
    'AWS_UPLOAD_FAILED' => '4064',
    'PASSPORT_EXPIRED' => '4065',
    'INVALID_SSN' => '4066',
    'UNPROCESSABLE_APPLICATION' => '4067',
    'TRADE_OWNER_DEAD' => '4068',
    'INVALID_BALANCE' => '4069',
    'INVALID_IDRAM_TOKEN' => '4070',
    'DUPLICATE_EMAIL' => '4071',
    'INVALID_SPOUSE_DOCUMENTS' => '4072',
    'UNMATCHED_PASSPORT' => '4073',
    'GC_CLIENT_NOT_FOUND' => '4074',
    'UNMATCHED_PHONE_NUMBER' => '4075',
    'UNMATCHED_EMAIL' => '4076',
    'UNPAID_LOAN' => '4078',
    'PAYMENT_DAY' => '4079',
    'HC_BLACKLISTED' => '4080',
    'OVERDUE_LOAN' => '4081',
    'CARD_CHECK_FAILURE' => '4082',
    'INVALID_OWNER_DOCUMENTS' => '4083',
    'PAYMENT_LOCKED' => '4084',
    'NOT_ALLOWED_LOAN_UPDATE' => '4085',
    'GUARANTEE_OVERDUE_DAYS' => '4087',
    'HC_LOAN_CLASS_EXCEPTION' => '4086',
    'INVALID_VEHICLE_CHECK_UP_DATE' => '4089',
    'INVALID_CLIENT_CODE' => '4090',
    'REGISTER_PAYMENT_FAILURE' => '4091',
    'GET_URLS_FAILURE' => '4092',
    'PAYMENT_ORDER_BINDING_FAILURE' => '4093',
    'LOAN_APPLICATION_ORDER_ALREADY_EXISTS' => '4094',
    'LOAN_APPLICATION_ORDER_NOT_FOUND' => '4095',
    'INVALID_FASTSHIFT_REQUEST_INFO' => '4096',
    'EXPIRED_LOAN_APPLICATION_ORDER' => '4097',
    'QR_NOT_FOUND' => '4098',
    'EXPIRED_QR_CODE' => '4099',
    'WALLET_LOANS_IN_LAST_PERIOD' => '4100',
    'LOAN_CONFIRMATION_LOCK' => '4101',
    'PROCESS_INTERRUPTED' => '4102',
    'INVALID_PHONE_NUMBER' => '4103',
    'PALLATON' => [
        'APP_WARNING' => '1024',
        'APP_ERROR' => '1025',
        'VERIFICATION_CODE_ATTEMPTS' => '4500',
        'VERIFICATION_CODE_EXPIRED' => '4501',
        'CATEGORY_NOT_FOUND' => '4504',
        'PRODUCT_NOT_FOUND' => '4505',
        'DUPLICATE_USER' => '4506',
        'WRONG_PASSWORD' => '4507',
        'PRODUCT_DETAILS_NOT_FOUND' => '4508',
        'HC_BANK_CONTRACT_NOT_FOUND' => '4509',
        'LOAN_REPAYMENT_FAILURE' => '4510',
        'LOAN_DOWN' => '4511',
        'APP_DOWN' => '4512',
        'DELETE_CARD' => '4513',
        'CALLBACK_INVALID_WORKING_HOURS' => '4514',
        'SUPPORT_INVALID_WORKING_HOURS' => '4515',
        'PHONE_NOT_FOUND' => '4516',
        'IM_ID_INTERNAL_ERROR' => '4518',
    ],

    'CREDIT_LINE' => [
        'PUBLIC' => [
            'SYSTEM_ERROR' => '8000',
            'EXPIRED_PAYMENT_ID' => '8001',
            'REJECTED_PAYMENT' => '8002',
            'COMPLETED_PURCHASE_ATTEMPT' => '8003',
            'DUPLICATE_ORDER_ID' => '8004',
            'INVALID_VENDOR' => '8005',
            'INVALID_TRANSACTION' => '8006',
            'REJECTED_BY_CLIENT' => '8007',
            'CANCEL_TRANSACTION_AMOUNT' => '8008',
            'CANCEL_TRANSACTION_DATE' => '8009',
            'REJECTED_CANCELATION' => '8010',
            'INVALID_PAYMENT_ID' => '8011',
        ],
        'INTERNAL_ERROR' => '5000',
        'INVALID_PAYMENT_ID' => '5001',
        'EXPIRED_PAYMENT_ID' => '5002',
        'MODEL_LOCKED' => '5003',
        'INSUFFICIENT_BALANCE' => '5004',
        'NON_SYNC_TRANSACTION' => '5005',
        'OVERDUE_FEE' => '5006',
        'MIN_AMOUNT_NOT_MET' => '5007',
        'DAILY_PURCHASE_LIMIT_EXCEEDED' => '5008',
        'INSUFFICIENT_MONTHLY_REPAYMENT' => '5009',
        'DUPLICATE_TRANSACTION' => '5010',
        'DUPLICATE_USER' => '5011',
        'DUPLICATE_CREDENTIAL' => '5012',
        'GET_DEBT_ERROR' => '5013',
        'UNAVAILABLE_CREDIT' => '5014',
        'REJECTED_BY_CLIENT' => '5015',
        'ZERO_BALANCE' => '5016',
        'NON_SYNC_PAYMENT' => '5017',
        'INVALID_ARMED_DATA' => '5018',
        'PURCHASE_ORDER_NOT_FOUND' => '5019',
        'INVALID_LOAN_SCHEDULE' => '5020',
        'EXPIRED_PURCHASE_ORDER' => '5022',
        'CHECK_STATUS' => '5023',
        'ARMED_RESPONSE_FAIL_STATUS' => '5024',
        'PURCHASE_ORDER_ALREADY_EXISTS' => '5025',
        'LOAN_ALREADY_EXISTS' => '5026',
        'INVALID_PURCHASE_SESSION_DATA' => '5027',
        'REJECTED_PURCHASE' => '5028',
        'DUPLICATE_ORDER_ID' => '5029',
        'INVALID_VENDOR' => '5030',
        'BNPL_DOWN' => '5031',
    ],

    'VLX' => [
        'INTERNAL_ERROR' => '6000',
        'INVALID_CITIZEN_INFO' => '6001',
        'INVALID_LOAN_SCHEDULE' => '6002',
        'DOWN' => '6003',
        'MERCHANT_BLACKLISTED' => '6004',
        'PRODUCT_IS_NOT_ACTIVE' => '6005',
        'INTERNAL_RULE_VIOLATION' => '6006',
    ],

    'CRM' => [
        'UNBLOCKED' => '6002',
        'CITIZEN_NOT_FOUND' => '6003',

        // validation errors
        'NOT_NUMERIC' => '0003',
    ],

    'OBL' => [
        'DOWN' => '7000',
    ],
];
