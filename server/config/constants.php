<?php

return [
    'NON_BIOMETRIC_PASSPORT' => 'NON_BIOMETRIC_PASSPORT',
    'B<PERSON>METRIC_PASSPORT' => 'BIOMETRIC_PASSPORT',
    'ID_CARD' => 'ID_CARD',
    'SOC_CARD' => 'SOC_CARD',

    'PASSPORT' => 'PASSPORT',

    'UNKNOWN' => 'UNKNOWN',

    'EKENG_DATE_FORMAT' => 'd/m/Y',

    'BIRTHDAY_DATE_FORMAT' => 'd/m/Y',

    'PASSPORT_DATE_FORMAT' => 'd/m/Y',

    'YEAR_FORMAT' => 'Y',

    'WITHDRAW_DATE_FORMAT' => 'Y-m-d H:i:s',

    'DW_PAYMENT_DATE_FORMAT' => 'Y-m-d H:i:s',

    'SERVER_DATE_TIME_FORMAT' => 'Y-m-d H:i:s',

    'NORK_DATE_FORMAT' => 'Y-m-d\TH:i:s',

    'STANDARD_DATE_FORMAT' => 'Y-m-d',

    'MORTGAGE_DATE_FORMAT' => 'd/m/Y H:i',
    'MORTGAGE_DATE_FORMAT_NOVA' => 'L LT',

    'THREE_DAYS_IN_MINUTES' => 24 * 60 * 3,

    'ONE_YEAR_IN_MINUTES' => 24 * 60 * 365,

    'TWENTY_DAYS_IN_MINUTES' => 24 * 60 * 20,

    'ONE_DAY' => 1,

    'TEN_DAY' => 10,

    'TWENTY_DAYS' => 20,

    'ONE_DAY_IN_MINUTES' => 24 * 60,

    'YEAR_IN_DAYS' => 365,

    'THIRTY_MINUTES' => 30,

    'ONE_HOUR_IN_MINUTES' => 60,

    'TWO_DAYS_IN_MINUTES' => 2880,

    'ONE_MONTH_IN_MINUTES' => 43200,

    'DAYS_FROM_START' => 90,

    'DECIMALS' => 2,

    'DIESEL_PRICE_MULTIPLIER' => 0.7,

    'RESET_PASSWORD_ATTEMPTS' => 5,

    'RESET_PASSWORD_EXPIRE_MINUTES' => 15,

    'SMS_VERIFICATION_ATTEMPTS' => 5,

    'GET_VERIFICATION_CODE_ATTEMPTS' => 3,

    'GET_EMAIL_VERIFICATION_CODE_ATTEMPTS' => 3,

    'EMAIL_VERIFICATION_CODE_ATTEMPTS' => 5,

    'BLACKLIST_EXPIRATION_IN_DAYS' => 30,

    'LOAN_HISTORY_MIN_DURATION' => 180,

    'IDENTITY_VERIFICATION_CODE_EXP' => 15,

    'CREDIT_LIMIT_CACHE_EXPIRATION' => 5,

    'HC_BLACKLISTED_CACHE_EXPIRATION' => 15,

    'ACRA_DATE_TIME_FORMAT' => 'd/m/Y h:i:s',

    'ACRA_DATE_FORMAT' => 'd/m/Y',

    'ACRA_ORGANISATION_MIN_LIFE' => 90, // days

    'STANDARD_DASHED_DATE_FORMAT' => 'd-m-Y',

    'EXPIRATION_TIME' => '20:00:00',

    'NKR' => 'ԼՂՀ',

    'EASY_PAY_LANGUAGES' => [
        'ARM' => 0,
        'ENG' => 1,
        'RUS' => 2,
    ],

    'WALLET_CACHE_EXPIRATION' => 1,

    'EASY_PAY_URLS' => [
        'CHECK' => '/PaymentApi.svc/paymentapi/check',
        'PAY' => '/PaymentApi.svc/paymentapi/pay',
    ],

    'EASY_PAY_TEST_SSN' => '**********',

    'EASYPAY_SYSTEM_TIME_FORMAT' => 'YmdHi',

    'EASYPAY_ACCOUNT_CREATED_FORMAT' => 'Y-m-d',

    'PAYMENT_INVOICE_EXPORT_DATE_FORMAT' => 'Ymd',

    'META_STEPS' => [
        'IDRAM_AGREEMENT' => 'IDRAM_AGREEMENT',
        'IDRAM_CONTRACT' => 'IDRAM_CONTRACT',
        'TELCELL_AGREEMENT' => 'TELCELL_AGREEMENT',
        'TELCELL_CONTRACT' => 'TELCELL_CONTRACT',
        'EASYPAY_AGREEMENT' => 'EASYPAY_AGREEMENT',
        'EASYPAY_CONTRACT' => 'EASYPAY_CONTRACT',
        'CONFIRM_TERMS' => 'CONFIRM_TERMS',
        'FETCH_TRIMS' => 'FETCH_TRIMS',
        'EKENG_DATA' => 'EKENG_DATA',
        'SEND_EMAIL_VERIFICATION_CODE' => 'SEND_EMAIL_VERIFICATION_CODE',
        'RESEND_EMAIL_VERIFICATION_CODE' => 'RESEND_EMAIL_VERIFICATION_CODE',
        'EMAIL_VERIFICATION' => 'EMAIL_VERIFICATION',
        'EMAIL_VERIFICATION_CODE' => 'EMAIL_VERIFICATION_CODE',
        'ALLOW_OPEN_VIDEO' => 'ALLOW_OPEN_VIDEO',
        'CREATE_STREAM_SESSION' => 'CREATE_STREAM_SESSION',
        'START_STREAM' => 'START_STREAM',
        'STOP_STREAM' => 'STOP_STREAM',
        'SUBMIT_DOCUMENT' => 'SUBMIT_DOCUMENT',
        'FETCH_CITIZEN' => 'FETCH_CITIZEN',
        'LOAN_APPROVAL' => 'LOAN_APPROVAL',
        'GET_PERSONAL_INFO' => 'GET_PERSONAL_INFO',
        'GET_EVALUATION_COMPANIES' => 'GET_EVALUATION_COMPANIES',
        'UPDATE_PERSONAL_INFO' => 'UPDATE_PERSONAL_INFO',
        'GET_CAR_VERIFICATION_INFO' => 'GET_CAR_VERIFICATION_INFO',
        'CAR_VERIFICATION' => 'CAR_VERIFICATION',
        'TRANSFER_INFO' => 'TRANSFER_INFO',
        'WALLET_INFO' => 'WALLET_INFO',
        'GET_LOAN' => 'GET_LOAN',
        'GET_CODE' => 'GET_CODE',
        'VALIDATE_CODE' => 'VALIDATE_CODE',
        'EXPIRE_CODE' => 'EXPIRE_CODE',
        'LOAN_CONFIRMATION' => 'LOAN_CONFIRMATION',
        'GET_LOAN_DOCUMENTS' => 'GET_LOAN_DOCUMENTS',
        'GET_CASH_OFFICES' => 'GET_CASH_OFFICES',
        'GET_TRANSFER_TYPES' => 'GET_TRANSFER_TYPES',
        'FETCH_AGENT_SCHEDULES' => 'FETCH_AGENT_SCHEDULES',
        'IM_ID_REGISTRATION' => 'IM_ID_REGISTRATION',
        'SEND_IDENTITY_VERIFICATION_SMS' => 'SEND_IDENTITY_VERIFICATION_SMS',
        'RESEND_IDENTITY_VERIFICATION_SMS' => 'RESEND_IDENTITY_VERIFICATION_SMS',
        'VERIFY_IDENTITY' => 'VERIFY_IDENTITY',
        'CURRENT_CONFIG' => 'CURRENT_CONFIG',
        'CREDENTIALS_VERIFIED' => 'CREDENTIALS_VERIFIED',
        'SUBMIT_DOCUMENT_PL' => 'SUBMIT_DOCUMENT_PL',
        'CHECK_LIVENESS' => 'CHECK_LIVENESS',
        'GET_SOLAR_PANEL_TYPES' => 'GET_SOLAR_PANEL_TYPES',
        'GET_REGIONS' => 'GET_REGIONS',
        'GET_ESTATE_DETAILS' => 'GET_ESTATE_DETAILS',
        'STORE_ESTATE_DETAILS' => 'STORE_ESTATE_DETAILS',
        'GET_ESTATE_MEDIA_DETAILS' => 'GET_ESTATE_MEDIA_DETAILS',
        'STORE_ESTATE_MEDIA_DETAILS' => 'STORE_ESTATE_MEDIA_DETAILS',
        'DELETE_ESTATE_MEDIA_DETAILS' => 'DELETE_ESTATE_MEDIA_DETAILS',
        'OUPL_AGREEMENT' => 'OUPL_AGREEMENT',
        'OUPL_CONTRACT' => 'OUPL_CONTRACT',
        'FASTSHIFT_AGREEMENT' => 'FASTSHIFT_AGREEMENT',
        'FASTSHIFT_CONTRACT' => 'FASTSHIFT_CONTRACT',

        'STORE_EKENG_PHOTO' => 'STORE_EKENG_PHOTO',

        'LOAN_CALC' => 'LOAN_CALC',
        'CITIZEN_INFO' => 'CITIZEN_INFO',
        'TRADE_VEHICLE' => 'TRADE_VEHICLE',
        'CONTEXT_INFO' => 'CONTEXT_INFO',
        'COMMON' => 'COMMON',
        'SET_PASSWORD' => 'SET_PASSWORD',
        'GET_PURCHASE' => 'GET_PURCHASE',
        'MAKE_PURCHASE' => 'MAKE_PURCHASE',
        'GET_TRANSACTION' => 'GET_TRANSACTION',

        'PROFILE_UPDATE' => 'PROFILE_UPDATE',
        'STORE_REQUESTED_LOAN_AMOUNT' => 'STORE_REQUESTED_LOAN_AMOUNT',

        'DATA_ROBOT_INFO' => 'DATA_ROBOT_INFO',

        'RULE_ENGINE' => 'RULE_ENGINE',
    ],

    'REGEXES' => [
        'PASSPORT' => '/^[a-zA-Z]{2}[0-9]{7}$/',
        'SOC_CARD' => '/^[0-9]{10}$/',
        'ID_CARD' => '/^[0-9]{9}$/',
        'PASSWORD' => '/^(?=.*[A-Za-z])(?=.*\d)(?=.*[!@#$%^&*()_\-+={}\[\]|:;"\'<,>\\\.?\/])/',
        'PASSWORD_MOBILE' => '/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d!@#$~%^&*()_\-+={}\[\]|:;"\'<,>\\\.?\/]{8,}$/',
        'VIN' => '/^[A-HJ-NPR-Za-hj-npr-z\d]{8}[\dX][A-HJ-NPR-Za-hj-npr-z\d]{2}\d{6}$/',
    ],

    'SMS_DATE_FORMAT' => 'Y-m-d H:i:s',

    'TRANSFER_TYPES' => [
        'CASH_PAYMENT' => 'cash_payment',
        'IDRAM_WALLET' => 'idram_wallet',
        'CARD_TO_CARD' => 'card_to_card',
        'WIRE_TRANSFER' => 'wire_transfer',
        'EASYPAY_WALLET' => 'easypay_wallet',
        'PRODUCT_PROVISION' => 'product_provision',
        'WALLET_LOAN' => 'wallet_loan',
        'VELOX_TRANSFER' => 'velox_transfer',
        'MANUAL_TRANSFER' => 'manual_transfer',
    ],

    'TRANSFER_BANKS' => [
        'ARMECONOMBANK' => 'Armeconombank',
        'EVOCABANK' => 'Evocabank',
    ],

    'BANK_REPORT_PARSER' => [
        'DOC_TYPES' => [
            'CONVERSE' => 'converse',
            'AEB' => 'aeb',
            'ABB' => 'abb',
            'AMERIA' => 'ameria',
            'EVOCA' => 'evoca',
            'ARDSHIN' => 'ardshin',
            'CORRECTED' => 'corrected',
            'ARARAT' => 'ararat',
        ],
        'PAYMENT_TYPES' => [
            'PAYMENT' => '4',
            'PRINCIPAL_PAYMENT' => '2',
        ],
        'ARDSHIN_CHECK_NUMBER_LENGTH' => 15,
    ],

    'GENDERS' => [
        'M' => 1,
        'F' => 2,
    ],

    'TITLE' => [
        1 => 'MR',
        2 => 'MRS',
    ],

    'HC' => [
        'GLOBAL_CREDIT_CODE' => 63500,
        'CREDIT_IDENTIFIERS' => [
            'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
        ],
        'CHECK_DIGIT_WEIGHTS' => [
            2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1,
        ],
        'LOAN_OUTER_CODE_TYPE_OCL' => 'M',
        'LOAN_OUTER_CODE_TYPE_OVL' => 'V',
        'LOAN_OUTER_CODE_TYPE_OASL' => 'S',
        'LOAN_OUTER_CODE_TYPE_OIQL' => 'Q',
        'LOAN_OUTER_CODE_TYPE_OIDL' => 'E',
        'LOAN_OUTER_CODE_TYPE_OTCL' => 'T',
        'LOAN_OUTER_CODE_TYPE_PL' => 'P',
        'LOAN_OUTER_CODE_TYPE_OEPL' => 'Y',
        'LOAN_OUTER_CODE_TYPE_REML' => 'R',
        'LOAN_OUTER_CODE_TYPE_VLX' => 'X',
        'LOAN_OUTER_CODE_TYPE_BNPL' => 'H',
        'LOAN_OUTER_CODE_TYPE_OIWL' => 'W',
        'MORTGAGE_OUTER_CODE_PREFIX' => 'G',
        'LOAN_OUTER_CODE_TYPE_OUPL' => 'U',
        'LOAN_OUTER_CODE_TYPE_OBL' => 'B',
        'LOAN_OUTER_CODE_TYPE_OFSL' => 'F',

        'NON_BIOMETRIC_PASSPORT' => '01',
        'BIOMETRIC_PASSPORT' => '13',
        'ID_CARD' => '12',

        'SOC_CARD_REFERENCE' => 4,

        'DELIVERY_REGISTRATION_ADDRESS' => 1,

        'VEHICLE_LOAN_MIN_DAYS' => 100,

        'HC_REGIONS' => [
            '01' => [
                'ԵՐԵՎԱՆ' => ['REGION' => '*********', 'DISTRICT' => '001'],
            ],
            '02' => [
                'ԱՐԱԳԱԾՈՏՆ' => ['REGION' => '*********', 'DISTRICT' => '009'],
            ],
            '03' => [
                'ԱՐԱՐԱՏ' => ['REGION' => '*********', 'DISTRICT' => '011'],
            ],
            '04' => [
                'ԱՐՄԱՎԻՐ' => ['REGION' => '*********', 'DISTRICT' => '006'],
            ],
            '05' => [
                'ԳԵՂԱՐՔՈՒՆԻՔ' => ['REGION' => '*********', 'DISTRICT' => '002'],
            ],
            '06' => [
                'ԼՈՌԻ' => ['REGION' => '*********', 'DISTRICT' => '004'],
            ],
            '07' => [
                'ԿՈՏԱՅՔ' => ['REGION' => '070000005', 'DISTRICT' => '010'],
            ],
            '08' => [
                'ՇԻՐԱԿ' => ['REGION' => '080000003', 'DISTRICT' => '003'],
            ],
            '09' => [
                'ՍՅՈՒՆԻՔ' => ['REGION' => '090000001', 'DISTRICT' => '007'],
            ],
            '10' => [
                'ՎԱՅՈՑ ՁՈՐ' => ['REGION' => '********9', 'DISTRICT' => '005'],
            ],
            '11' => [
                'ՏԱՎՈՒՇ' => ['REGION' => '110000007', 'DISTRICT' => '008'],
            ],
            '12' => [
                'ՍՏԵՓԱՆԱԿԵՐՏ' => ['REGION' => '510000003', 'DISTRICT' => '012'],
                'ԱՍԿԵՐԱՆ' => ['REGION' => '520000001', 'DISTRICT' => '012'],
                'ՀԱԴՐՈՒԹ' => ['REGION' => '530000009', 'DISTRICT' => '012'],
                'ՄԱՐՏԱԿԵՐՏ' => ['REGION' => '540000007', 'DISTRICT' => '012'],
                'ՄԱՐՏՈՒՆԻ' => ['REGION' => '550000004', 'DISTRICT' => '012'],
                'ՇԱՀՈՒՄՅԱՆ' => ['REGION' => '560000002', 'DISTRICT' => '012'],
                'ՇՈՒՇԻ' => ['REGION' => '570000000', 'DISTRICT' => '012'],
                'ՔԱՇԱԹԱՂ' => ['REGION' => '580000008', 'DISTRICT' => '012'],
            ],
        ],
    ],

    'EKENG_REGION_NKR' => '12',

    'ARM_CURRENCY_ISO' => 51,

    'MONTHS' => [
        'January' => 'Հունվար',
        'February' => 'Փետրվար',
        'March' => 'Մարտ',
        'April' => 'Ապրիլ',
        'May' => 'Մայիս',
        'June' => 'Հունիս',
        'July' => 'Հուլիս',
        'August' => 'Օգոստոս',
        'September' => 'Սեպտեմբեր',
        'October' => 'Հոկտեմբեր',
        'November' => 'Նոյեմբեր',
        'December' => 'Դեկտեմբեր',
    ],

    'FICO_ACTIVE_DAYS' => 31,

    'ARM_TIMEZONE' => 'Asia/Yerevan',
    'UTC_TIME' => 'UTC',

    'NOTIFICATION_METHODS' => [
        'email' => 'Էլեկտրոնային փոստի միջոցով',
        'post' => 'Փոստային կապի միջոցով',
        'in_person' => 'Առձեռն՝ Վարկատուի գրասենյակից',
    ],

    'MINS_BEFORE_CHECKUP' => 60,

    'SMPP_SOCKET_TIMEOUT' => 30000,

    'BANK_CODE_LENGTH' => 3,
    'BANK_ACOUNT_NUM_MIN_LEN' => 12,
    'BANK_ACOUNT_NUM_MAX_LEN' => 16,

    'BANK_CODES' => [
        115 => 'Armbusinessbank CJSC',
        118 => 'ID Bank CJSC',
        151 => 'Araratbank OJSC',
        157 => 'Ameriabank CJSC',
        160 => 'VTB Bank Armenia CJSC',
        163 => 'Armeconombank OJSC',
        166 => 'Evocabank CJSC',
        175 => 'Armeconombank OJSC (BTA bank)',
        193 => 'Converse Bank CJSC',
        205 => 'InecoBank CJSC',
        208 => 'Mellat bank CJSC',
        214 => 'Byblos Bank Armenia CJSC',
        217 => 'HSBC Bank Armenia CJSC',
        220 => 'ACBA-Credit Agricol Bank CJSC',
        223 => 'Artsakhbank CJSC',
        241 => 'UniBank CJSC',
        247 => 'Ardshinbank CJSC',
        250 => 'ArmSwissBank CJSC',
        253 => 'Inecobank CJSC (Procredit bank)',
        256 => 'Panarmenian Bank OJSC',
        103 => 'Central bank',
        248 => 'Ardshinbank CJSC (Areximbank)',
        181 => 'Ararat bank CJSC (AD bank)',
        0 => 'Other',
    ],

    'NUMERIC_MONTHS' => [
        '01',
        '02',
        '03',
        '04',
        '05',
        '06',
        '07',
        '08',
        '09',
        '10',
        '11',
        '12',
    ],

    'ARM_MOBILE_PHONE_CODES' => [
        '33',
        '41',
        '43',
        '44',
        '47',
        '49',
        '55',
        '77',
        '91',
        '93',
        '94',
        '95',
        '96',
        '97',
        '98',
        '99',
    ],

    'ARM_LANDLINE_PHONE_CODES' => [
        '10',
        '11',
        '60',
    ],

    'CURRENCY' => [
        'AMD' => 'AMD',
    ],

    'OASL_CREDIT_MAX_AMOUNT' => [
        'Ջերմ Օջախ' => 3000000,
        'R2E2' => ********,
        'KfW' => 3200000,
    ],

    'OASL_XLSX_EXPORT_DATA' => [
        'EVALUATION_REPORT' => 'Գնահատման հաշվետվություն',
        'IDENTIFICATOR_PREFIX_1' => 'GLB_MA002_LDA',
        'IDENTIFICATOR_PREFIX_2' => '_ML',
        'INTEREST_RATE_TYPE' => 'ֆիքսված',
        'CREDIT_PURPOSE' => 'ՋՕ վերանորոգում',
        'CREDIT_TYPE' => 'անուիտետային',
        'LOAN_OFFICER' => 'Մայրանուշ Պապոյան',
        'AMOUNT_RATE' => 'AMD',
        'PTI' => 0,
        'OTI' => 0,
        'COUNTRY' => 'ՀՀ',
        'ESTIMATED_COST' => '0.1',
        'PARTICIPANTS' => 'Վարկառու',
        'GENDER' => [
            1 => 'արական',
            2 => 'իգական',
        ],
        'BUILDING_TYPE' => 'բնակելի տուն',
        'EDUCATION' => 'միջնակարգ',
    ],

    'PERSONAL_SHEET_EXP' => 10,

    'EXTENSION' => [
        'PDF' => 'pdf',
    ],

    'LOAN_TYPES' => [
        'OCL' => 1,
        'OVL' => 2,
        'COMMON' => 3,
        'OIQL' => 4,
        'OASL' => 5,
        'OIDL' => 6,
        'OTCL' => 7,
        'PL' => 8,
        'OEPL' => 10,
        'VLX' => 11,
        'REML' => 12,
        'BNPL' => 13,
        'OIWL' => 14,
        'OUPL' => 15,
        'OBL' => 16,
        'OFSL' => 17,
    ],

    'LOAN_TYPE_LOCKS' => [
        1 => [1, 11],
        2 => [6, 7, 10, 14, 15, 17],
    ],

    'REQUESTED_LOAN_TYPE' => [
        'FAST_MONEY' => 'FAST_MONEY',
        'OVL_PLEDGE' => 'PLEDGE',
        'OVL_TRADE' => 'TRADE',
    ],

    'LOAN_SUBTYPES' => [
        'PREML' => 1,
        'SREML' => 2,
        'OVIL' => 4,
        'OVPL' => 5,
        'OVTL' => 6,
    ],

    'VENDOR_TYPES' => [
        'PAY_LATER' => 1,
        'INTERNAL_BNPL' => 2,
        'BNPL' => 3,
        'ARMED' => 4,
        'VEHICLE_IMPORT' => 5,
    ],

    'MERCHANT_TYPES' => [
        'HOSPITAL' => 1,
        'VEHICLE_IMPORT' => 2,
    ],

    'NOTIFIABLE_LOAN_TYPES' => [
        'APP',
        'OCL',
        'OVL',
    ],

    'CREDIT_OFFER_TYPES' => [
        'TOP_UP_LOAN_APPLICATION' => 'TOP_UP_LOAN_APPLICATION',
        'CREDIT_LIMIT' => 'CREDIT_LIMIT',
        'PERIODIC_CREDIT_LIMIT_CALCULATION' => 'PERIODIC_CREDIT_LIMIT_CALCULATION',
        'TRANSACTION_CREDIT_LIMIT_CALCULATION' => 'TRANSACTION_CREDIT_LIMIT_CALCULATION',
    ],

    'LOAN_DOCUMENT_BATCHES' => [
        'LOAN' => 1,
        'TOP_UP_LOAN' => 2,
    ],

    'TOP_UP_OFFER_PERIOD_IN_MONTHS' => 2,

    'DEVELOPER_COMPANIES' => [
        'RENSHIN' => [
            'ID' => 1,
            'ADDRESS' => 'Տերյան 107',
        ],
        'LUYSER' => [
            'ID' => 2,
            'ADDRESS' => 'Լենինգրադյան 31/30',
            'APPARTMENT_STATUSES' => [
                '10' => 'FREE',
                '20' => 'RESERVED',
                '30' => 'SOLD',
            ],
            'MIN_ANNUAL_RATE' => 11,
            'MAX_ANNUAL_RATE' => 12,
            'REAL_ESTATE_AVERAGE_PRICE' => 35000000,
        ],
    ],

    'REAL_ESTATE_MARKET_TYPES' => [
        'PRIMARY' => 'PRIMARY',
        'SECONDARY' => 'SECONDARY',
    ],

    'REFERRER_SOURCES' => [
        'BANALI' => 'BANALI',
        'LUYSER' => 'LUYSER',
        'TELCELL' => 'TELCELL',
    ],

    'LOAN_TYPE_STATUS' => [
        'DISABLE' => 1,
        'ENABLE' => 0,
    ],

    'ARBITRATION' => [
        'OPTIMUS' => 'optimum',
        'GNM' => 'gnm_arbitration',
        'UBA' => 'arbitration',

        'NAMES' => [
            'gnm_arbitration' => '«ԳՆՄ ԱՐԲԻՏՐԱԺ» ՍՊԸ',
            'optimum' => '«Օպտիմուս Լեքս» ՍՊԸ',
            'arbitration' => 'Հայաստանի բանկերի միության «Ֆինանսական արբիտրաժ» հիմնարկ',
        ],

        'EMAILS' => [
            'gnm_arbitration' => 'www.arbitragegnm.com',
            'optimum' => 'www.optimuslex.am',
            'arbitration' => 'www.uba.am',
        ],
    ],

    'ACRA' => [
        'CURRENT_CREDIT' => 'գործող',
        'CAR' => 'ավտոմեքենա',
        'TYPE' => [
            'MONITORING' => 'MONITORING',
            'PHYSICAL' => 'PHYSICAL',
        ],
    ],

    'ACCEPTABLE_MAX_MILEAGE' => 40000,

    'MAX_AGE_BY_MONTH' => [
        'OVL' => 780, // 65 age
        'REML' => 720, // 60 age
    ],

    'ALLOWED_OWNER_COUNT_OVL' => 1,

    'REAL_ESTATE_MIN_ALLOWED_PRICE' => 2230000,

    'REAL_ESTATE_MIN_PRE_PAYMENT' => 223000,

    'REML_SOLVENCY_BOUNDARY_VALUES' => [
        'REAL_ESTATE_PRICE' => 55000000,
        'MORTGAGE_AMOUNT' => 35000000,
        'NET_SALARY_PERCENTAGE_60' => 0.6,
        'NET_SALARY_PERCENTAGE_45' => 0.45,
    ],

    'NET_SALARY_CONFIG' => [
        'INCOME_TAX' => 0.21,
        'SOCIAL' => [
            'SALARY_LESS_500000_PERCENTAGE' => 0.045,
            'SALARY_500000_1020000_PERCENTAGE' => 0.1,
            'SALARY_500000_1020000_SUB' => 27500,
            'SALARY_MORE_1020000_TAX' => 74500,
        ],
        'ARMY' => [
            'SALARY_LESS_100000' => 1500,
            'SALARY_100000_200000' => 3000,
            'SALARY_200000_500000' => 5500,
            'SALARY_500000_1000000' => 8500,
            'SALARY_MORE_1000000' => 15000,
        ],
    ],

    'DUPLICATE_KEY_EXCEPTION' => 7,

    'GC_VIEW_IDENTITY_VERIFICATION_CODE_EXP' => 24 * 60, // In mins

    'EMAIL_VERIFICATION_CODE_EXP' => 15, // In mins

    'SMS_VERIFICATION_CODE_EXP' => 15, // In mins

    'MISSING_ADDRESS_TEXT' => 'ՃԻՇՏ ՀԱՍՑԵՆ ԲԱՑԱԿԱՅՈՒՄ Է',

    'LOCKED_CITIZEN_EXPIRATION' => 1,

    'ACRA_LOCK_TIMEOUT' => 3600,

    'VELOX_LOAN_OFFER_LOCK_TIMEOUT' => 300,

    'PURCHASE_ORDER_LOCK_TIMEOUT' => 300,

    'LOAN_APPLICATION_ORDER_LOCK_TIMEOUT' => 300,

    'LOAN_CONFIRMATION_LOCK_TIMEOUT' => 900, // in seconds

    'LOAN_APPLICATION_ORDER_EXP' => 3, // in days

    'REGISTRATION_USER_INFO_LOCK' => 60,

    'ACRA_PLEDGE_SUBJECTS' => [
        'Անհայտ',
        'Ապրանքներ',
        'Երաշխավորություններ',
        'Շրջանառու միջոցներ',
        'Աշխատավարձ',
        'Ոչ ստանդարտ ապահովում',
        'ֆինանսական հոսք',
        'ֆինանսական հոսք և բարի համբավ',
        'Այլ',
        'Սարքավորումներ',
        'Պատրաստի արտադրանք',
        'Բլանկային',
        'Վարկային երաշխավորություններ',
        'Այլ երաշխավորություններ',
        'Պատրաստի արտադրանք և ապրանքներ',
        'Երաշխավորության պայմանագիր',
        'Կենցաղային տեխնիկա',
        'Կենցաղային այլ ապրանքներ',
        'Գրավ',
        'Գրավադրված ապրանք ապառիկի գծով',
        'Ապրանքներ ՄՄԿ',
        'Այլ գրավ',
        'Ապրանքներ Ակցիային/ԲԿ',
        'Ապրանքներ Բջջային',
        'Հաշվի շրջանառություն կրեդիտ քարտեր',
        'Բլանկային կրեդիտ',
        'Վարկային երաշխավորություն Համատեղ',
    ],

    'ACRA_OSM_CREDIT_USE_PLACE' => [
        'GROUP_1' => [
            1,
            2,
            4,
            36,
        ],
        'GROUP_2' => [
            3,
            6,
            39,
        ],
        'MIXED' => 35,
    ],

    'INCOME_COEFFICIENTS' => [
        [
            'min' => 1,
            'max' => 25000,
            'coefficient' => [
                'osm' => 0.1,
                'dsti' => 0.1,
            ],
        ],
        [
            'min' => 25001,
            'max' => 35000,
            'coefficient' => [
                'osm' => 0.17,
                'dsti' => 0.17,
            ],
        ],
        [
            'min' => 35001,
            'max' => 95000,
            'coefficient' => [
                'osm' => 0.24,
                'dsti' => 0.24,
            ],
        ],
        [
            'min' => 95001,
            'max' => 210000,
            'coefficient' => [
                'osm' => 0.37,
                'dsti' => 0.37,
            ],
        ],
        [
            'min' => 210001,
            'max' => ********0,
            'coefficient' => [
                'osm' => 0.55,
                'dsti' => 0.47,
            ],
        ],
    ],

    'NO_FREE_AGENTS' => 'no_free_agents',

    'EFFECTIVE_FICO_SCORE' => 476,

    'FACE_SIMILARITY_THRESHOLD' => 80,

    'FACE_RECOGNITION_ATTEMPTS' => 3,

    'OBL_SCHEDULE_INDEX' => 0.35,

    'LIVENESS_MIN_RATE' => 0.5,

    'LIVENESS_MIN_HEAD_SCALE' => 0.125,

    'LIVENESS_MIN_EYE_DISTANCE' => 100,

    'LIVENESS_IMAGE_QUALITY' => 0,

    'ARCHIVE_VIDEO_NAME' => 'archive.mp4',

    'TOK_BOX_CONFIGS' => [
        'hasAudio' => true,
        'hasVideo' => true,
    ],

    'LIST_OF_SUPPORTED_PLUGINS_BY_PLUGIN_VERSION_API' => [
        'globalcredit/rule-engine',
    ],

    'AWS_SIGNATURE_DURATION' => 15, // Minutes

    'LOAN_MODERATION_DURATION' => 15, // 15 Minutes

    'LOAN_REJECTION_DURATION' => 3, // 3 Minutes

    'NOVA' => [
        'LOAN_STATUS' => [
            'PROCESSING' => 'PROCESSING',
            'REVIEW' => 'REVIEW',
            'PROCESSED' => 'PROCESSED',
            'PLEDGED' => 'PLEDGED',
            'CONFIRMED' => 'CONFIRMED',
            'REJECTED' => 'REJECTED',
            'PARTIAL_PROCESSED' => 'PARTIAL_PROCESSED',
        ],
        'OVL_STRATEGY' => [
            'DIRECT' => 'direct',
            'SUPERVISED' => 'supervised',
        ],
        'MONITORING_STATUSES' => [
            'FAILED' => 'FAILED',
            'PENDING' => 'PENDING',
            'COMPLETED' => 'COMPLETED',
        ],
        'NETWORK_LOADING_RESOURCES' => [
            '/nova-api/withdrawn-loans',
            '/nova-api/withdrawable-loans',
        ],
    ],

    'SALES_CONRTACT_NUMBER_PREFIX' => 'SS',

    'WORKPLACE_MULTIPLIERS' => [
        '1' => 0.5,
        '2' => 0.4,
        'NONE' => 0.3,
    ],

    'TOKBOX_VIDEO_STATUS' => [
        'STARTED' => 'started',
    ],

    'VEHICLE_LENDER_PSN' => '01013781', // Lender identifier for GC

    'INCODE_STRATEGY' => 'INCODE_STRATEGY',

    'KAIROS_STRATEGY' => 'KAIROS_STRATEGY',

    'KAIROS_MAX_WIDTH' => 3000,

    'KAIROS_MAX_HEIGHT' => 3000,

    'AWS_MAX_IMAGE_SIZE' => 5, // MB

    'INCODE_LIVENESS_CONFIDENCE' => '0',

    'ARCA_SUCCESS_CODE' => '00',

    'ARCA_EXECUTED_TRANSFER_CODE' => '99',

    'ARCA_TIMEOUT_ERROR_CODE' => '2',

    'MAIL_VERIFICATION_CODE_LENGTH' => 6,

    'PAYMENT_ID_EXP' => 60, // Minutes

    'PL_DASHBOARD_PAGINATION' => 10,

    'NEW_CUSTOMER_TRANSMISSION_OVL' => 91,

    'NEW_CUSTOMER_TRANSMISSION_OCL' => 31,

    'CURRENCIES' => ['USD', 'EUR', 'RUB'],

    'QR_CODE' => [
        'TOKEN_LENGTH' => 8,
        'FORMAT' => 'svg',
        'DISCOUNT' => [
            'MIN' => 0,
            'MAX' => 1,
        ],
        'IMAGE_SIZES' => [
            200 => '200x200',
            400 => '400x400',
            600 => '600x600',
            800 => '800x800',
            1000 => '1000x1000',
        ],
    ],

    'PREDEFINED_REAL_ESTATE' => [
        'TOKEN_LENGTH' => 10,
    ],

    'REFERRAL_CODE' => [
        'HASH_LENGTH' => 8,
        'DATE_FORMAT' => 'Y-m-d',
        'REPAYMENT' => [
            1 => 5000, //OCL
            2 => 20000, //OVL
        ],
    ],

    'FILE_TIME_FORMAT' => 'Y-m-d_h:i:s',

    'FILE_MONITORING_FORMAT' => 'Y-m-d_H-i',

    'UNBLOCKING_ORIGIN' => [
        '1' => 'CASHME',
        '2' => 'CRM',
    ],

    'AMOUNT_LIMITS' => [
        'OCL' => 300000,
        'VLX' => 300000,
        'BNPL' => 300000,
    ],

    'SCORE_LIMIT' => 0.5,

    'PRECISION' => 2,

    'CREDIT_LINE' => [
        'PL_PROJECT_START_YEAR' => 2021,
        'BNPL_PROJECT_START_YEAR' => 2022,
        'SCHEDULE_DATE_FORMAT' => 'Y-m-d',
        'LAST_SECOND' => 59,
        'DATE_FILTER_FORMAT' => 'Y-m-d\TH:i',
        'APR' => 0,
    ],

    'ACRA_LOGIN' => [
        'CREDITING_VALUES' => [
            'RequestTarget' => '1',
            'UsageRange' => '1',
        ],
        'MONITORING_VALUES' => [
            'RequestTarget' => '2',
            'UsageRange' => '21',
        ],
    ],

    'ACRA_REPORT_TYPE' => [
        'CREDIT' => '01',
        'CREDIT_WITH_FICO' => '02',
        'ONLY_FICO' => '03',
        'CREDIT_WITH_BUSINESS' => '05',
    ],

    'ACRA_MONITORING' => [
        'MAX_USER_COUNT' => 20,
        'MONITORING_WITHOUT_FICO' => '01',
        'MONITORING_RETRO' => '02',
        'MONITORING_ONLY_FICO' => '03',
        'EXCEL_KEYS' => [
            'CONTRACT_NUMBER' => 'Contract Number',
            'LAST_NAME' => 'Last Name',
            'FIRST_NAME' => 'First Name',
            'DOCUMENT_NUMBER' => 'Document Number',
            'SSN' => 'SSN',
            'BIRTH_DATE' => 'Birth Date',
        ],
        'REDIS_KEY' => 'acra_monitoring',
    ],

    'N/A_FICO' => 99999, // Allowed maximum value of fico score in HC

    'INITIAL_INTEREST_RATE' => 24,

    'SCHEDULE_DATE_FORMAT' => 'Y-m-d',

    'PALLATON' => [
        'FORGOT_PASSWORD_GET_SMS_VERIFICATION_CODE_ATTEMPTS' => 3,
        'FORGOT_PASSWORD_GET_EMAIL_VERIFICATION_CODE_ATTEMPTS' => 3,

        'FORGOT_PASSWORD_VERIFICATION_CODE_ATTEMPTS' => 5,

        'FORGOT_PASSWORD_EMAIL_VERIFICATION_CODE_LENGTH' => 4,
        'FORGOT_PASSWORD_SMS_VERIFICATION_CODE_LENGTH' => 4,

        'FORGOT_PASSWORD_EMAIL_VERIFICATION_CODE_EXP' => 15, // In mins
        'FORGOT_PASSWORD_SMS_VERIFICATION_CODE_EXP' => 15, // In mins

        'MAIL_VERIFICATION_CODE_LENGTH' => 4,

        'CATEGORY_INFO' => [
            '1' => [
                'amount' => '20,000 - 900,000 ՀՀ դրամ',
            ],
            '2' => [
                'amount' => '500,000 - 5,000,000 ՀՀ դրամ',
            ],
        ],
    ],

    'SECURITY_ENTRY' => [
        'REGISTRATION' => 'registration',
        'LOAN_APPLICATION' => 'loan_application',
        'LOAN_APPLICATION_MOBILE' => 'loan_application_mobile',
        'LOAN_APPLICATION_SYSTEM' => 'loan_application_system',
    ],

    'ARM_PHONE_CODE' => '374',

    'REPAYMENT_TYPES' => [
        'current_repayment' => 0,
        'principal_repayment' => 2,
        'upcoming_month' => 4,
        'full_repayment' => 9,
    ],

    'REPAYMENT_DATE_FORMAT' => 'Y-m-d',

    'SREML_MORTGAGE_CONTRACT_NOTES' => 'Հարկային օրենսգրքի 236-րդ հոդվածի 4-րդ կետի պահանջների համաձայն, օտարման գործարքներից ծագող իրավունքների պետական գրանցում կկատարվի հաշվառող մարմինների կողմից տրված անշարժ գույքի գծով հարկային պարտավորություններ չունենալու վերաբերյալ տեղեկանք ներկայացնելու դեպքում, բացառությամբ այն դեպքերի, երբ օտարումը կատարվում է դատական ակտերի հարկադիր կատարման ծառայության, սնանկության կառավարչի կողմից կամ գրավառուի կողմից։',

    'SREML_SUBJECT_DESCRIPTION' => 'Բազմաբնակարան շենքի զբաղեցրած հողամասի ու բազմաբնակարան շենքի ընդհանուր օգտագործման տարածքների բաժնային սեփականության իրավունքում համապատասխան բաժինը կամ բաժինները։',

    'LOAN_TERMS' => [
        '1' => [
            'borrower' => 'ՀՀ քաղաքացի',
            'annual_rate' => '0 - 24',
            'min_start_month' => '1',
            'age' => '21 - 65',
            'provision_type' => 'կանխիկ/անկանխիկ',
            'apr' => '39 - 152',
            'service_fee' => '1 - 7.83',
            'loan_payment' => 'անուիտետային',
            'loan_review_fee' => 'սահմանված չէ',
            'withdrawal_fee' => 'չի կիրառվում',
            'required_documents' => [
               'Անձնագրի և սոցիալական քարտի առկայություն',
            ],
            'card_additional_info' => [
                 [
                    'title' => 'Մինչև 150,000 ՀՀ Դրամ',
                    'description' => '1 տարով` օրական սկսած 50 դրամից մայր գումարը` ժամկետի վերջում',
                    'icon' => env('APP_URL').'/assets/pallaton/ocl/calendar-one-number.svg',
                ],
                 [
                    'title' => 'Մինչև 900,000 ՀՀ Դրամ',
                    'description' => '18 ամիս` ամսական հավասարաչափ մարումներով',
                    'icon' => env('APP_URL').'/assets/pallaton/ocl/calendar.svg',
                ],
            ],
        ],
        '2' => [
            'borrower' => 'ՀՀ քաղաքացի',
            'currency' => 'ՀՀ դրամ',
            'loan_provision_fee' => 'վարկի գումարի 3%, միանվագ',
            'loan_security' => 'շարժական գույք',
            'loan_collateral_ratio' => 'շուկայական գնի մինչև 100%',
            'application_review_fee' => 'սահմանված չէ',
            'early_loan_repayment_penalty' => 'տույժ չի կիրառվում',
            'loan_decision' => 'նույն պահին',
            'annual_rate' => '12',
            'loan_term' => '12-120',
            'age' => '21 - 65',
            'provision_type' => 'անկանխիկ',
            'apr' => '23.03 - 69.78',
            'service_fee' => '0.67 - 3',
            'loan_payment' => 'անուիտետային',
            'loan_review_fee' => 'սահմանված չէ',
            'withdrawal_fee' => 'չի կիրառվում',
            'required_documents' => [
                'անձը հաստատող փաստաթուղթ',
                'սոցիալական քարտ',
                'մեքենայի սեփականության վկայական',
                'մեքենայի տեխ․ անձնագիր',
            ],
        ],
        '13' => [
            'annual_rate' => '0',
            'age' => '21 - 70',
            'provision_type' => 'անկանխիկ',
            'apr' => '0',
            'service_fee' => '0',
            'loan_payment' => 'անուիտետային',
            'loan_review_fee' => 'սահմանված չէ',
            'withdrawal_fee' => 'չի կիրառվում',
            'required_documents' => [
                'անձը հաստատող փաստաթուղթ',
                'սոցիալական քարտ',
            ],
        ],
        '14' => [
            'borrower' => 'ՀՀ քաղաքացի',
            'age' => '21 - 65',
            'provision_type' => 'անկանխիկ',
            'apr' => '447 - 460',
            'loan_payment' => 'սպասարկման վճարը՝ աﬔնամսյա, մայր գումարը ժամկետի վերջում',
            'required_documents' => [
                'Անձնագիր կամ ID քարտ',
            ],
        ],
    ],

    'LOAN_SUBTYPE_TERMS' => [
        '4' => [
            'provision_type' => 'անկանխիկ՝ փոխանցվում է գործընկեր կազմակերպությանը',
        ],
    ],

    'VPOS_BANKS' => [
        'ARARAT' => 'ararat_bank',
        'EVOCA' => 'evoca_bank',
    ],

    'MAX_ALLOWED_CREDIT_CARDS_COUNT' => 5,

    'DISPUTE_SOLUTION_METHODS' => [
        'gnm_arbitration' => 'ԳՆՄ արբիտրաժային դատարան',
        'optimum' => 'Օպտիմուս Լեքս ՍՊԸ',
        'court' => 'Լուծվեն դատական կարգով',
        'arbitration' => 'ՀԲՄ Ֆինանսական արբիտրաժ',
    ],

    'CLEAN_UP_DIRECTORIES' => [
        'PUBLIC' => [
            'SUB_DIRECTORIES' => ['nova-downloads'],
        ],
    ],

    'REQUEST_MONITOR' => [
        'SERVICE' => [
            'EKENG' => 'EKENG',
            'ACRA' => 'ACRA',
            'NORK' => 'NORK',
        ],
        'TYPE' => [
            'PASSPORT' => 'PASSPORT',
            'SSN' => 'SSN',
            'MONITORING_BULK' => 'MONITORING_BULK',
            'MONITORING' => 'MONITORING',
        ],
    ],

    'NOTIFIER' => [
        'APPLICATION' => 'customer',
        'DEVICE_OS_TYPES' => [
            'Android' => 'ANDROID',
            'Ios' => 'IOS',
        ],
    ],

    'HC_BODY_TYPE' => [
        'ԹԱՄԲԱՅԻՆ ՔԱՐՇԱԿ' => '10',
        'ՈւՆԻՎԵՐՍԱԼ' => '1',
        'ՍԵԴԱՆ' => '2',
        'ԻՆՔՆԱԹԱՓ' => '3',
        'ՖՈՒՐԳՈՆ' => '4',
        'ՀԵՏՉԲԵԿ' => '5',
        'ՍԱՌՆԱՐԱՆ' => '6',
        'ԿՈՒՊԵ' => '7',
        'ԿՈՂԱՎՈՐ' => '8',
        'ՎԱԳՈՆ' => '9',
    ],

    'EVOCA_SUPPORTED_CARDS' => [
        'ARCA' => [
            '550103', '431827', '548342', '431828', '676930', '431829', '470380', '431830', '402455',
        ],
        'ASHIB' => [
            '540887', '445428', '540622', '445429', '552858', '445430', '677050', '445431', '547128', '445615',
            '527073', '435656', '445616',
        ],
        'ARARAT' => [
            '522065', '437511', '532850', '437513', '547339', '437512', '677549', '423936',
        ],
        'AMERIA' => [
            '550104', '408306', '548358', '408307', '552655', '408309', '528612', '408310', '676961', '408308',
            '457372', '457368',
        ],
        'AEB' => [
            '544128', '488962', '544456', '488963', '552604', '488964', '676864', '488965', '486362', '485496',
        ],
        'EVOCA' => [
            '516088', '526392', '553062', '677217', '400160', '4150960', '4150961', '4150962', '4150963', '4150964',
            '4150965', '4150966', '4150967', '4150968', '4150969',
        ],
        'CONVERSE' => [
            '545057', '484701', '545479', '484702', '534586', '484703', '484704', '418732', '404590', '483291',
            '418733',
        ],
        'BYBLOS' => [
            '418733', '525757', '547804', '539434',
        ],
        'HSBC' => [
            '557114', '557115', '550068',
        ],
        'ACBA' => [
            '518180', '435503', '526898', '435504', '677678', '435505', '435506', '435501',
        ],
        'ARTSAKH' => [
            '544906', '540854', '552880', '677069',
        ],
        'ARMSWISS' => [
            '514780', '515804', '511738',
        ],
    ],

    'ALERT' => [
        'ACTIONS' => [
            'EKENG' => 'EKENG',
            'EKENG_SSN' => 'EKENG_SSN',
            'EKENG_PASSPORT' => 'EKENG_PASSPORT',
            'EKENG_NO_VEHICLE' => 'EKENG_NO_VEHICLE',
            'TELCELL_NO_BALANCE' => 'TELCELL_NO_BALANCE',
            'IDRAM_NO_BALANCE' => 'IDRAM_NO_BALANCE',
            'FASTSHIFT_NO_BALANCE' => 'FASTSHIFT_NO_BALANCE',
            'ARMECONOM_NO_BALANCE' => 'ARMECONOM_NO_BALANCE',
            'EVOCA_NO_BALANCE' => 'EVOCA_NO_BALANCE',
            'ACRA' => 'ACRA',
            'NORK' => 'NORK',
            'CASHME_JOB_FAILED' => 'CASHME_JOB_FAILED',
            'ACRA_MONITORING' => 'ACRA_MONITORING',
        ],

        'CASES' => [
            'LOGIN' => 'login',
            'NO_DATA' => 'noData',
            'NO_VEHICLE_DATA' => 'noVehicleData',
            'INSUFFICIENT_BALANCE' => 'insufficientBalance',
        ],
    ],

    'WALLET_AVAILABLE_AMOUNTS' => [
        20000,
        40000,
        80000,
        100000,
        150000,
    ],

    'OBL_END_DATE_FORMAT' => 'Y-m-d',

    'GET_EKENG_INFO_TIME_BY_MINUTES' => 1440, //by default 1440 min is 1 day

    'LOAN_DISBURSEMENT_TYPES' => [
        'PAYMENT' => 'PAYMENT',
        'TRANSACTION' => 'TRANSACTION',
        'BNPL_CONVERSION' => 'BNPL_CONVERSION',
    ],

    'BLACKLIST_REASONS' => [
        'SMS_VERIFICATION_ATTEMPTS' => 'SMS_VERIFICATION_ATTEMPTS',
        'EMAIL_VERIFICATION_CODE_ATTEMPTS' => 'EMAIL_VERIFICATION_CODE_ATTEMPTS',
        'SYSTEM_REJECT' => 'SYSTEM_REJECT',
        'LOAN_REJECTED' => 'LOAN_REJECTED',
        'RECOGNITION_NOT_SUCCESSFULL' => 'RECOGNITION_NOT_SUCCESSFULL',
        'NO_EKENG_PHOTO' => 'NO_EKENG_PHOTO',
        'UNKNOWN' => 'UNKNOWN',
    ],

    'BLACKLIST_TYPES' => [
        'BLOCK' => 'BLOCK',
        'UNBLOCK' => 'UNBLOCK',
    ],

    'GC_SUPPORT_WORKING_DAYS' => [
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
    ],

    'GC_SUPPORT_WORKING_HOURS' => [
        'FROM' => '05:00',
        'TO' => '13:45',
    ],

    'NOTIFICATION_TYPE' => [
        'PUSH' => 'PUSH',
        'IN_APP' => 'IN_APP',
    ],

    'REPAYMENT_LOCK_TIMEOUT' => 60, // in seconds

    'DEBT_AUTOREPAY_INFO' => [
        'AUTODEBT' => 'Y',
        'DEBTJPART1' => '2',
        'DEBTJPART' => '0',
        'ACCCONNMODE' => '3',
        'ACCCONNSCH' => '001',
    ],

    'PASSPORTS' => [
        'TYPES_BY_ORDER_PRIORITY' => [
            'NON_BIOMETRIC_PASSPORT',
            'ID_CARD',
            'BIOMETRIC_PASSPORT',
        ],
    ],

    'NOT_APPLICABLE' => 'N/A',

    'CREDIT_CODE_TIMEOUT' => 600,
];
