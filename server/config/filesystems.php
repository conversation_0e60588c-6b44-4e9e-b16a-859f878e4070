<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Filesystem Disk
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default filesystem disk that should be used
    | by the framework. The "local" disk, as well as a variety of cloud
    | based disks are available to your application. Just store away!
    |
    */

    'default' => 'local',

    /*
    |--------------------------------------------------------------------------
    | Default Cloud Filesystem Disk
    |--------------------------------------------------------------------------
    |
    | Many applications store files both locally and in the cloud. For this
    | reason, you may specify a default "cloud" driver here. This driver
    | will be bound as the Cloud disk implementation in the container.
    |
    */

    'cloud' => 's3',

    /*
    |--------------------------------------------------------------------------
    | Filesystem Disks
    |--------------------------------------------------------------------------
    |
    | Here you may configure as many filesystem "disks" as you wish, and you
    | may even configure multiple disks of the same driver. Defaults have
    | been setup for each driver as an example of the required options.
    |
    | Supported Drivers: "local", "ftp", "s3", "rackspace"
    |
    */

    'disks' => [
        'local' => [
            'driver' => 'local',
            'root' => storage_path('app'),
        ],

        'public' => [
            'driver' => 'local',
            'root' => storage_path('app/public'),
            'visibility' => 'public',
        ],

        'local_loan_documents' => [
            'driver' => 'local',
            'root' => public_path(),
        ],

        'local_medias_upload' => [
            'driver' => 'local',
            'root' => public_path(),
        ],

        'local_merchant_media_upload' => [
            'driver' => 'local',
            'root' => public_path().env('MERCHANT_MEDIA_DIR'),
            'url' => env('APP_URL').env('MERCHANT_MEDIA_DIR'),
        ],

        'arpi_solar_media_upload' => [
            'driver' => 'local',
            'root' => public_path().'/arpi_solar_media',
        ],

        'arpi_solar_kfw_excels' => [
            'driver' => 'local',
            'root' => public_path().'/kfw_excels',
        ],

        'vehicle_models_upload' => [
            'driver' => 'local',
            'root' => public_path().'/vehicle_models',
        ],

        'monitoring_uploaded_files' => [
            'driver' => 'local',
            'root' => public_path().'/monitoring',
        ],

        'stabilization_loan_upload' => [
            'driver' => 'local',
            'root' => public_path().'/stabilization_loans',
        ],

        'local_qr_codes' => [
            'driver' => 'local',
            'root' => public_path().env('QR_CODE_SUBDIR'),
            'url' => env('APP_URL').env('QR_CODE_SUBDIR'),
        ],

        'local_referral_code_pdfs' => [
            'driver' => 'local',
            'root' => public_path(),
        ],

        'local_ekeng_media' => [
            'driver' => 'local',
            'root' => public_path().env('EKENG_PHOTO_SUBDIR', '/ekeng'),
        ],

        'local_recognition_media' => [
            'driver' => 'local',
            'root' => public_path(),
        ],

        'ftp_iqos' => [
            'driver' => 'ftp',
            'host' => env('FTP_IQOS_HOST'),
            'username' => env('FTP_IQOS_USERNAME'),
            'password' => env('FTP_IQOS_PASSWORD'),
            'root' => '/Iqos',
        ],

        's3-ekeng-photo' => [
            'driver' => 's3',
            'key' => env('AWS_API_KEY'),
            'secret' => env('AWS_API_SECRET'),
            'region' => env('AWS_S3_REGION'),
            'bucket' => env('AWS_S3_EKENG_BUCKET'),
        ],

        's3-recognition' => [
            'driver' => 's3',
            'key' => env('AWS_API_KEY'),
            'secret' => env('AWS_API_SECRET'),
            'region' => env('AWS_S3_REGION'),
            'bucket' => env('AWS_S3_RECOGNITION_BUCKET'),
        ],

        's3-video' => [
            'driver' => 's3',
            'key' => env('AWS_API_KEY'),
            'secret' => env('AWS_API_SECRET'),
            'region' => env('AWS_S3_REGION'),
            'bucket' => env('AWS_S3_VIDEO_BUCKET'),
        ],

        's3-loan-documents' => [
            'driver' => 's3',
            'key' => env('AWS_API_KEY'),
            'secret' => env('AWS_API_SECRET'),
            'region' => env('AWS_S3_REGION'),
            'bucket' => env('AWS_S3_LOAN_DOCUMENTS_BUCKET'),
        ],

        's3-mortgage-media' => [
            'driver' => 's3',
            'key' => env('AWS_API_KEY'),
            'secret' => env('AWS_API_SECRET'),
            'region' => env('AWS_S3_REGION'),
            'bucket' => env('AWS_S3_MORTGAGE_MEDIA_BUCKET'),
        ],

        's3-qr-code' => [
            'driver' => 's3',
            'key' => env('AWS_API_KEY'),
            'secret' => env('AWS_API_SECRET'),
            'region' => env('AWS_S3_REGION'),
            'bucket' => env('AWS_S3_QR_CODE_BUCKET'),
        ],

        's3-referral-code-pdfs' => [
            'driver' => 's3',
            'key' => env('AWS_API_KEY'),
            'secret' => env('AWS_API_SECRET'),
            'region' => env('AWS_S3_REGION'),
            'bucket' => env('AWS_REFERRAL_CODE_PDFS_BUCKET'),
        ],
    ],
];
