<?php

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RuleGroupsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('rule_groups')->insert([
            [
                'merchant_name' => null,
                'product_category_id' => 1,
                'loan_type_id' => 11,
                'mi_tax_payer_id' => null,
                'group' => 'B',
            ],
            [
                'merchant_name' => null,
                'product_category_id' => 2,
                'loan_type_id' => 11,
                'mi_tax_payer_id' => null,
                'group' => 'A',
            ],
            [
                'merchant_name' => null,
                'product_category_id' => 3,
                'loan_type_id' => 11,
                'mi_tax_payer_id' => null,
                'group' => 'A',
            ],
            [
                'merchant_name' => null,
                'product_category_id' => 4,
                'loan_type_id' => 11,
                'mi_tax_payer_id' => null,
                'group' => 'A',
            ],
            [
                'merchant_name' => null,
                'product_category_id' => 5,
                'loan_type_id' => 11,
                'mi_tax_payer_id' => null,
                'group' => 'A',
            ],
            [
                'merchant_name' => null,
                'product_category_id' => 6,
                'loan_type_id' => 11,
                'mi_tax_payer_id' => null,
                'group' => 'A',
            ],
            [
                'merchant_name' => null,
                'product_category_id' => 7,
                'loan_type_id' => 11,
                'mi_tax_payer_id' => null,
                'group' => 'B',
            ],
            [
                'merchant_name' => null,
                'product_category_id' => 8,
                'loan_type_id' => 11,
                'mi_tax_payer_id' => null,
                'group' => 'A',
            ],
            [
                'merchant_name' => null,
                'product_category_id' => 9,
                'loan_type_id' => 11,
                'mi_tax_payer_id' => null,
                'group' => 'B',
            ],
            [
                'merchant_name' => null,
                'product_category_id' => 10,
                'loan_type_id' => 11,
                'mi_tax_payer_id' => null,
                'group' => 'A',
            ],
            [
                'merchant_name' => null,
                'product_category_id' => 11,
                'loan_type_id' => 11,
                'mi_tax_payer_id' => null,
                'group' => 'A',
            ],
            [
                'merchant_name' => null,
                'product_category_id' => 12,
                'loan_type_id' => 11,
                'mi_tax_payer_id' => null,
                'group' => 'A',
            ],
            [
                'merchant_name' => null,
                'product_category_id' => 13,
                'loan_type_id' => 11,
                'mi_tax_payer_id' => null,
                'group' => 'A',
            ],
            [
                'merchant_name' => null,
                'product_category_id' => 14,
                'loan_type_id' => 11,
                'mi_tax_payer_id' => null,
                'group' => 'A',
            ],
            [
                'merchant_name' => null,
                'product_category_id' => 20,
                'loan_type_id' => 11,
                'mi_tax_payer_id' => null,
                'group' => 'A',
            ],
            [
                'merchant_name' => null,
                'product_category_id' => 21,
                'loan_type_id' => 11,
                'mi_tax_payer_id' => null,
                'group' => 'A',
            ],
            [
                'merchant_name' => null,
                'product_category_id' => 23,
                'loan_type_id' => 11,
                'mi_tax_payer_id' => null,
                'group' => 'A',
            ],
            [
                'merchant_name' => 'CMShipping',
                'product_category_id' => null,
                'loan_type_id' => 2,
                'mi_tax_payer_id' => '02900508',
                'group' => 'A',
            ],
            [
                'merchant_name' => 'EcoMotors',
                'product_category_id' => null,
                'loan_type_id' => 2,
                'mi_tax_payer_id' => '00233358',
                'group' => 'B',
            ],
        ]);
    }
}
