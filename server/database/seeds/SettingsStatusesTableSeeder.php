<?php

use App\Models\SettingsStatus;
use Illuminate\Database\Seeder;

class SettingsStatusesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $settings = [
            ['name' => SettingsStatus::WEB, 'description' => 'Website Interface', 'disabled' => true],
            ['name' => SettingsStatus::APP, 'description' => 'Mobile Application', 'disabled' => false],
            ['name' => SettingsStatus::GET_EKENG_DATA_FROM_DB, 'description' => 'Get Ekeng data from DB when Ekeng is down', 'disabled' => false],
            ['name' => SettingsStatus::TOP_UP, 'description' => 'Top-up Offer', 'disabled' => false],
            ['name' => SettingsStatus::FACE_RECOGNITION, 'description' => 'Face Recognition matching', 'disabled' => false],
            ['name' => SettingsStatus::IM_ID, 'description' => 'Im id Mobile Authentication Or Registration', 'disabled' => false],
        ];

        DB::table('settings_statuses')->insert($settings);
    }
}
