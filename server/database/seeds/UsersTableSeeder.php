<?php

use App\Models\AgentPassport;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class UsersTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $user = User::create(['email' => '<EMAIL>', 'password' => 'admin', 'first_name' => 'Admin', 'last_name' => 'Admin', 'middle_name' => 'Admin']);
        $user->assignRole('admin');

        $user = User::create(['email' => '<EMAIL>', 'password' => 'cashier', 'first_name' => 'GC', 'last_name' => 'Admin', 'middle_name' => 'Admin']);
        $user->assignRole('gc-cashier');

        $user = User::create(['email' => '<EMAIL>', 'password' => 'cashier', 'first_name' => 'Easypay', 'last_name' => 'User', 'middle_name' => 'Easypay']);
        $user->assignRole('easypay-cashier');

        $user = User::create(['email' => '<EMAIL>', 'password' => 'cashier', 'first_name' => 'Upay', 'last_name' => 'User', 'middle_name' => 'Upay']);
        $user->assignRole('upay-cashier');

        $user = User::create(['email' => '<EMAIL>', 'password' => 'admin', 'first_name' => 'Upay', 'last_name' => 'Admin', 'middle_name' => 'Upay']);
        $user->assignRole('upay-admin');

        $user = User::create(['email' => '<EMAIL>', 'password' => 'cashier', 'first_name' => 'Telcell', 'last_name' => 'User', 'middle_name' => 'Telcell']);
        $user->assignRole('telcell-cashier');

        $user = User::create(['email' => '<EMAIL>', 'password' => 'admin', 'first_name' => 'Telcell', 'last_name' => 'Admin', 'middle_name' => 'Telcell']);
        $user->assignRole('telcell-admin');

        $user = User::create(['email' => '<EMAIL>', 'password' => 'admin', 'first_name' => 'Easypay', 'last_name' => 'Admin', 'middle_name' => 'Easypay']);
        $user->assignRole('easypay-admin');

        $user = User::create(['email' => '<EMAIL>', 'password' => 'admin', 'first_name' => 'GC', 'last_name' => 'Admin', 'middle_name' => 'GC']);
        $user->assignRole('gc-admin');

        $user = User::create([
            'email' => '<EMAIL>',
            'password' => 'accountant',
            'first_name' => 'GCA1',
            'last_name' => 'AGENT1',
            'middle_name' => 'AGENT',
            'phone_number' => '+***********',
        ]);
        $user->assignRole('globalcredit-agent');

        AgentPassport::create([
            'user_id' => $user->id,
            'passport_number' => '*********',
            'given_date' => '2010-08-03',
            'expire_date' => '2020-08-03',
            'from' => '001',
            'address' => 'Արարատի մարզ, գ. Վեդի 1փ. 22տ.',
        ]);

        $user = User::create([
            'email' => '<EMAIL>',
            'password' => 'accountant',
            'first_name' => 'GCA2',
            'last_name' => 'AGENT2',
            'middle_name' => 'AGENT',
            'phone_number' => '+***********',
        ]);
        $user->assignRole('globalcredit-agent');

        AgentPassport::create([
            'user_id' => $user->id,
            'passport_number' => '*********',
            'given_date' => '2012-10-30',
            'expire_date' => '2022-10-30',
            'from' => '002',
            'address' => 'Արարատի մարզ, գ. Վեդի 1փ. 22տ.',
        ]);

        $user = User::create([
            'email' => '<EMAIL>',
            'password' => 'accountant',
            'first_name' => 'GCA3',
            'last_name' => 'AGENT3',
            'middle_name' => 'AGENT',
            'phone_number' => '+***********',
        ]);
        $user->assignRole('globalcredit-agent');

        AgentPassport::create([
            'user_id' => $user->id,
            'passport_number' => '*********',
            'given_date' => '2012-10-30',
            'expire_date' => '2022-10-30',
            'from' => '003',
            'address' => 'Արարատի մարզ, գ. Վեդի 1փ. 22տ.',
        ]);

        $user = User::create(['email' => '<EMAIL>', 'password' => 'admin', 'first_name' => 'GCA-Admin', 'last_name' => 'AGENTADMIN', 'middle_name' => 'AGENTADMIN', 'phone_number' => '+***********']);
        $user->assignRole('globalcredit-agent-admin');

        $user = User::create(['email' => '<EMAIL>', 'password' => 'ovlviewer', 'first_name' => 'OVL', 'last_name' => 'Viewer', 'middle_name' => 'GC']);
        $user->assignRole('ovl-viewer');

        $user = User::create(['email' => '<EMAIL>', 'password' => 'seller', 'first_name' => 'IQOS', 'last_name' => 'Seller', 'middle_name' => 'Seller', 'phone_number' => '+37400000001']);
        // In order to be able to specify the guard name, we should instanciate a role
        $user->assignRole(app(Role::class)->findByName('iqos-seller', 'api'));

        $user = User::create(['email' => '<EMAIL>', 'password' => 'moderator', 'first_name' => 'Moderator', 'last_name' => 'Moderator', 'middle_name' => 'Moderator', 'phone_number' => '+37400000002']);
        // In order to be able to specify the guard name, we should instanciate a role
        $user->assignRole(app(Role::class)->findByName('moderator', 'api'));

        $user = User::create(['email' => '<EMAIL>', 'password' => 'moderator', 'first_name' => 'Moderator', 'last_name' => 'Moderator', 'middle_name' => 'Moderator', 'phone_number' => '+37400000003']);
        // In order to be able to specify the guard name, we should instanciate a role
        $user->assignRole(app(Role::class)->findByName('moderator', 'api'));

        $user = User::create(['email' => '<EMAIL>', 'password' => 'supermoderator', 'first_name' => 'Moderator', 'last_name' => 'Moderator', 'middle_name' => 'Moderator', 'phone_number' => '+37400000004']);
        // In order to be able to specify the guard name, we should instanciate a role
        $user->assignRole(app(Role::class)->findByName('moderator', 'api'));
        $user->givePermissionTo(app(Permission::class)->findByName('view-loan-amount', 'api'));

        $user = User::create(['email' => '<EMAIL>', 'password' => '', 'first_name' => 'system', 'last_name' => 'system', 'middle_name' => 'system', 'phone_number' => '+37400000000']);
        // In order to be able to specify the guard name, we should instanciate a role
        $user->assignRole(app(Role::class)->findByName('system', 'web'));

        $user = User::create(['email' => '<EMAIL>', 'password' => 'agent', 'first_name' => 'Arpi', 'last_name' => 'Solar', 'middle_name' => 'Agent', 'phone_number' => '+37490000000']);
        $user->assignRole(app(Role::class)->findByName('oasl-agent', 'api'));

        $user = User::create(['email' => '<EMAIL>', 'password' => 'admin', 'first_name' => 'OASL', 'last_name' => 'Admin', 'middle_name' => 'Arpi', 'phone_number' => '+37488888888']);
        $user->assignRole('oasl-admin');

        $user = User::create(['email' => '<EMAIL>', 'password' => 'admin', 'first_name' => 'OASL', 'last_name' => 'Sub', 'middle_name' => 'Admin', 'phone_number' => '+37400000005']);
        $user->assignRole('oasl-sub-admin');

        $user = User::create(['email' => '<EMAIL>', 'password' => 'admin', 'first_name' => 'OASL', 'last_name' => 'GC', 'middle_name' => 'Admin', 'phone_number' => '+37400000006']);
        $user->assignRole('oasl-gc-admin');

        $user = User::create(['email' => '<EMAIL>', 'password' => 'admin', 'first_name' => 'OASL', 'last_name' => 'NMC', 'middle_name' => 'Agent', 'phone_number' => '+37400000007']);
        $user->assignRole('oasl-nmc-agent');

        $user = User::create(['email' => '<EMAIL>', 'password' => 'admin', 'first_name' => 'Loan Support', 'last_name' => 'GC', 'middle_name' => 'Admin', 'phone_number' => '+37498287520']);
        $user->assignRole('loan-support-admin');

        $user = User::create(['email' => '<EMAIL>', 'password' => 'admin', 'first_name' => 'GC', 'last_name' => 'Sub', 'middle_name' => 'Admin', 'phone_number' => '+37498000000']);
        $user->assignRole('gc-sub-admin');

        $user = User::create(['email' => '<EMAIL>', 'password' => 'admin', 'first_name' => 'Telcell', 'last_name' => 'Sub', 'middle_name' => 'Admin', 'phone_number' => '+37477000000']);
        $user->assignRole('telcell-sub-admin');

        $user = User::create(['email' => '<EMAIL>', 'password' => 'admin', 'first_name' => 'QR', 'last_name' => 'Code', 'middle_name' => 'Admin', 'phone_number' => '+37400000008']);
        $user->givePermissionTo(['view-nova', 'view-qr-codes', 'view-loan-history', 'view-cash', 'view-full-history', 'view-pdfs', 'view-referral-code', 'regenerate-documents', 'send-documents', 'view-merchant']);

        $user = User::create([
            'email' => '<EMAIL>',
            'password' => 'acramonitoring',
            'first_name' => 'Acra',
            'last_name' => 'Monitoring',
            'middle_name' => 'User',
        ]);
        $user->assignRole('acra-monitoring');

        $user = User::create(['email' => '<EMAIL>', 'password' => 'mobile', 'first_name' => 'Mobile', 'last_name' => 'User', 'middle_name' => 'User', 'phone_number' => '+37494755763']);
        $user->assignRole(app(Role::class)->findByName('mobile-user', 'api'));

        $profile = $user->profile()->create([
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'birth_date' => Carbon::create('1988', '05', '10'),
            'email' => $user->email,
            'phone_number' => $user->phone_number,
            'document_number' => '*********',
            'ssn' => '1101000631',
            'address' => 'ՀՀ, Երեվան Հակոբ Հակոբյան 3',
        ]);
        $profile->save();

        $user = User::create(['email' => '<EMAIL>', 'password' => 'admin', 'first_name' => 'EC', 'last_name' => 'Reml', 'middle_name' => 'Admin', 'phone_number' => '+37443311330']);
        $user->assignRole('ec-reml-admin');

        $user = User::create(['email' => '<EMAIL>', 'password' => 'admin', 'first_name' => 'GC', 'last_name' => 'Reml', 'middle_name' => 'Admin', 'phone_number' => '+37477316566']);
        $user->assignRole('gc-reml-admin');

        $user = User::create(['email' => '<EMAIL>', 'password' => 'admin', 'first_name' => 'GC', 'last_name' => 'Reml', 'middle_name' => 'SubAdmin', 'phone_number' => '+37493803400']);
        $user->assignRole('gc-reml-sub-admin');

        $user = User::create(['email' => '<EMAIL>', 'password' => 'pladmin', 'first_name' => 'Pl', 'last_name' => 'Admin', 'middle_name' => 'Pl admin']);
        $user->assignRole('pl-admin');

        $user = User::create(['email' => '<EMAIL>', 'password' => 'admin', 'first_name' => 'GC', 'last_name' => 'Viewer', 'middle_name' => 'GC Viewer', 'phone_number' => '+37400000010']);
        $user->assignRole(['gc-reml-viewer']);

        $user = User::create(['email' => '<EMAIL>', 'password' => 'agent', 'first_name' => 'Merchant', 'last_name' => 'Agent', 'middle_name' => 'Agent', 'phone_number' => '+37400000011']);
        $user->assignRole(app(Role::class)->findByName('merchant-agent', 'api'));

        $user = User::create(['email' => '<EMAIL>', 'password' => 'agent', 'first_name' => 'Merchant Agent', 'last_name' => 'OVIL', 'middle_name' => 'Agent', 'phone_number' => '+37400000112']);
        $user->assignRole(app(Role::class)->findByName('merchant-agent-ovil', 'api'));
    }
}
