<?php

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MarketingMediaTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('marketing_media')->insert([
            [
                'image' => '/assets/pallaton/carousel/cash-me.png',
                'title' => 'Քո ֆինանսական ընկերը',
                'icon' => env('APP_URL').'/assets/pallaton/carousel/cashme-white.svg',
                'is_trading' => false,
                'description' => 'Օրվա ցանկացած ժամի, ցանկացած վայրում',
                'screen_title' => 'GET_IT_NOW_OCL_SCREEN',
                'loan_type_id' => 1,
            ],
            [
                'image' => '/assets/pallaton/carousel/vehicle-trading.jpg',
                'title' => 'Քո ֆինանսական ընկերը',
                'icon' => env('APP_URL').'/assets/pallaton/carousel/cashme-white.svg',
                'is_trading' => true,
                'description' => 'Օրվա ցանկացած ժամի, ցանկացած վայրում',
                'screen_title' => 'GET_IT_NOW_OVL_SCREEN',
                'loan_type_id' => 2,
            ],
            [
                'image' => '/assets/pallaton/carousel/travel.jpg',
                'title' => 'Քո ֆինանսական ընկերը',
                'icon' => env('APP_URL').'/assets/pallaton/carousel/cashme-white.svg',
                'is_trading' => false,
                'description' => 'Օրվա ցանկացած ժամի, ցանկացած վայրում',
                'screen_title' => 'GET_IT_NOW_OVL_SCREEN',
                'loan_type_id' => 2,
            ],
            [
                'image' => '/assets/pallaton/carousel/bnpl.jpg',
                'title' => 'Մաս-մաս',
                'icon' => env('APP_URL').'/assets/pallaton/carousel/bnpl-white.svg',
                'is_trading' => false,
                'description' => 'Քո ցանկացած ապրանքը կամ ծառայությունը 0% տոկոսադրույքով',
                'screen_title' => 'GET_IT_NOW_BNPL_SCREEN',
                'loan_type_id' => 7,
            ],
        ]);
    }
}
