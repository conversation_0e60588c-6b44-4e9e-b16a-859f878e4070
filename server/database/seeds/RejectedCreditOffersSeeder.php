<?php

use App\Models\CreditOffer;
use function Functional\some;
use Illuminate\Database\Seeder;

class RejectedCreditOffersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        CreditOffer::with('affected_rules')->chunkById(100, function ($credit_offers) {
            foreach ($credit_offers as $credit_offer) {
                $rejected = some($credit_offer->affected_rules, function ($rule) {
                    return $rule->rejected === true;
                });

                $credit_offer->update(['rejected' => $rejected]);
            }
        });
    }
}
