<?php

namespace Database\Seeders;

class RulesTableOCLSeeder extends AbstractRulesTableSeeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $ocl_rules = [
            [
                'name' => 'osmAmount',
                'label' => 'osmAmount',
                'formula' => true,
                'amount' => json_encode(['var' => 'osmAmount']),
                'resolvable' => true,
                'disabled' => false,
                'phase' => 3,
            ],
            [
                'name' => 'dstiAmount',
                'label' => 'dstiAmount',
                'formula' => true,
                'amount' => json_encode(['var' => 'dstiAmount']),
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            [
                'name' => 'isDead',
                'label' => 'isDead',
                'formula' => json_encode(['==' => [['var' => 'isDead'], 'true']]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'age',
                'label' => 'age',
                'formula' => json_encode(['or' => [['<' => [['var' => 'age'], 21]], ['>' => [['var' => 'age'], 65]]]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'citizenship',
                'label' => 'citizenship',
                'formula' => json_encode(['!=' => [['var' => 'citizenship'], 'ARM']]),
                'amount' => '0',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'hasAddress',
                'label' => 'hasAddress',
                'formula' => json_encode(['==' => [['var' => 'hasAddress'], false]]),
                'amount' => '0',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'loanClass',
                'label' => 'loanClass',
                'formula' => json_encode(['and' => [['!=' => [['var' => 'loanClass'], 'Ստանդարտ']], ['!=' => [['var' => 'loanClass'], null]]]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'hasOverdueLoans',
                'label' => 'hasOverdueLoans',
                'formula' => json_encode(['==' => [['var' => 'hasOverdueLoans'], true]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'acraActiveLoans',
                'label' => 'acraActiveLoans',
                'formula' => json_encode(['and' => [['>' => [['var' => 'acraActiveLoans'], 8]], ['==' => [['var' => 'isNewOclCustomer'], true]]]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 4,
            ],
            [
                'name' => 'isValidLoanClass12',
                'label' => 'isValidLoanClass12',
                'formula' => json_encode(['==' => [['var' => 'isValidLoanClass12'], false]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'acraStatus',
                'label' => 'acraStatus',
                'formula' => json_encode(['==' => [['var' => 'acraStatus'], '2']]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'hasNonSyncedCredits',
                'label' => 'hasNonSyncedCredits',
                'formula' => json_encode(['==' => [['var' => 'hasNonSyncedCredits'], true]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'notAcceptableTopUpLoanTypeId',
                'label' => 'notAcceptableTopUpLoanTypeId',
                'formula' => json_encode(
                    ['if' => [['and' => [
                        ['==' => [['var' => 'hasCredit'], true]],
                        ['==' => [['var' => 'notAcceptableTopUpLoanTypeId'], true]], ],
                    ]]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'hasTopUpEligibility',
                'label' => 'hasTopUpEligibility',
                'formula' => json_encode(
                    ['if' => [['==' => [['var' => 'hasCredit'], true]],
                        ['or' => [
                            ['==' => [['var' => 'minMonthlyPaymentDatePassed'], 0]],
                            ['==' => [['var' => 'minDisbursementDatePassed'], 0]],
                            ['==' => [['var' => 'hasValidLoanClass'], 0]],
                            ['==' => [['var' => 'hasPendingMonthlyPayment'], 1]],
                            ['==' => [['var' => 'hasOverdueFee'], 1]],
                            ['==' => [['var' => 'hasAdvancePayment'], 1]],
                            ['==' => [['var' => 'hasDayCloseIssue'], 1]],
                        ]],
                        false,
                    ],
                    ]
                ),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'topUpAmount',
                'label' => 'topUpAmount',
                'formula' => json_encode(['==' => [['var' => 'hasCredit'], 'true']]),
                'amount' => json_encode(['var' => 'topUpAmount']),
                'resolvable' => true,
                'disabled' => false,
                'phase' => 5,
            ],
            // In general, we are enabling this rule when TOP_UP key in SettingStatuses is true
            [
                'name' => 'hasCredit',
                'label' => 'hasCredit',
                'formula' => json_encode(['==' => [['var' => 'hasCredit'], 'true']]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => true,
                'phase' => 1,
            ],
            [
                'name' => 'dailyLimits',
                'label' => 'dailyLimits',
                'formula' => json_encode(
                    ['if' => [['and' => [['==' => [['var' => 'isWeekend'], null]], ['==' => [['var' => 'isNight'], null]]]],
                        ['or' => [['>=' => [['var' => 'totalAmount'], 1000000]], ['>=' => [['var' => 'totalCount'], 6]]]],
                        false,
                    ],
                    ]
                ),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => true,
                'phase' => 1,
            ],
            [
                'name' => 'weekendLimits',
                'label' => 'weekendLimits',
                'formula' => json_encode(
                    ['if' => [['and' => [['==' => [['var' => 'isWeekend'], 'true']], ['==' => [['var' => 'isNight'], null]]]],
                        ['or' => [['>=' => [['var' => 'totalAmount'], 900000]], ['>=' => [['var' => 'totalCount'], 5]]]],
                        false,
                    ],
                    ]
                ),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => true,
                'phase' => 1,
            ],
            [
                'name' => 'weekendNightLimits',
                'label' => 'weekendNightLimits',
                'formula' => json_encode(
                    ['if' => [['and' => [['==' => [['var' => 'isWeekend'], 'true']], ['==' => [['var' => 'isNight'], 'true']]]],
                        ['or' => [['>=' => [['var' => 'totalNightAmount'], 300000]], ['>=' => [['var' => 'totalNightCount'], 3]]]],
                        false,
                    ],
                    ]
                ),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => true,
                'phase' => 1,
            ],
            [
                'name' => 'nightLimits',
                'label' => 'nightLimits',
                'formula' => json_encode(
                    ['if' => [['and' => [['==' => [['var' => 'isWeekend'], null]], ['==' => [['var' => 'isNight'], 'true']]]],
                        ['or' => [['>=' => [['var' => 'totalNightAmount'], 700000]], ['>=' => [['var' => 'totalNightCount'], 4]]]],
                        false,
                    ],
                    ]
                ),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => true,
                'phase' => 1,
            ],
            [
                'name' => 'isQKH',
                'label' => 'isQKH',
                'formula' => json_encode(['==' => [['var' => 'isQKH'], 'true']]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'hasPendingVeloxLoan',
                'label' => 'hasPendingVeloxLoan',
                'formula' => json_encode(['==' => [['var' => 'hasPendingVeloxLoan'], 'true']]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'qrDiscount',
                'label' => 'qr_discount',
                'formula' => true,
                'rate' => json_encode(['var' => 'qrDiscount']),
                'resolvable' => false,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0_0.05_age_21_25_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'drScore'], 0]],
                    ['<=' => [['var' => 'drScore'], 0.05]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 38,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_501_549_age_21_25_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 501]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 74,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0_0.05_age_26_30_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'drScore'], 0]],
                    ['<=' => [['var' => 'drScore'], 0.05]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 34,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_501_549_age_26_30_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 501]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 74,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0_0.05_age_31_40_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'drScore'], 0]],
                    ['<=' => [['var' => 'drScore'], 0.05]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 36,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_501_549_age_31_40_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 501]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 74,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0_0.05_age_41_50_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'drScore'], 0]],
                    ['<=' => [['var' => 'drScore'], 0.05]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 36,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_501_549_age_41_50_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 501]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 72,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0_0.05_age_51_65_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'drScore'], 0]],
                    ['<=' => [['var' => 'drScore'], 0.05]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 36,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_501_549_age_51_65_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 501]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 67,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0.05_0.1_age_21_25_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.05]],
                    ['<=' => [['var' => 'drScore'], 0.1]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 42,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_550_599_age_21_25_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 63,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_0.05_0.1_age_26_30_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.05]],
                    ['<=' => [['var' => 'drScore'], 0.1]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 42,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_550_599_age_26_30_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 70,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_0.05_0.1_age_31_40_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.05]],
                    ['<=' => [['var' => 'drScore'], 0.1]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 38,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_550_599_age_31_40_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 70,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_0.05_0.1_age_41_50_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.05]],
                    ['<=' => [['var' => 'drScore'], 0.1]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 36,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_550_599_age_41_50_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 72,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_0.05_0.1_age_51_65_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.05]],
                    ['<=' => [['var' => 'drScore'], 0.1]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 42,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_550_599_age_51_65_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 69,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0.1_0.15_age_21_25_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.1]],
                    ['<=' => [['var' => 'drScore'], 0.15]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 50,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_600_649_age_21_25_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 50,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0.1_0.15_age_26_30_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.1]],
                    ['<=' => [['var' => 'drScore'], 0.15]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 51,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_600_649_age_26_30_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 48,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0.1_0.15_age_31_40_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.1]],
                    ['<=' => [['var' => 'drScore'], 0.15]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 45,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_600_649_age_31_40_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 55,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0.1_0.15_age_41_50_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.1]],
                    ['<=' => [['var' => 'drScore'], 0.15]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 40,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_600_649_age_41_50_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 60,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0.1_0.15_age_51_65_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.1]],
                    ['<=' => [['var' => 'drScore'], 0.15]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 59,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_600_649_age_51_65_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 57,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0.15_0.2_age_21_25_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.15]],
                    ['<=' => [['var' => 'drScore'], 0.2]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 64,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_650_699_age_21_25_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 41,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0.15_0.2_age_26_30_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.15]],
                    ['<=' => [['var' => 'drScore'], 0.2]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 59,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_650_699_age_26_30_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 38,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0.15_0.2_age_31_40_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.15]],
                    ['<=' => [['var' => 'drScore'], 0.2]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 54,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_650_699_age_31_40_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 39,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0.15_0.2_age_41_50_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.15]],
                    ['<=' => [['var' => 'drScore'], 0.2]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 60,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_650_699_age_41_50_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 40,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0.15_0.2_age_51_65_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.15]],
                    ['<=' => [['var' => 'drScore'], 0.2]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 65,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_650_699_age_51_65_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 44,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0.2_0.3_age_21_25_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.2]],
                    ['<=' => [['var' => 'drScore'], 0.3]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 73,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_700_749_age_21_25_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 34,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0.2_0.3_age_26_30_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.2]],
                    ['<=' => [['var' => 'drScore'], 0.3]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 76,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_700_749_age_26_30_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 34,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0.2_0.3_age_31_40_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.2]],
                    ['<=' => [['var' => 'drScore'], 0.3]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 67,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_700_749_age_31_40_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 34,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0.2_0.3_age_41_50_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.2]],
                    ['<=' => [['var' => 'drScore'], 0.3]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 74,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_700_749_age_41_50_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 34,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0.2_0.3_age_51_65_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.2]],
                    ['<=' => [['var' => 'drScore'], 0.3]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 66,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_700_749_age_51_65_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 34,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0.3_0.4_age_21_25_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.3]],
                    ['<=' => [['var' => 'drScore'], 0.4]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 80,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_750_age_21_25_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 34,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0.3_0.4_age_26_30_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.3]],
                    ['<=' => [['var' => 'drScore'], 0.4]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 80,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_750_age_26_30_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 34,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0.3_0.4_age_31_40_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.3]],
                    ['<=' => [['var' => 'drScore'], 0.4]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 73,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_750_age_31_40_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 34,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0.3_0.4_age_41_50_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.3]],
                    ['<=' => [['var' => 'drScore'], 0.4]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 80,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_750_age_41_50_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 34,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_0.3_0.4_age_51_65_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'drScore'], 0.3]],
                    ['<=' => [['var' => 'drScore'], 0.4]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'rate' => 68,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_750_age_51_65_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'rate' => 34,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_dr_score_greater_or_less_0_0.2_age_21_65_newOclCustomer',
                'formula' => json_encode(
                    ['if' => [['and' => [['==' => [['var' => 'isNewOclCustomer'], true]]]],
                        ['or' => [
                            ['<' => [['var' => 'age'], 21]],
                            ['>' => [['var' => 'age'], 65]],
                            ['>' => [['var' => 'drScore'], 0.2]],
                            ['<' => [['var' => 'drScore'], 0]],
                        ]],
                        false,
                    ],
                    ]
                ),
                'rate' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate_fico_missing_0_501_age_21_65_oldOclCustomer',
                'formula' => json_encode(
                    ['if' => [['and' => [['==' => [['var' => 'isNewOclCustomer'], false]]]],
                        ['or' => [
                            ['<' => [['var' => 'age'], 21]],
                            ['>' => [['var' => 'age'], 65]],
                            ['<' => [['var' => 'fico'], 501]],
                            ['if' => [['missing' => ['fico']], true, false]],
                        ]],
                        false,
                    ],
                    ]
                ),
                'rate' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRateDiscount',
                'label' => 'interestRate_discount',
                'formula' => json_encode(['>' => [['var' => 'qrDiscount'], '0']]),
                'rate' => '$interestRate - $qrDiscount',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_501_549_age_21_25_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 501]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 70,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0_0.05_age_21_25_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'drScore'], 0]],
                    ['<=' => [['var' => 'drScore'], 0.05]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 36,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_501_549_age_26_30_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 501]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 70,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0_0.05_age_26_30_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'drScore'], 0]],
                    ['<=' => [['var' => 'drScore'], 0.05]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 24,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_501_549_age_31_40_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 501]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 70,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0_0.05_age_31_40_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'drScore'], 0]],
                    ['<=' => [['var' => 'drScore'], 0.05]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 24,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_501_549_age_41_50_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 501]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 70,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0_0.05_age_41_50_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'drScore'], 0]],
                    ['<=' => [['var' => 'drScore'], 0.05]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 24,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_501_549_age_51_65_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 501]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 55,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0_0.05_age_51_65_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'drScore'], 0]],
                    ['<=' => [['var' => 'drScore'], 0.05]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 24,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.05_0.1_age_21_25_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.05]],
                    ['<=' => [['var' => 'drScore'], 0.1]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 36,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_550_599_age_21_25_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 55,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.05_0.1_age_26_30_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.05]],
                    ['<=' => [['var' => 'drScore'], 0.1]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 36,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_550_599_age_26_30_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 64,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.05_0.1_age_31_40_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.05]],
                    ['<=' => [['var' => 'drScore'], 0.1]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 36,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_550_599_age_31_40_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 64,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.05_0.1_age_41_50_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.05]],
                    ['<=' => [['var' => 'drScore'], 0.1]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 24,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_550_599_age_41_50_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 70,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.05_0.1_age_51_65_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.05]],
                    ['<=' => [['var' => 'drScore'], 0.1]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 36,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_550_599_age_51_65_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 55,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.1_0.15_age_21_25_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.1]],
                    ['<=' => [['var' => 'drScore'], 0.15]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 48,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_600_649_age_21_25_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 36,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.1_0.15_age_26_30_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.1]],
                    ['<=' => [['var' => 'drScore'], 0.15]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 48,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_600_649_age_26_30_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 36,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.1_0.15_age_31_40_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.1]],
                    ['<=' => [['var' => 'drScore'], 0.15]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 36,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_600_649_age_31_40_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 48,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.1_0.15_age_41_50_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.1]],
                    ['<=' => [['var' => 'drScore'], 0.15]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 36,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_600_649_age_41_50_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 55,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.1_0.15_age_51_65_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.1]],
                    ['<=' => [['var' => 'drScore'], 0.15]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 48,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_600_649_age_51_65_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 55,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.15_0.2_age_21_25_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.15]],
                    ['<=' => [['var' => 'drScore'], 0.2]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 48,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_650_699_age_21_25_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 36,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.15_0.2_age_26_30_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.15]],
                    ['<=' => [['var' => 'drScore'], 0.2]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 48,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_650_699_age_26_30_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 36,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.15_0.2_age_31_40_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.15]],
                    ['<=' => [['var' => 'drScore'], 0.2]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 48,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_650_699_age_31_40_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 36,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.15_0.2_age_41_50_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.15]],
                    ['<=' => [['var' => 'drScore'], 0.2]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 48,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_650_699_age_41_50_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 36,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.15_0.2_age_51_65_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.15]],
                    ['<=' => [['var' => 'drScore'], 0.2]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 64,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_650_699_age_51_65_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 36,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.2_0.3_age_21_25_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.2]],
                    ['<=' => [['var' => 'drScore'], 0.3]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 64,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_700_749_age_21_25_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 24,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.2_0.3_age_26_30_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.2]],
                    ['<=' => [['var' => 'drScore'], 0.3]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 64,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_700_749_age_26_30_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 24,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.2_0.3_age_31_40_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.2]],
                    ['<=' => [['var' => 'drScore'], 0.3]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 64,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_700_749_age_31_40_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 24,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.2_0.3_age_41_50_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.2]],
                    ['<=' => [['var' => 'drScore'], 0.3]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 64,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_700_749_age_41_50_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 24,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.2_0.3_age_51_65_newOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.2]],
                    ['<=' => [['var' => 'drScore'], 0.3]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 64,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_700_749_age_51_65_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 24,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.3_0.4_age_21_25_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.3]],
                    ['<=' => [['var' => 'drScore'], 0.4]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 64,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_750_age_21_25_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 24,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.3_0.4_age_26_30_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.3]],
                    ['<=' => [['var' => 'drScore'], 0.4]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 64,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_750_age_26_30_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 24,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.3_0.4_age_31_40_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.3]],
                    ['<=' => [['var' => 'drScore'], 0.4]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 64,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_750_age_31_40_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 24,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.3_0.4_age_41_50_newCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.3]],
                    ['<=' => [['var' => 'drScore'], 0.4]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 64,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_750_age_41_50_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 24,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_0.3_0.4_age_51_65_newOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.3]],
                    ['<=' => [['var' => 'drScore'], 0.4]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                ]]),
                'service_fee_rate' => 64,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_750_age_51_65_oldCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'service_fee_rate' => 24,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_fico_missing_0_501_age_21_65_oldOclCustomer',
                'formula' => json_encode(
                    ['if' => [['and' => [['==' => [['var' => 'isNewOclCustomer'], false]]]],
                        ['or' => [
                            ['<' => [['var' => 'age'], 21]],
                            ['>' => [['var' => 'age'], 65]],
                            ['<' => [['var' => 'fico'], 501]],
                            ['if' => [['missing' => ['fico']], true, false]],
                        ]],
                        false,
                    ],
                    ]
                ),
                'rate' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate_dr_score_greater_or_less_0_0.2_age_21_65_newOclCustomer',
                'formula' => json_encode(
                    ['if' => [['and' => [['==' => [['var' => 'isNewOclCustomer'], true]]]],
                        ['or' => [
                            ['<' => [['var' => 'age'], 21]],
                            ['>' => [['var' => 'age'], 65]],
                            ['>' => [['var' => 'drScore'], 0.2]],
                            ['<' => [['var' => 'drScore'], 0]],
                        ]],
                        false,
                    ],
                    ]
                ),
                'service_fee_rate' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_501_549_age_21_25_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 501]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '180000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_501_549_age_26_30_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 501]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '400000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_501_549_age_31_40_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 501]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '180000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_501_549_age_41_50_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 501]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '250000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_501_549_age_51_65_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 501]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '160000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_550_599_age_21_25_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '300000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_550_599_age_26_30_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '550000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_550_599_age_31_40_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '250000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_550_599_age_41_50_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '350000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_550_599_age_51_65_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '160000', // in rule docs there is a mistake it should be 160 000
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_600_649_age_21_25_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '420000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_600_649_age_26_30_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '550000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_600_649_age_31_40_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '360000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_600_649_age_41_50_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '550000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_600_649_age_51_65_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '160000', // in rule docs there is a mistake it should be 160 000
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_650_699_age_21_25_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '450000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_650_699_age_26_30_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '600000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_650_699_age_31_40_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '420000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_650_699_age_41_50_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '550000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_650_699_age_51_65_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '160000', // in rule docs there is a mistake it should be 160 000
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_700_749_age_21_25_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '550000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_700_749_age_26_30_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '600000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_700_749_age_31_40_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '550000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_700_749_age_41_50_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '600000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_700_749_age_51_65_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '200000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_750_age_21_25_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '600000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_750_age_26_30_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '600000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_750_age_31_40_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '600000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_750_age_41_50_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '600000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_750_age_51_65_oldOclCustomer',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '250000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0_0.05_age_21_25_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'drScore'], 0]],
                    ['<=' => [['var' => 'drScore'], 0.05]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '330000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0_0.05_age_21_25_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'drScore'], 0]],
                    ['<=' => [['var' => 'drScore'], 0.05]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '310000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0_0.05_age_26_30_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'drScore'], 0]],
                    ['<=' => [['var' => 'drScore'], 0.05]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '460000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0_0.05_age_26_30_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'drScore'], 0]],
                    ['<=' => [['var' => 'drScore'], 0.05]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '440000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0_0.05_age_31_40_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'drScore'], 0]],
                    ['<=' => [['var' => 'drScore'], 0.05]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '410000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0_0.05_age_31_40_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'drScore'], 0]],
                    ['<=' => [['var' => 'drScore'], 0.05]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '390000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0_0.05_age_41_50_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'drScore'], 0]],
                    ['<=' => [['var' => 'drScore'], 0.05]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '380000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0_0.05_age_41_50_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'drScore'], 0]],
                    ['<=' => [['var' => 'drScore'], 0.05]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '360000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0_0.05_age_51_65_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'drScore'], 0]],
                    ['<=' => [['var' => 'drScore'], 0.05]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '380000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0_0.05_age_51_65_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'drScore'], 0]],
                    ['<=' => [['var' => 'drScore'], 0.05]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '360000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.05_0.1_age_21_25_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.05]],
                    ['<=' => [['var' => 'drScore'], 0.1]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '280000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.05_0.1_age_21_25_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.05]],
                    ['<=' => [['var' => 'drScore'], 0.1]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '260000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.05_0.1_age_26_30_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.05]],
                    ['<=' => [['var' => 'drScore'], 0.1]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '280000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.05_0.1_age_26_30_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.05]],
                    ['<=' => [['var' => 'drScore'], 0.1]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '260000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.05_0.1_age_31_40_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.05]],
                    ['<=' => [['var' => 'drScore'], 0.1]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '310000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.05_0.1_age_31_40_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.05]],
                    ['<=' => [['var' => 'drScore'], 0.1]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '290000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.05_0.1_age_41_50_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.05]],
                    ['<=' => [['var' => 'drScore'], 0.1]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '380000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.05_0.1_age_41_50_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.05]],
                    ['<=' => [['var' => 'drScore'], 0.1]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '360000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.05_0.1_age_51_65_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.05]],
                    ['<=' => [['var' => 'drScore'], 0.1]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '260000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.05_0.1_age_51_65_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.05]],
                    ['<=' => [['var' => 'drScore'], 0.1]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '280000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.1_0.15_age_21_25_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.1]],
                    ['<=' => [['var' => 'drScore'], 0.15]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '210000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.1_0.15_age_21_25_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.1]],
                    ['<=' => [['var' => 'drScore'], 0.15]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '230000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.1_0.15_age_26_30_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.1]],
                    ['<=' => [['var' => 'drScore'], 0.15]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '260000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.1_0.15_age_26_30_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.1]],
                    ['<=' => [['var' => 'drScore'], 0.15]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '280000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.1_0.15_age_31_40_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.1]],
                    ['<=' => [['var' => 'drScore'], 0.15]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '290000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.1_0.15_age_31_40_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.1]],
                    ['<=' => [['var' => 'drScore'], 0.15]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '310000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.1_0.15_age_41_50_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.1]],
                    ['<=' => [['var' => 'drScore'], 0.15]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '310000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.1_0.15_age_41_50_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.1]],
                    ['<=' => [['var' => 'drScore'], 0.15]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '330000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.1_0.15_age_51_65_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.1]],
                    ['<=' => [['var' => 'drScore'], 0.15]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '190000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.1_0.15_age_51_65_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.1]],
                    ['<=' => [['var' => 'drScore'], 0.15]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '210000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.15_0.2_age_21_25_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.15]],
                    ['<=' => [['var' => 'drScore'], 0.2]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '200000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.15_0.2_age_21_25_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.15]],
                    ['<=' => [['var' => 'drScore'], 0.2]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '220000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.15_0.2_age_26_30_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.15]],
                    ['<=' => [['var' => 'drScore'], 0.2]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '200000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.15_0.2_age_26_30_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.15]],
                    ['<=' => [['var' => 'drScore'], 0.2]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '220000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.15_0.2_age_31_40_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.15]],
                    ['<=' => [['var' => 'drScore'], 0.2]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '250000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.15_0.2_age_31_40_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.15]],
                    ['<=' => [['var' => 'drScore'], 0.2]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '270000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.15_0.2_age_41_50_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.15]],
                    ['<=' => [['var' => 'drScore'], 0.2]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '200000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.15_0.2_age_41_50_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.15]],
                    ['<=' => [['var' => 'drScore'], 0.2]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '220000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.15_0.2_age_51_65_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.15]],
                    ['<=' => [['var' => 'drScore'], 0.2]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '190000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.15_0.2_age_51_65_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.15]],
                    ['<=' => [['var' => 'drScore'], 0.2]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '210000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.2_0.3_age_21_25_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.2]],
                    ['<=' => [['var' => 'drScore'], 0.3]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '190000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.2_0.3_age_21_25_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.2]],
                    ['<=' => [['var' => 'drScore'], 0.3]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '210000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.2_0.3_age_26_30_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.2]],
                    ['<=' => [['var' => 'drScore'], 0.3]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '170000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.2_0.3_age_26_30_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.2]],
                    ['<=' => [['var' => 'drScore'], 0.3]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '190000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.2_0.3_age_31_40_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.2]],
                    ['<=' => [['var' => 'drScore'], 0.3]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '250000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.2_0.3_age_31_40_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.2]],
                    ['<=' => [['var' => 'drScore'], 0.3]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '270000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.2_0.3_age_41_50_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.2]],
                    ['<=' => [['var' => 'drScore'], 0.3]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '170000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.2_0.3_age_41_50_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.2]],
                    ['<=' => [['var' => 'drScore'], 0.3]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '190000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.2_0.3_age_51_65_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.2]],
                    ['<=' => [['var' => 'drScore'], 0.3]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '190000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.2_0.3_age_51_65_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.2]],
                    ['<=' => [['var' => 'drScore'], 0.3]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '210000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.3_0.4_age_21_25_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.3]],
                    ['<=' => [['var' => 'drScore'], 0.4]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '180000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.3_0.4_age_21_25_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.3]],
                    ['<=' => [['var' => 'drScore'], 0.4]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '200000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.3_0.4_age_26_30_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.3]],
                    ['<=' => [['var' => 'drScore'], 0.4]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '160000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.3_0.4_age_26_30_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.3]],
                    ['<=' => [['var' => 'drScore'], 0.4]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '180000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.3_0.4_age_31_40_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.3]],
                    ['<=' => [['var' => 'drScore'], 0.4]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '200000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.3_0.4_age_31_40_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.3]],
                    ['<=' => [['var' => 'drScore'], 0.4]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '220000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.3_0.4_age_41_50_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.3]],
                    ['<=' => [['var' => 'drScore'], 0.4]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '160000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.3_0.4_age_41_50_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.3]],
                    ['<=' => [['var' => 'drScore'], 0.4]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '180000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.3_0.4_age_51_65_newOclCustomer_salary_0',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.3]],
                    ['<=' => [['var' => 'drScore'], 0.4]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['<=' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '180000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.3_0.4_age_51_65_newOclCustomer_salary',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'drScore'], 0.3]],
                    ['<=' => [['var' => 'drScore'], 0.4]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['==' => [['var' => 'isNewOclCustomer'], true]],
                    ['>' => [['var' => 'salary'], 0]],
                ]]),
                'amount' => '200000',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'fico',
                'label' => 'fico_missing_0_501_age_21_65_oldOclCustomer',
                'formula' => json_encode(
                    ['if' => [['and' => [['==' => [['var' => 'isNewOclCustomer'], false]]]],
                        ['or' => [
                            ['<' => [['var' => 'age'], 21]],
                            ['>' => [['var' => 'age'], 65]],
                            ['<' => [['var' => 'fico'], 501]],
                            ['if' => [['missing' => ['fico']], true, false]],
                        ]],
                        false,
                    ],
                    ]
                ),
                'amount' => '0',
                'duration' => 18,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_greater_or_less_0_0.2_age_21_65_newCustomer',
                'formula' => json_encode(
                    ['if' => [['and' => [['==' => [['var' => 'isNewOclCustomer'], true]]]],
                        ['or' => [
                            ['<' => [['var' => 'age'], 21]],
                            ['>' => [['var' => 'age'], 65]],
                            ['>' => [['var' => 'drScore'], 0.2]],
                            ['<' => [['var' => 'drScore'], 0]],
                        ]],
                        false,
                    ],
                    ]
                ),
                'amount' => 0,
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            ['name' => 'fico',
                'label' => 'N_A_fico',
                'formula' => json_encode(['and' => [
                    ['==' => [['var' => 'fico'], 99999]],
                    ['==' => [['var' => 'isNewOclCustomer'], false]],
                ]]),
                'amount' => '0',
                'duration' => 0,
                'rate' => 0,
                'service_fee_rate' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
        ];

        $this->seed($ocl_rules, constants('LOAN_TYPES.OCL'));
    }
}
