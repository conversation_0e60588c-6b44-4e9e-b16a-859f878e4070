<?php

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LoanTypesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('loan_types')->insert([
            [
                'id' => 1,
                'name' => 'OCL',
                'description' => 'Online citizen loan',
                'disabled' => false,
                'is_line' => false,
            ],
            [
                'id' => 2,
                'name' => 'OVL',
                'description' => 'Online vehicle loan',
                'disabled' => false,
                'is_line' => false,
            ],
            [
                'id' => 3,
                'name' => 'COMMON',
                'description' => 'No loan type',
                'disabled' => false,
                'is_line' => false,
            ],
            [
                'id' => 4,
                'name' => 'OIQL',
                'description' => 'Online IQOS loan',
                'disabled' => false,
                'is_line' => false,
            ],
            [
                'id' => 5,
                'name' => 'OASL',
                'description' => 'Online Arpi Solar loan',
                'disabled' => false,
                'is_line' => false,
            ],
            [
                'id' => 6,
                'name' => 'OIDL',
                'description' => 'Online Idram loan',
                'disabled' => false,
                'is_line' => false,
            ],
            [
                'id' => 7,
                'name' => 'OTCL',
                'description' => 'Online TelCell loan',
                'disabled' => false,
                'is_line' => false,
            ],
            [
                'id' => 8,
                'name' => 'PL',
                'description' => 'Pay Later',
                'disabled' => false,
                'is_line' => true,
            ],
            [
                'id' => 9,
                'name' => 'OREL',
                'description' => 'Online Real Estate loan',
                'is_line' => false,
                'disabled' => true,
            ],
            [
                'id' => 10,
                'name' => 'OEPL',
                'description' => 'Online EasyPay loan',
                'disabled' => false,
                'is_line' => false,
            ],
            [
                'id' => 11,
                'name' => 'VLX',
                'description' => 'Velox',
                'disabled' => false,
                'is_line' => false,
            ],
            [
                'id' => 12,
                'name' => 'REML',
                'description' => 'Real Estate Mortgage Loan',
                'disabled' => false,
                'is_line' => false,
            ],
            [
                'id' => 13,
                'name' => 'BNPL',
                'description' => 'Buy Now Pay Later',
                'disabled' => false,
                'is_line' => true,
            ],
            [
                'id' => 14,
                'name' => 'OIWL',
                'description' => 'Internal wallet loan',
                'disabled' => false,
                'is_line' => false,
            ],
            [
                'id' => 15,
                'name' => 'OUPL',
                'description' => 'Online Upay loan',
                'disabled' => false,
                'is_line' => false,
            ],
            [
                'id' => 16,
                'name' => 'OBL',
                'description' => 'Online Business Loan',
                'disabled' => false,
                'is_line' => false,
            ],
            [
                'id' => 17,
                'name' => 'OFSL',
                'description' => 'Online Fastshift Loan',
                'disabled' => false,
                'is_line' => false,
            ],
        ]);

        DB::table('loan_subtypes')->insert([
            [
                'id' => 1,
                'name' => 'PREML',
                'description' => 'Primary Real Estate Mortgage Loan',
                'disabled' => false,
                'loan_type_id' => constants('LOAN_TYPES.REML'),
            ],
            [
                'id' => 2,
                'name' => 'SREML',
                'description' => 'Secondary Real Estate Mortgage Loan',
                'disabled' => false,
                'loan_type_id' => constants('LOAN_TYPES.REML'),
            ],
            [
                'id' => 4,
                'name' => 'OVIL',
                'description' => 'Online Vehicle Import Loan',
                'disabled' => false,
                'loan_type_id' => constants('LOAN_TYPES.OVL'),
            ],
            [
                'id' => 5,
                'name' => 'OVPL',
                'description' => 'Online Vehicle Pledge Loan',
                'disabled' => false,
                'loan_type_id' => constants('LOAN_TYPES.OVL'),
            ],
            [
                'id' => 6,
                'name' => 'OVTL',
                'description' => 'Online Vehicle Trade Loan',
                'disabled' => false,
                'loan_type_id' => constants('LOAN_TYPES.OVL'),
            ],
        ]);
    }
}
