<?php

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LoanConfigsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('loan_configs')->insert([
            // Blank loan configs
            [
                'loan_type_id' => 1,
                'key' => 'min_amount',
                'value' => 160000,
            ],
            [
                'loan_type_id' => 1,
                'key' => 'max_amount',
                'value' => 900000,
            ],
            [
                'loan_type_id' => 1,
                'key' => 'amount_step',
                'value' => 10000,
            ],
            [
                'loan_type_id' => 1,
                'key' => 'monthly_payment_step',
                'value' => 1000,
            ],
            [
                'loan_type_id' => 1,
                'key' => 'min_months',
                'value' => 18,
            ],
            [
                'loan_type_id' => 1,
                'key' => 'max_months',
                'value' => 18,
            ],
            [
                'loan_type_id' => 1,
                'key' => 'annual_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 1,
                'key' => 'penalty_base_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 1,
                'key' => 'penalty_percentage_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 1,
                'key' => 'night_start',
                'value' => '20:00:00',
            ],
            [
                'loan_type_id' => 1,
                'key' => 'night_end',
                'value' => '08:00:00',
            ],
            [
                'loan_type_id' => 1,
                'key' => 'fallback_transfer',
                'value' => 'cash_payment',
            ],
            [
                'loan_type_id' => 1,
                'key' => 'interest_rate',
                'value' => 100,
            ],
            [
                'loan_type_id' => 1,
                'key' => 'withdrawal_fee_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 1,
                'key' => 'service_fee_rate',
                'value' => 24,
            ],
            [
                'loan_type_id' => 1,
                'key' => 'confirmation_flow',
                'value' => 'supervised',
            ],
            [
                'loan_type_id' => 1,
                'key' => 'autoconfirmation_flow',
                'value' => 'autoconfirm',
            ],
            [
                'loan_type_id' => 1,
                'key' => 'primary_bank',
                'value' => 'Evocabank',
            ],
            [
                'loan_type_id' => 1,
                'key' => 'secondary_bank',
                'value' => 'Armeconombank',
            ],
            [
                'loan_type_id' => 1,
                'key' => 'withdraw_exp_days',
                'value' => 7,
            ],
            [
                'loan_type_id' => 1,
                'key' => 'top_up_withdraw_exp_days',
                'value' => 3,
            ],
            [
                'loan_type_id' => 1,
                'key' => 'top_up_min_amount',
                'value' => 100000,
            ],

            // Vehicle loan configs
            [
                'loan_type_id' => 2,
                'key' => 'min_amount',
                'value' => 500000,
            ],
            [
                'loan_type_id' => 2,
                'key' => 'max_amount',
                'value' => 5000000,
            ],
            [
                'loan_type_id' => 2,
                'key' => 'max_amount_group_A',
                'value' => 5000000,
            ],
            [
                'loan_type_id' => 2,
                'key' => 'max_amount_group_B',
                'value' => ********,
            ],
            [
                'loan_type_id' => 2,
                'key' => 'amount_step',
                'value' => 100000,
            ],
            [
                'loan_type_id' => 2,
                'key' => 'monthly_payment_step',
                'value' => 1000,
            ],
            [
                'loan_type_id' => 2,
                'key' => 'min_months',
                'value' => 12,
            ],
            [
                'loan_type_id' => 2,
                'key' => 'max_months',
                'value' => 120,
            ],
            [
                'loan_type_id' => 2,
                'key' => 'annual_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 2,
                'key' => 'penalty_base_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 2,
                'key' => 'penalty_percentage_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 2,
                'key' => 'night_start',
                'value' => '20:00:00',
            ],
            [
                'loan_type_id' => 2,
                'key' => 'night_end',
                'value' => '08:00:00',
            ],
            [
                'loan_type_id' => 2,
                'key' => 'fallback_transfer',
                'value' => 'cash_payment',
            ],
            [
                'loan_type_id' => 2,
                'key' => 'pledge_flow',
                'value' => 'direct',
            ],
            [
                'loan_type_id' => 2,
                'key' => 'interest_rate',
                'value' => 100,
            ],
            [
                'loan_type_id' => 2,
                'key' => 'withdrawal_fee_rate',
                'value' => 0.03,
            ],
            [
                'loan_type_id' => 2,
                'key' => 'service_fee_rate',
                'value' => 12,
            ],
            [
                'loan_type_id' => 2,
                'key' => 'confirmation_flow',
                'value' => 'supervised',
            ],
            [
                'loan_type_id' => 2,
                'key' => 'autoconfirmation_flow',
                'value' => 'autoconfirm',
            ],
            [
                'loan_type_id' => 2,
                'key' => 'primary_bank',
                'value' => 'Evocabank',
            ],
            [
                'loan_type_id' => 2,
                'key' => 'secondary_bank',
                'value' => 'Armeconombank',
            ],
            [
                'loan_type_id' => 2,
                'key' => 'withdraw_exp_days',
                'value' => 14,
            ],
            [
                'loan_type_id' => 2,
                'key' => 'vehicle_max_price',
                'value' => ********,
            ],
            [
                'loan_type_id' => 2,
                'key' => 'vehicle_max_price_group_A',
                'value' => ********,
            ],
            [
                'loan_type_id' => 2,
                'key' => 'vehicle_max_price_group_B',
                'value' => ********,
            ],
            // General loan configs
            [
                'loan_type_id' => 3,
                'key' => 'min_amount',
                'value' => 50000,
            ],
            [
                'loan_type_id' => 3,
                'key' => 'max_amount',
                'value' => 5000000,
            ],
            [
                'loan_type_id' => 3,
                'key' => 'amount_step',
                'value' => 10000,
            ],
            [
                'loan_type_id' => 3,
                'key' => 'monthly_payment_step',
                'value' => 1000,
            ],
            [
                'loan_type_id' => 3,
                'key' => 'min_months',
                'value' => 1,
            ],
            [
                'loan_type_id' => 3,
                'key' => 'max_months',
                'value' => 18,
            ],
            [
                'loan_type_id' => 3,
                'key' => 'toggle_amount',
                'value' => 1000000,
            ],
            [
                'loan_type_id' => 3,
                'key' => 'interest_rate',
                'value' => 100,
            ],
            [
                'loan_type_id' => 3,
                'key' => 'service_fee_rate',
                'value' => 12,
            ],
            [
                'loan_type_id' => 3,
                'key' => 'confirmation_flow',
                'value' => 'supervised',
            ],
            [
                'loan_type_id' => 3,
                'key' => 'autoconfirmation_flow',
                'value' => 'autoconfirm',
            ],

            // IQOS loan configs
            [
                'loan_type_id' => 4,
                'key' => 'min_months',
                'value' => 12,
            ],
            [
                'loan_type_id' => 4,
                'key' => 'max_months',
                'value' => 12,
            ],
            [
                'loan_type_id' => 4,
                'key' => 'annual_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 4,
                'key' => 'penalty_base_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 4,
                'key' => 'penalty_percentage_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 4,
                'key' => 'night_start',
                'value' => '00:00:00',
            ],
            [
                'loan_type_id' => 4,
                'key' => 'night_end',
                'value' => '00:00:00',
            ],
            [
                'loan_type_id' => 4,
                'key' => 'fallback_transfer',
                'value' => 'upay_payment',
            ],
            [
                'loan_type_id' => 4,
                'key' => 'interest_rate',
                'value' => 70,
            ],
            [
                'loan_type_id' => 4,
                'key' => 'withdrawal_fee_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 4,
                'key' => 'min_amount',
                'value' => 0,
            ],
            [
                'loan_type_id' => 4,
                'key' => 'max_amount',
                'value' => 0,
            ],
            [
                'loan_type_id' => 4,
                'key' => 'service_fee_rate',
                'value' => 24,
            ],
            [
                'loan_type_id' => 4,
                'key' => 'confirmation_flow',
                'value' => 'direct',
            ],
            [
                'loan_type_id' => 4,
                'key' => 'autoconfirmation_flow',
                'value' => 'autoconfirm',
            ],

            // Arpi solar loan configs
            [
                'loan_type_id' => 5,
                'key' => 'min_months',
                'value' => 120,
            ],
            [
                'loan_type_id' => 5,
                'key' => 'max_months',
                'value' => 120,
            ],
            [
                'loan_type_id' => 5,
                'key' => 'annual_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 5,
                'key' => 'penalty_base_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 5,
                'key' => 'penalty_percentage_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 5,
                'key' => 'night_start',
                'value' => '00:00:00',
            ],
            [
                'loan_type_id' => 5,
                'key' => 'night_end',
                'value' => '00:00:00',
            ],
            [
                'loan_type_id' => 5,
                'key' => 'fallback_transfer',
                'value' => 'upay_payment',
            ],
            [
                'loan_type_id' => 5,
                'key' => 'interest_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 5,
                'key' => 'withdrawal_fee_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 5,
                'key' => 'min_amount',
                'value' => 0,
            ],
            [
                'loan_type_id' => 5,
                'key' => 'max_amount',
                'value' => 0,
            ],
            [
                'loan_type_id' => 5,
                'key' => 'service_fee_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 5,
                'key' => 'confirmation_flow',
                'value' => 'direct',
            ],
            [
                'loan_type_id' => 5,
                'key' => 'autoconfirmation_flow',
                'value' => 'autoconfirm',
            ],
            [
                'loan_type_id' => 5,
                'key' => 'withdraw_exp_days',
                'value' => 15,
            ],

            // Idram loan configs
            [
                'loan_type_id' => 6,
                'key' => 'min_months',
                'value' => 1,
            ],
            [
                'loan_type_id' => 6,
                'key' => 'max_months',
                'value' => 12,
            ],
            [
                'loan_type_id' => 6,
                'key' => 'annual_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 6,
                'key' => 'penalty_base_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 6,
                'key' => 'penalty_percentage_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 6,
                'key' => 'night_start',
                'value' => '00:00:00',
            ],
            [
                'loan_type_id' => 6,
                'key' => 'night_end',
                'value' => '00:00:00',
            ],
            [
                'loan_type_id' => 6,
                'key' => 'fallback_transfer',
                'value' => 'upay_payment',
            ],
            [
                'loan_type_id' => 6,
                'key' => 'interest_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 6,
                'key' => 'withdrawal_fee_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 6,
                'key' => 'withdrawal_fee_20000',
                'value' => 200,
            ],
            [
                'loan_type_id' => 6,
                'key' => 'withdrawal_fee_40000',
                'value' => 300,
            ],
            [
                'loan_type_id' => 6,
                'key' => 'withdrawal_fee_80000',
                'value' => 400,
            ],
            [
                'loan_type_id' => 6,
                'key' => 'withdrawal_fee_100000',
                'value' => 500,
            ],
            [
                'loan_type_id' => 6,
                'key' => 'withdrawal_fee_150000',
                'value' => 750,
            ],
            [
                'loan_type_id' => 6,
                'key' => 'min_amount',
                'value' => 20000,
            ],
            [
                'loan_type_id' => 6,
                'key' => 'max_amount',
                'value' => 150000,
            ],
            [
                'loan_type_id' => 6,
                'key' => 'service_fee_rate',
                'value' => '0.5',
            ],
            [
                'loan_type_id' => 6,
                'key' => 'confirmation_flow',
                'value' => 'direct',
            ],
            [
                'loan_type_id' => 6,
                'key' => 'autoconfirmation_flow',
                'value' => 'autoconfirm',
            ],

            // Telcell loan configs
            [
                'loan_type_id' => 7,
                'key' => 'min_months',
                'value' => 1,
            ],
            [
                'loan_type_id' => 7,
                'key' => 'max_months',
                'value' => 12,
            ],
            [
                'loan_type_id' => 7,
                'key' => 'annual_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 7,
                'key' => 'penalty_base_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 7,
                'key' => 'penalty_percentage_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 7,
                'key' => 'night_start',
                'value' => '00:00:00',
            ],
            [
                'loan_type_id' => 7,
                'key' => 'night_end',
                'value' => '00:00:00',
            ],
            [
                'loan_type_id' => 7,
                'key' => 'fallback_transfer',
                'value' => 'upay_payment',
            ],
            [
                'loan_type_id' => 7,
                'key' => 'interest_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 7,
                'key' => 'withdrawal_fee_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 7,
                'key' => 'withdrawal_fee_20000',
                'value' => 200,
            ],
            [
                'loan_type_id' => 7,
                'key' => 'withdrawal_fee_40000',
                'value' => 300,
            ],
            [
                'loan_type_id' => 7,
                'key' => 'withdrawal_fee_80000',
                'value' => 400,
            ],
            [
                'loan_type_id' => 7,
                'key' => 'withdrawal_fee_100000',
                'value' => 500,
            ],
            [
                'loan_type_id' => 7,
                'key' => 'withdrawal_fee_150000',
                'value' => 750,
            ],
            [
                'loan_type_id' => 7,
                'key' => 'min_amount',
                'value' => 20000,
            ],
            [
                'loan_type_id' => 7,
                'key' => 'max_amount',
                'value' => 150000,
            ],
            [
                'loan_type_id' => 7,
                'key' => 'service_fee_rate',
                'value' => '0.5',
            ],
            [
                'loan_type_id' => 7,
                'key' => 'confirmation_flow',
                'value' => 'direct',
            ],
            [
                'loan_type_id' => 7,
                'key' => 'autoconfirmation_flow',
                'value' => 'autoconfirm',
            ],

            // Pay later loan configs
            [
                'loan_type_id' => 8,
                'key' => 'penalty_base_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 8,
                'key' => 'penalty_percentage_amount',
                'value' => '0',
            ],
            [
                'loan_type_id' => 8,
                'key' => 'service_fee_rate',
                'value' => '0',
            ],
            [
                'loan_type_id' => 8,
                'key' => 'interest_rate',
                'value' => 0,
            ],

            // Easypay loan configs
            [
                'loan_type_id' => 10,
                'key' => 'min_months',
                'value' => 1,
            ],
            [
                'loan_type_id' => 10,
                'key' => 'max_months',
                'value' => 12,
            ],
            [
                'loan_type_id' => 10,
                'key' => 'annual_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 10,
                'key' => 'penalty_base_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 10,
                'key' => 'penalty_percentage_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 10,
                'key' => 'night_start',
                'value' => '00:00:00',
            ],
            [
                'loan_type_id' => 10,
                'key' => 'night_end',
                'value' => '00:00:00',
            ],
            [
                'loan_type_id' => 10,
                'key' => 'fallback_transfer',
                'value' => 'upay_payment',
            ],
            [
                'loan_type_id' => 10,
                'key' => 'interest_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 10,
                'key' => 'withdrawal_fee_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 10,
                'key' => 'withdrawal_fee_20000',
                'value' => 200,
            ],
            [
                'loan_type_id' => 10,
                'key' => 'withdrawal_fee_40000',
                'value' => 300,
            ],
            [
                'loan_type_id' => 10,
                'key' => 'withdrawal_fee_80000',
                'value' => 400,
            ],
            [
                'loan_type_id' => 10,
                'key' => 'withdrawal_fee_100000',
                'value' => 500,
            ],
            [
                'loan_type_id' => 10,
                'key' => 'withdrawal_fee_150000',
                'value' => 750,
            ],
            [
                'loan_type_id' => 10,
                'key' => 'min_amount',
                'value' => 20000,
            ],
            [
                'loan_type_id' => 10,
                'key' => 'max_amount',
                'value' => 150000,
            ],
            [
                'loan_type_id' => 10,
                'key' => 'service_fee_rate',
                'value' => '0.5',
            ],
            [
                'loan_type_id' => 10,
                'key' => 'confirmation_flow',
                'value' => 'direct',
            ],
            [
                'loan_type_id' => 10,
                'key' => 'autoconfirmation_flow',
                'value' => 'autoconfirm',
            ],

            // REML loan configs
            [
                'loan_type_id' => 12,
                'key' => 'min_months',
                'value' => 120,
            ],
            [
                'loan_type_id' => 12,
                'key' => 'max_months',
                'value' => 240,
            ],
            [
                'loan_type_id' => 12,
                'key' => 'annual_rate',
                'value' => 12,
            ],
            [
                'loan_type_id' => 12,
                'key' => 'penalty_base_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 12,
                'key' => 'penalty_percentage_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 12,
                'key' => 'night_start',
                'value' => '00:00:00',
            ],
            [
                'loan_type_id' => 12,
                'key' => 'night_end',
                'value' => '00:00:00',
            ],
            [
                'loan_type_id' => 12,
                'key' => 'fallback_transfer',
                'value' => 'upay_payment',
            ],
            [
                'loan_type_id' => 12,
                'key' => 'interest_rate',
                'value' => 12,
            ],
            // NOTE: In the future we may have a different 'withdrawal_fee_rate' and 'withdrawal_fee_min_amount' for
            // each reml loan subtype, at that time think a logic to separate them.
            [
                'loan_type_id' => 12,
                'key' => 'withdrawal_fee_rate',
                'value' => 0.007,
            ],
            [
                'loan_type_id' => 12,
                'key' => 'withdrawal_fee_min_amount',
                'value' => 30000,
            ],
            [
                'loan_type_id' => 12,
                'key' => 'min_amount',
                'value' => 1000000,
            ],
            [
                'loan_type_id' => 12,
                'key' => 'max_amount',
                'value' => 50000000,
            ],
            [
                'loan_type_id' => 12,
                'key' => 'service_fee_rate',
                'value' => '0.3',
            ],
            [
                'loan_type_id' => 12,
                'key' => 'confirmation_flow',
                'value' => 'direct',
            ],
            [
                'loan_type_id' => 12,
                'key' => 'autoconfirmation_flow',
                'value' => 'autoconfirm',
            ],
            [
                'loan_type_id' => 12,
                'key' => 'time_step',
                'value' => 60,
            ],
            [
                'loan_type_id' => 12,
                'key' => 'initial_time',
                'value' => 180,
            ],
            [
                'loan_type_id' => 12,
                'key' => 'min_prepayment_percentage',
                'value' => 10,
            ],
            [
                'loan_type_id' => 12,
                'key' => 'max_prepayment_percentage',
                'value' => 90,
            ],

            // Velox loan configs
            [
                'loan_type_id' => 11,
                'key' => 'min_amount',
                'value' => 50000,
            ],
            [
                'loan_type_id' => 11,
                'key' => 'max_amount',
                'value' => 1400000,
            ],
            [
                'loan_type_id' => 11,
                'key' => 'amount_step',
                'value' => 10000,
            ],
            [
                'loan_type_id' => 11,
                'key' => 'monthly_payment_step',
                'value' => 1000,
            ],
            [
                'loan_type_id' => 11,
                'key' => 'min_months',
                'value' => 1,
            ],
            [
                'loan_type_id' => 11,
                'key' => 'max_months',
                'value' => 36,
            ],
            [
                'loan_type_id' => 11,
                'key' => 'annual_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 11,
                'key' => 'penalty_base_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 11,
                'key' => 'penalty_percentage_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 11,
                'key' => 'night_start',
                'value' => '20:00:00',
            ],
            [
                'loan_type_id' => 11,
                'key' => 'night_end',
                'value' => '08:00:00',
            ],
            [
                'loan_type_id' => 11,
                'key' => 'fallback_transfer',
                'value' => 'cash_payment',
            ],
            [
                'loan_type_id' => 11,
                'key' => 'interest_rate',
                'value' => 100,
            ],
            [
                'loan_type_id' => 11,
                'key' => 'withdrawal_fee_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 11,
                'key' => 'service_fee_rate',
                'value' => 24,
            ],
            [
                'loan_type_id' => 11,
                'key' => 'confirmation_flow',
                'value' => 'supervised',
            ],
            [
                'loan_type_id' => 11,
                'key' => 'autoconfirmation_flow',
                'value' => 'autoconfirm',
            ],

            // BNPL loan configs
            [
                'loan_type_id' => 13,
                'key' => 'amount_step',
                'value' => 10000,
            ],
            [
                'loan_type_id' => 13,
                'key' => 'monthly_payment_step',
                'value' => 1000,
            ],
            [
                'loan_type_id' => 13,
                'key' => 'min_months',
                'value' => 3,
            ],
            [
                'loan_type_id' => 13,
                'key' => 'max_months',
                'value' => 3,
            ],
            [
                'loan_type_id' => 13,
                'key' => 'penalty_base_amount',
                'value' => '0',
            ],
            [
                'loan_type_id' => 13,
                'key' => 'penalty_percentage_amount',
                'value' => '0',
            ],
            [
                'loan_type_id' => 13,
                'key' => 'service_fee_rate',
                'value' => '0',
            ],
            [
                'loan_type_id' => 13,
                'key' => 'interest_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 13,
                'key' => 'duration',
                'value' => 3,
            ],
            [
                'loan_type_id' => 13,
                'key' => 'max_amount',
                'value' => 2000000,
            ],
            [
                'loan_type_id' => 13,
                'key' => 'min_amount',
                'value' => 72000,
            ],
            [
                'loan_type_id' => 13,
                'key' => 'amortized_service_fee_rate',
                'value' => '24',
            ],
            [
                'loan_type_id' => 13,
                'key' => 'amortized_interest_rate',
                'value' => 40,
            ],
            [
                'loan_type_id' => 13,
                'key' => 'amortized_duration',
                'value' => 18,
            ],
            [
                'loan_type_id' => 13,
                'key' => 'amortized_penalty_base_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 13,
                'key' => 'amortized_penalty_percentage_amount',
                'value' => '0.13',
            ],

            // OIWL configs
            [
                'loan_type_id' => 14,
                'key' => 'min_months',
                'value' => 1,
            ],
            [
                'loan_type_id' => 14,
                'key' => 'max_months',
                'value' => 12,
            ],
            [
                'loan_type_id' => 14,
                'key' => 'annual_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 14,
                'key' => 'penalty_base_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 14,
                'key' => 'penalty_percentage_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 14,
                'key' => 'night_start',
                'value' => '00:00:00',
            ],
            [
                'loan_type_id' => 14,
                'key' => 'night_end',
                'value' => '00:00:00',
            ],
            [
                'loan_type_id' => 14,
                'key' => 'fallback_transfer',
                'value' => 'upay_payment',
            ],
            [
                'loan_type_id' => 14,
                'key' => 'interest_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 14,
                'key' => 'withdrawal_fee_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 14,
                'key' => 'withdrawal_fee_20000',
                'value' => 200,
            ],
            [
                'loan_type_id' => 14,
                'key' => 'withdrawal_fee_40000',
                'value' => 300,
            ],
            [
                'loan_type_id' => 14,
                'key' => 'withdrawal_fee_80000',
                'value' => 400,
            ],
            [
                'loan_type_id' => 14,
                'key' => 'withdrawal_fee_100000',
                'value' => 500,
            ],
            [
                'loan_type_id' => 14,
                'key' => 'withdrawal_fee_150000',
                'value' => 750,
            ],
            [
                'loan_type_id' => 14,
                'key' => 'min_amount',
                'value' => 20000,
            ],
            [
                'loan_type_id' => 14,
                'key' => 'max_amount',
                'value' => 150000,
            ],
            [
                'loan_type_id' => 14,
                'key' => 'service_fee_rate',
                'value' => '0.5',
            ],
            [
                'loan_type_id' => 14,
                'key' => 'fallback_transfer',
                'value' => 'cash_payment',
            ],
            [
                'loan_type_id' => 14,
                'key' => 'confirmation_flow',
                'value' => 'supervised',
            ],
            [
                'loan_type_id' => 14,
                'key' => 'autoconfirmation_flow',
                'value' => 'autoconfirm',
            ],
            [
                'loan_type_id' => 14,
                'key' => 'primary_bank',
                'value' => 'Evocabank',
            ],
            [
                'loan_type_id' => 14,
                'key' => 'secondary_bank',
                'value' => 'Armeconombank',
            ],
            [
                'loan_type_id' => 14,
                'key' => 'withdraw_exp_days',
                'value' => 7,
            ],

            // Upay loan configs
            [
                'loan_type_id' => 15,
                'key' => 'min_months',
                'value' => 1,
            ],
            [
                'loan_type_id' => 15,
                'key' => 'max_months',
                'value' => 12,
            ],
            [
                'loan_type_id' => 15,
                'key' => 'annual_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 15,
                'key' => 'penalty_base_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 15,
                'key' => 'penalty_percentage_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 15,
                'key' => 'night_start',
                'value' => '00:00:00',
            ],
            [
                'loan_type_id' => 15,
                'key' => 'night_end',
                'value' => '00:00:00',
            ],
            [
                'loan_type_id' => 15,
                'key' => 'fallback_transfer',
                'value' => 'upay_payment',
            ],
            [
                'loan_type_id' => 15,
                'key' => 'interest_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 15,
                'key' => 'withdrawal_fee_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 15,
                'key' => 'withdrawal_fee_20000',
                'value' => 200,
            ],
            [
                'loan_type_id' => 15,
                'key' => 'withdrawal_fee_40000',
                'value' => 300,
            ],
            [
                'loan_type_id' => 15,
                'key' => 'withdrawal_fee_80000',
                'value' => 400,
            ],
            [
                'loan_type_id' => 15,
                'key' => 'withdrawal_fee_100000',
                'value' => 500,
            ],
            [
                'loan_type_id' => 15,
                'key' => 'withdrawal_fee_150000',
                'value' => 750,
            ],
            [
                'loan_type_id' => 15,
                'key' => 'min_amount',
                'value' => 20000,
            ],
            [
                'loan_type_id' => 15,
                'key' => 'max_amount',
                'value' => 150000,
            ],
            [
                'loan_type_id' => 15,
                'key' => 'service_fee_rate',
                'value' => '0.5',
            ],
            [
                'loan_type_id' => 15,
                'key' => 'confirmation_flow',
                'value' => 'direct',
            ],
            [
                'loan_type_id' => 15,
                'key' => 'autoconfirmation_flow',
                'value' => 'autoconfirm',
            ],

            // OBL loan configs
            [
                'loan_type_id' => 16,
                'key' => 'service_fee_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 16,
                'key' => 'interest_rate',
                'value' => 24,
            ],
            [
                'loan_type_id' => 16,
                'key' => 'transaction_fee',
                'value' => 100,
            ],
            [
                'loan_type_id' => 16,
                'key' => 'transaction_fee_100000',
                'value' => 100,
            ],
            [
                'loan_type_id' => 16,
                'key' => 'transaction_fee_500000',
                'value' => 500,
            ],
            [
                'loan_type_id' => 16,
                'key' => 'duration',
                'value' => 3,
            ],
            [
                'loan_type_id' => 16,
                'key' => 'max_amount',
                'value' => 5000000,
            ],
            [
                'loan_type_id' => 16,
                'key' => 'min_amount',
                'value' => 100000,
            ],
            [
                'loan_type_id' => 16,
                'key' => 'penalty_base_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 16,
                'key' => 'penalty_percentage_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 16,
                'key' => 'link',
                'value' => 'https://www.globalcredit.am/loans/pay4me-business',
            ],

            // Fastshift loan configs
            [
                'loan_type_id' => 17,
                'key' => 'min_months',
                'value' => 1,
            ],
            [
                'loan_type_id' => 17,
                'key' => 'max_months',
                'value' => 12,
            ],
            [
                'loan_type_id' => 17,
                'key' => 'annual_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 17,
                'key' => 'penalty_base_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 17,
                'key' => 'penalty_percentage_amount',
                'value' => '0.13',
            ],
            [
                'loan_type_id' => 17,
                'key' => 'night_start',
                'value' => '00:00:00',
            ],
            [
                'loan_type_id' => 17,
                'key' => 'night_end',
                'value' => '00:00:00',
            ],
            [
                'loan_type_id' => 15,
                'key' => 'fallback_transfer',
                'value' => 'upay_payment',
            ],
            [
                'loan_type_id' => 17,
                'key' => 'interest_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 17,
                'key' => 'withdrawal_fee_rate',
                'value' => 0,
            ],
            [
                'loan_type_id' => 17,
                'key' => 'withdrawal_fee_20000',
                'value' => 200,
            ],
            [
                'loan_type_id' => 17,
                'key' => 'withdrawal_fee_40000',
                'value' => 300,
            ],
            [
                'loan_type_id' => 17,
                'key' => 'withdrawal_fee_80000',
                'value' => 400,
            ],
            [
                'loan_type_id' => 17,
                'key' => 'withdrawal_fee_100000',
                'value' => 500,
            ],
            [
                'loan_type_id' => 17,
                'key' => 'withdrawal_fee_150000',
                'value' => 750,
            ],
            [
                'loan_type_id' => 17,
                'key' => 'min_amount',
                'value' => 20000,
            ],
            [
                'loan_type_id' => 17,
                'key' => 'max_amount',
                'value' => 150000,
            ],
            [
                'loan_type_id' => 17,
                'key' => 'service_fee_rate',
                'value' => '0.5',
            ],
            [
                'loan_type_id' => 17,
                'key' => 'confirmation_flow',
                'value' => 'direct',
            ],
            [
                'loan_type_id' => 17,
                'key' => 'autoconfirmation_flow',
                'value' => 'autoconfirm',
            ],
        ]);
    }
}
