<?php

use App\Factory\DocumentServiceFactory;
use App\Jobs\SendLoanDocuments;
use App\Mail\LoanDocumentsMail;
use App\Models\HC\HcGCCRDTCODE;
use App\Models\Loan;
use App\Traits\Transaction;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class SendBnplDocumentsSeeder extends Seeder
{
    use Transaction;

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $loans = Loan::whereStatus(Loan::CONFIRMED)
            ->whereLoanTypeId(constants('LOAN_TYPES.BNPL'))
            ->with('transactions')
            ->get();

        foreach ($loans as $loan) {
            try {
                $closed = HcGCCRDTCODE::isCreditClosed($loan->contract_number);

                if (!$closed && is_null($loan->transactions->first())) {
                    Log::info('Sending BNPL documents', ['loan_id', $loan->id]);

                    $loan_document_service = DocumentServiceFactory::build($loan->loan_type_id);
                    $loan_document_service->exposePrivateDocuments($loan);

                    SendLoanDocuments::dispatch($loan, LoanDocumentsMail::class);

                    Log::info('Bnpl documents sent', ['loan_id', $loan->id]);
                }
            } catch (Throwable $e) {
                Log::error('Bnpl documents Recover Exception', ['error', $e->getMessage()]);
            }
        }
    }
}
