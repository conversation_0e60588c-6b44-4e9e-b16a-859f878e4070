<?php

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolesAndPermissionsTablesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Reset cached roles and permissions
        app()['cache']->forget('spatie.permission.cache');

        // Create permissions
        Permission::create(['name' => 'view-pdfs', 'description' => 'view pdf files']);
        Permission::create(['name' => 'view-cash', 'description' => 'view cashes/unpaid loans']);
        Permission::create(['name' => 'view-all-details', 'description' => 'view all details related Loans']);
        Permission::create(['name' => 'view-nova', 'description' => 'access nova in non-local environments']);
        Permission::create(['name' => 'view-withdraw-details', 'description' => 'access withdraw details']);
        Permission::create(['name' => 'view-loan-history', 'description' => 'view Loan Histories']);
        Permission::create(['name' => 'view-full-history', 'description' => 'use filters in the Loan resource']);
        Permission::create(['name' => 'view-ovl-loan', 'description' => 'allow to view OVL loans']);
        Permission::create(['name' => 'verify-ovl-loan', 'description' => 'allow to verify OVL loans']);
        Permission::create(['name' => 'confirm-ovl-loan', 'description' => 'allow to confirm OVL loans']);
        Permission::create(['name' => 'block-user', 'description' => 'view Black List resource']);
        Permission::create(['name' => 'withdraw', 'description' => 'allow work with withdraws']);
        Permission::create(['name' => 'view-loan-explorer',  'description' => 'view Loan Explorer resource']);
        Permission::create(['guard_name' => 'api', 'name' => 'view-loan-amount', 'description' => 'view loan amount during moderation']);
        Permission::create(['name' => 'view-oasl-loan', 'description' => 'view Arpi Solar resource']);
        Permission::create(['name' => 'edit-oasl-address', 'description' => 'allow to edit Arpi Solar records']);
        Permission::create(['name' => 'edit-oasl-address-before-confirming', 'description' => 'allow to edit Arpi Solar records before confirms Loan']);
        Permission::create(['name' => 'download-kfw-documents', 'description' => 'download kfw documents']);
        Permission::create(['name' => 'view-package', 'description' => 'view Arpi Solar Package resource']);
        Permission::create(['name' => 'create-package', 'description' => 'create Arpi Solar package']);
        Permission::create(['name' => 'change-package-state', 'description' => 'Change Arpi Solar package state']);
        Permission::create(['name' => 'change-expiration-date', 'description' => 'Change Arpi Solar expiration date']);
        Permission::create(['name' => 'download-oasl-documents', 'description' => 'download Arpi Solar documents']);
        Permission::create(['name' => 'regenerate-documents', 'description' => 'regenerate loan documents']);
        Permission::create(['name' => 'send-documents', 'description' => 'send loan documents']);
        Permission::create(['name' => 'bank-report', 'description' => 'view Bank Reporting resource']);
        Permission::create(['name' => 'withdraw-ovl', 'description' => 'allow work with OVL withdraws']);
        Permission::create(['name' => 'stabilization-loan', 'description' => 'view Stabilization Loan resource']);
        Permission::create(['name' => 'correct-devated-schedule', 'description' => 'view correct devated schedule tool']);
        Permission::create(['name' => 'view-credit-offers', 'description' => 'view Credit Offer resource']);
        Permission::create(['name' => 'view-loan-support', 'description' => 'view Loan Support resource']);
        Permission::create(['name' => 'view-qr-codes', 'description' => 'view QR code resource and related fields in the Loan resources']);
        Permission::create(['name' => 'create-purchase-request', 'description' => 'create purchase request']);
        Permission::create(['name' => 'view-purchase-request', 'description' => 'view purchase request']);
        Permission::create(['name' => 'view-referral-code', 'description' => 'view referral code']);
        Permission::create(['name' => 'download-loan-documents']);
        Permission::create(['name' => 'acra-monitoring', 'description' => 'Acra monitoring']);
        Permission::create(['name' => 'view-oasl-users', 'description' => 'allow to view Arpi Solar users']);
        Permission::create(['guard_name' => 'api', 'name' => 'access-mobile', 'description' => 'Access mobile application']);
        Permission::create(['name' => 'view-all-purchase-requests', 'description' => 'view all purchase requests']);
        Permission::create(['name' => 'create-vendor', 'description' => 'create vendor']);
        Permission::create(['name' => 'view-vendor', 'description' => 'view vendor']);
        Permission::create(['name' => 'view-reml', 'description' => 'View Real Estate Mortgage Loans']);
        Permission::create(['name' => 'view-reml-loan-applications', 'description' => 'View reml loan applications']);
        Permission::create(['name' => 'view-pipe-types', 'description' => 'view pipe types']);
        Permission::create(['name' => 'view-lock-user', 'description' => 'view lock user']);
        Permission::create(['name' => 'view-reml-details', 'description' => 'view reml details']);
        Permission::create(['name' => 'regenerate-withdraw-check', 'description' => 'Regenerate withdraw check']);
        Permission::create(['name' => 'remove-users', 'description' => 'Allow remove users']);
        Permission::create(['name' => 'view-loan-update-history', 'description' => 'View loan update history']);
        Permission::create(['name' => 'edit-app-media', 'description' => 'Edit app media']);
        Permission::create(['name' => 'view-merchant', 'description' => 'View merchant']);
        Permission::create(['name' => 'view-payment-invoice', 'description' => 'View payment invoices']);
        Permission::create(['name' => 'block-merchant', 'description' => 'view Merchant blacklist resource']);
        Permission::create(['name' => 'view-bnpl-transactions', 'description' => 'view BNPL Transactions']);
        Permission::create(['name' => 'view-loans', 'description' => 'view Loans']);

        // Create roles and assign created permissions
        $role = Role::create(['name' => 'admin']);
        $role->givePermissionTo(Permission::where('guard_name', 'web')->get());

        $role = Role::create(['name' => 'upay-admin']);
        $role->givePermissionTo(['view-pdfs', 'view-nova']);

        $role = Role::create(['name' => 'telcell-admin']);
        $role->givePermissionTo(['view-pdfs', 'view-nova', 'view-withdraw-details']);

        $role = Role::create(['name' => 'telcell-sub-admin']);
        $role->givePermissionTo(['view-pdfs', 'view-nova', 'view-withdraw-details']);

        $role = Role::create(['name' => 'easypay-admin']);
        $role->givePermissionTo(['view-pdfs', 'view-cash', 'view-nova', 'view-loan-history']);

        $role = Role::create(['name' => 'gc-admin']);
        $role->givePermissionTo(['view-pdfs', 'view-cash', 'view-nova', 'view-withdraw-details', 'view-full-history', 'view-loan-history', 'bank-report', 'view-loan-support']);

        $role = Role::create(['name' => 'gc-cashier']);
        $role->givePermissionTo(['view-pdfs', 'view-all-details', 'view-cash', 'view-nova', 'withdraw', 'view-loan-history', 'withdraw-ovl']);

        $role = Role::create(['name' => 'easypay-cashier']);
        $role->givePermissionTo(['view-pdfs', 'view-cash', 'view-nova', 'withdraw', 'view-loan-history']);

        $role = Role::create(['name' => 'upay-cashier']);
        $role->givePermissionTo(['view-pdfs', 'view-cash', 'view-nova', 'withdraw', 'view-loan-history']);

        $role = Role::create(['name' => 'telcell-cashier']);
        $role->givePermissionTo(['view-pdfs', 'view-cash', 'view-nova', 'withdraw', 'view-loan-history']);

        $role = Role::create(['name' => 'ovl-viewer']);
        $role->givePermissionTo(['view-nova', 'view-ovl-loan', 'view-pdfs']);

        $role = Role::create(['name' => 'globalcredit-agent']);
        $role->givePermissionTo(['view-nova', 'view-ovl-loan', 'verify-ovl-loan', 'view-pdfs']);

        $role = Role::create(['name' => 'globalcredit-agent-admin']);
        $role->givePermissionTo(['view-nova', 'view-ovl-loan', 'verify-ovl-loan', 'confirm-ovl-loan', 'view-pdfs', 'block-user']);

        $role = Role::create(['guard_name' => 'api', 'name' => 'iqos-seller']);

        $role = Role::create(['guard_name' => 'api', 'name' => 'moderator']);

        $role = Role::create(['guard_name' => 'api', 'name' => 'pl-user']);

        $role = Role::create(['guard_name' => 'web', 'name' => 'system']);

        $role = Role::create(['guard_name' => 'api', 'name' => 'oasl-agent']);

        $role = Role::create(['guard_name' => 'api', 'name' => 'merchant-agent']);

        $role = Role::create(['guard_name' => 'api', 'name' => 'merchant-agent-ovil']);

        $role = Role::create(['guard_name' => 'api', 'name' => 'mobile-user']);
        $role->givePermissionTo(['access-mobile']);

        $role = Role::create(['name' => 'oasl-admin']);
        $role->givePermissionTo(['view-nova', 'view-oasl-loan', 'view-pdfs', 'download-oasl-documents', 'edit-oasl-address-before-confirming']);

        $role = Role::create(['name' => 'oasl-sub-admin']);
        $role->givePermissionTo(['view-oasl-loan', 'view-pdfs', 'download-oasl-documents']);

        $role = Role::create(['name' => 'oasl-gc-admin']);
        $role->givePermissionTo(['view-nova', 'view-oasl-loan', 'view-pdfs', 'view-package', 'edit-oasl-address', 'edit-oasl-address-before-confirming', 'create-package', 'download-oasl-documents', 'regenerate-documents', 'download-kfw-documents', 'change-package-state', 'change-expiration-date']);

        $role = Role::create(['name' => 'oasl-nmc-agent']);
        $role->givePermissionTo(['view-nova', 'view-oasl-loan', 'view-pdfs', 'view-package', 'download-oasl-documents']);

        $role = Role::create(['guard_name' => 'web', 'name' => 'loan-support-admin']);
        $role->givePermissionTo(['view-nova', 'correct-devated-schedule', 'stabilization-loan']);

        $role = Role::create(['name' => 'gc-sub-admin']);
        $role->givePermissionTo(['view-nova', 'view-loan-history', 'bank-report', 'view-cash', 'view-full-history', 'view-pdfs']);

        $role = Role::create(['name' => 'pl-agent']);
        $role->givePermissionTo(['create-purchase-request', 'view-purchase-request', 'view-nova']);

        $role = Role::create(['name' => 'pl-admin']);
        $role->givePermissionTo(['view-all-purchase-requests', 'view-nova']);

        $role = Role::create(['name' => 'acra-monitoring']);
        $role->givePermissionTo(['acra-monitoring']);

        $role = Role::create(['name' => 'ec-reml-admin']);
        $role->givePermissionTo(['view-nova', 'view-reml']);

        $role = Role::create(['name' => 'gc-reml-admin']);
        $role->givePermissionTo(['view-nova', 'view-reml', 'view-pdfs']);

        $role = Role::create(['name' => 'gc-reml-sub-admin']);
        $role->givePermissionTo(['view-nova', 'view-reml', 'view-pdfs', 'view-reml-loan-applications']);

        $role = Role::create(['name' => 'gc-reml-viewer']);
        $role->givePermissionTo(['view-reml-loan-applications', 'view-reml', 'view-reml-details', 'view-nova']);

        $role = Role::create(['name' => 'gc-customer-admin']);
    }
}
