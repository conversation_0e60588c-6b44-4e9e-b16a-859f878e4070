<?php

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $this->call([
            RolesAndPermissionsTablesSeeder::class,
            UsersTableSeeder::class,
            LoanConfigsTableSeeder::class,
            LoanTypesTableSeeder::class,
            \Database\Seeders\RulesTableOCLSeeder::class,
            \Database\Seeders\RulesTableOIWLSeeder::class,
            \Database\Seeders\RulesTableOVLSeeder::class,
            \Database\Seeders\RulesTableOASLSeeder::class,
            \Database\Seeders\RulesTableOIDLSeeder::class,
            \Database\Seeders\RulesTableOTCLSeeder::class,
            \Database\Seeders\RulesTableOEPLSeeder::class,
            \Database\Seeders\RulesTableOUPLSeeder::class,
            \Database\Seeders\RulesTableOIQLSeeder::class,
            \Database\Seeders\RulesTablePLSeeder::class,
            \Database\Seeders\RulesTableREMLSeeder::class,
            \Database\Seeders\RulesTableVLXSeeder::class,
            \Database\Seeders\RulesTableBNPLSeeder::class,
            \Database\Seeders\RulesTableOBLSeeder::class,
            \Database\Seeders\RulesTableOFSLSeeder::class,
            CashOfficesTableSeeder::class,
            TransferTypesTableSeeder::class,
            HcDefaultsTableSeeder::class,
            CashOfficeSchedulesTableSeeder::class,
            AgentSchedulesTableSeeder::class,
            VehicleModelsTableSeeder::class,
            CashOfficeTypesTableSeeder::class,
            PipeTypesTableSeeder::class,
            RegionsAndVillagesTableSeeder::class,
            ArpiSolarTermsTableSeeder::class,
            SolarPanelTypesTableSeeder::class,
            TagsTableSeeder::class,
            WorkplacesTableSeeder::class,
            VendorsTableSeeder::class,
            QrTypesTableSeeder::class,
            DataRobotModelsTableSeeder::class,
            PoliceCodesTableSeeder::class,
            ContractNumberIdsSeeder::class,
            RealEstateEvaluationCompaniesTableSeeder::class,
            CategoriesTableSeeder::class,
            DeveloperCompaniesTableSeeder::class,
            RenshinPredefinedRealEstatesSeeder::class,
            LuyserPredefinedRealEstatesSeeder::class,
            MerchantsTableSeeder::class,
            BlacklistReasonsTableSeeder::class,
            LoanTypeHcNoteTableSeeder::class,
        ]);
    }
}
