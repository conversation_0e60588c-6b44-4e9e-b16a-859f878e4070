<?php

use App\Models\CashOfficeType;
use App\Models\LoanType;
use Illuminate\Database\Seeder;

class CashOfficeTypesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $ocl_loan = LoanType::find(constants('LOAN_TYPES.OCL'));
        $ovl_loan = LoanType::find(constants('LOAN_TYPES.OVL'));

        $cash_office_type = CashOfficeType::create([
            'name' => 'GlobalCredit',
            'name_hy' => 'Գլոբալ Կրեդիտ',
            'phone_number' => '011700100',
        ]);
        $cash_office_type->loan_types()->attach($ocl_loan, ['disabled' => true]);
        $cash_office_type->loan_types()->attach($ovl_loan, ['disabled' => false]);

        $cash_office_type = CashOfficeType::create([
            'name' => 'IdramCashdesk',
            'name_hy' => 'Իդրամ',
        ]);
        $cash_office_type->loan_types()->attach($ocl_loan, ['disabled' => true]);
        $cash_office_type->loan_types()->attach($ovl_loan, ['disabled' => true]);

        $cash_office_type = CashOfficeType::create([
            'name' => 'EasyPay',
            'name_hy' => 'Իզի Փեյ',
        ]);
        $cash_office_type->loan_types()->attach($ocl_loan, ['disabled' => true]);
        $cash_office_type->loan_types()->attach($ovl_loan, ['disabled' => true]);

        $cash_office_type = CashOfficeType::create([
            'name' => 'UPay',
            'name_hy' => 'Յուքոմ',
        ]);
        $cash_office_type->loan_types()->attach($ocl_loan, ['disabled' => false]);
        $cash_office_type->loan_types()->attach($ovl_loan, ['disabled' => true]);

        $cash_office_type = CashOfficeType::create([
            'name' => 'Telcell',
            'name_hy' => 'ԹԵԼ-ՍԵԼ',
            'phone_number' => '060272222',
        ]);
        $cash_office_type->loan_types()->attach($ocl_loan, ['disabled' => false]);
        $cash_office_type->loan_types()->attach($ovl_loan, ['disabled' => false]);
    }
}
