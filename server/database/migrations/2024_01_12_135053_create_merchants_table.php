<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMerchantsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('merchants', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('vendor_id')->default(constants('VENDOR_TYPES.INTERNAL_BNPL'));
            $table->string('name')->nullable();
            $table->string('title');
            $table->string('address');
            $table->string('mi_tax_payer_id');
            $table->string('account_number')->nullable();
            $table->string('icon');
            $table->string('link');
            $table->string('type');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('vendor_id')->references('id')->on('vendors');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('merchants');
    }
}
