<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTopUpOfferDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('top_up_offer_details', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('preapproved_credit_offer_id');
            $table->string('contract_number')->nullable();
            $table->float('balance')->nullable();
            $table->float('accrued_interest_amount')->nullable();
            $table->timestamps();

            $table->foreign('preapproved_credit_offer_id')
                ->references('id')
                ->on('preapproved_credit_offers')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('top_up_offer_details');
    }
}
