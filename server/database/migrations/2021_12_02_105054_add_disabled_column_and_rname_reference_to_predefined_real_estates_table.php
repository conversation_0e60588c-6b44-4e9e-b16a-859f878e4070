<?php

use App\Models\PredefinedRealEstate;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDisabledColumnAndRnameReferenceToPredefinedRealEstatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('predefined_real_estates', function (Blueprint $table) {
            $table->dropColumn('region');
            $table->dropColumn('reference');
            $table->integer('region_id');
            $table->integer('developer_company_id');
            $table->string('status')->default(PredefinedRealEstate::FREE);
            $table->boolean('disabled')->default(false);

            $table->foreign('region_id')
                ->references('id')
                ->on('regions');

            $table->foreign('developer_company_id')
                ->references('id')
                ->on('developer_companies');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('predefined_real_estates', function (Blueprint $table) {
            $table->dropColumn('region_id');
            $table->dropColumn('developer_company_id');
            $table->dropColumn('status');
            $table->dropColumn('disabled');
            $table->string('region');
            $table->string('reference');
        });
    }
}
