<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSmsVerificationAttemptsColumnInLoanSecuritiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loan_securities', function (Blueprint $table) {
            $table->integer('sms_verification_attempts')->default(0);
            $table->string('identity_verification_code')->nullable();
            $table->dateTime('identity_verification_code_sent')->nullable();
            $table->dateTime('identity_verification_code_exp')->nullable();
            $table->dateTime('identity_verification_code_recv')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loan_securities', function (Blueprint $table) {
            $table->dropColumn('sms_verification_attempts');
            $table->dropColumn('identity_verification_code');
            $table->dropColumn('identity_verification_code_sent');
            $table->dropColumn('identity_verification_code_exp');
            $table->dropColumn('identity_verification_code_recv');
        });
    }
}
