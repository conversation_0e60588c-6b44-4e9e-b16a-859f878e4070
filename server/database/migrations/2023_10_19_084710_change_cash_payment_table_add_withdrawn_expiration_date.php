<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class ChangeCashPaymentTableAddWithdrawnExpirationDate extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('cash_payments', function (Blueprint $table) {
            $table->dateTime('withdrawn_expiration_date')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('cash_payments', function (Blueprint $table) {
            $table->date('withdrawn_expiration_date')->nullable();
        });
    }
}
