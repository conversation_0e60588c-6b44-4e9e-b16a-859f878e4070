<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIdentifierAndIdentifierTypeAndDateColumnsInEkengRequestsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('ekeng_requests', function (Blueprint $table) {
            $table->string('identifier')->nullable();
            $table->string('identifier_type')->nullable();
            $table->dateTime('date')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('ekeng_requests', function (Blueprint $table) {
            $table->dropColumn('identifier');
            $table->dropColumn('identifier_type');
            $table->dropColumn('date');
        });
    }
}
