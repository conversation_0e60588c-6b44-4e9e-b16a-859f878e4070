<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLoanTypeHcNotesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('loan_type_hc_notes', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('loan_type_id');
            $table->foreign('loan_type_id')->references('id')->on('loan_types');
            $table->string('identifier')->nullable();
            $table->string('note2')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('loan_type_hc_notes');
    }
}
