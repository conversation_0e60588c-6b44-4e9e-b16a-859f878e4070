<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFingerprintLoanSecurityTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('fingerprint_loan_security', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('loan_security_id')->unsigned()->index();
            $table->foreign('loan_security_id')->references('id')->on('loan_securities')->onDelete('cascade');
            $table->integer('fingerprint_id')->unsigned()->index();
            $table->foreign('fingerprint_id')->references('id')->on('fingerprints')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('fingerprint_loan_security');
    }
}
