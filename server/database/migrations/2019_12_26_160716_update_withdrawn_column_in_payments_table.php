<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateWithdrawnColumnInPaymentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('card_to_card_payments', function (Blueprint $table) {
            $table->dateTime('withdrawn')->nullable()->change();
        });

        Schema::table('easypay_wallet_payments', function (Blueprint $table) {
            $table->dateTime('withdrawn')->nullable()->change();
        });

        Schema::table('idram_wallet_payments', function (Blueprint $table) {
            $table->dateTime('withdrawn')->nullable()->change();
        });

        Schema::table('wire_payments', function (Blueprint $table) {
            $table->dateTime('withdrawn')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('card_to_card_payments', function (Blueprint $table) {
            $table->date('withdrawn')->nullable()->change();
        });

        Schema::table('easypay_wallet_payments', function (Blueprint $table) {
            $table->date('withdrawn')->nullable()->change();
        });

        Schema::table('idram_wallet_payments', function (Blueprint $table) {
            $table->date('withdrawn')->nullable()->change();
        });

        Schema::table('wire_payments', function (Blueprint $table) {
            $table->date('withdrawn')->nullable()->change();
        });
    }
}
