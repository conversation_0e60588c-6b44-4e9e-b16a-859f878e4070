<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AddReasonIdInCreditOfferCutOffsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('credit_offer_cut_offs', function (Blueprint $table) {
            $table->integer('reason_id')->nullable();
            $table->foreign('reason_id')->references('id')->on('credit_offer_cut_off_reasons');
        });

        // We need to replace 'name' with 'reason_id' and remove 'reason' column
        DB::transaction(function () {
            DB::table('credit_offer_cut_offs')->orderBy('id')->chunkById(1000, function ($items) {
                foreach ($items as $item) {
                    $reason = DB::table('credit_offer_cut_off_reasons')->where('name', $item->reason)->first();
                    if ($reason) {
                        DB::table('credit_offer_cut_offs')
                            ->where('id', $item->id)
                            ->update(['reason_id' => $reason->id]);
                    }
                }
            });

            Schema::table('credit_offer_cut_offs', function (Blueprint $table) {
                $table->dropColumn('reason');
            });
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('credit_offer_cut_offs', function (Blueprint $table) {
            $table->dropForeign(['credit_offer_cut_offs_reason_id_foreign']);
            $table->dropColumn('reason_id');
            $table->string('reason')->nullable();
        });
    }
}
