<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddLoanTypeIdInLoanSubtypesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loan_subtypes', function (Blueprint $table) {
            $table->integer('loan_type_id')->nullable();
            $table->foreign('loan_type_id')->references('id')->on('loan_types');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loan_subtypes', function (Blueprint $table) {
            $table->dropForeign('loan_subtypes_loan_type_id_foreign');
            $table->dropColumn('loan_type_id');
        });
    }
}
