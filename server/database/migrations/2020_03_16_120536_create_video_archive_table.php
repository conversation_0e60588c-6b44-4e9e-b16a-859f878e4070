<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVideoArchiveTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('video_archives', function (Blueprint $table) {
            $table->increments('id');
            $table->string('archive_id');
            $table->string('resolution')->nullable();
            $table->string('status')->nullable();
            $table->string('path')->nullable();
            $table->integer('loan_security_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('video_archives');
    }
}
