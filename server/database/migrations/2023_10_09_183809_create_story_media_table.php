<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStoryMediaTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('story_media', function (Blueprint $table) {
            $table->increments('id');
            $table->string('logo');
            $table->string('prefix')->nullable();
            $table->string('title');
            $table->text('description');
            $table->string('image_src');
            $table->string('background_image_src');
            $table->string('button_text');
            $table->string('navigator')->nullable();
            $table->string('screen')->nullable();
            $table->string('screen_title')->nullable();
            $table->string('loan_type_id')->nullable();
            $table->boolean('is_trading');
            $table->boolean('auth_required');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('story_media');
    }
}
