<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDocumentTypeColumnInArpiSolarMediaTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('arpi_solar_media', function (Blueprint $table) {
            $table->string('document_type')->default('media');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('arpi_solar_media', function (Blueprint $table) {
            $table->dropColumn('document_type');
        });
    }
}
