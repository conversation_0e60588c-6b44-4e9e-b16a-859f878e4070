<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateImIdDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('im_id_details', function (Blueprint $table) {
            $table->increments('id');
            $table->string('ssn')->nullable();
            $table->string('session_id');
            $table->string('notification_token');
            $table->string('phone_number');
            $table->string('status');
            $table->integer('loan_security_id')->nullable();
            $table->foreign('loan_security_id')->references('id')->on('loan_securities')->onDelete('cascade');
            $table->index(['loan_security_id']);
            $table->timestamp('expiration')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('im_id_details');
    }
}
