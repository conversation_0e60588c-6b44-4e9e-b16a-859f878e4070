<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeGivenAndExpireDateTypesInCitizenPassportsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('citizen_passports', function (Blueprint $table) {
            $table->date('given_date')->nullable()->change();
            $table->date('expire_date')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('citizen_passports', function (Blueprint $table) {
            $table->dateTime('given_date')->nullable()->change();
            $table->dateTime('expire_date')->nullable()->change();
        });
    }
}
