<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTrimColumnInVehicleModelsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('vehicle_models', function (Blueprint $table) {
            $table->string('trim')->nullable();
            $table->dropUnique('vehicle_models_police_code_unique');
        });

        Schema::table('vehicle_models', function (Blueprint $table) {
            $table->unique(['police_code', 'trim']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('vehicle_models', function (Blueprint $table) {
            $table->dropUnique('vehicle_models_police_code_trim_unique');
        });

        Schema::table('vehicle_models', function (Blueprint $table) {
            $table->dropColumn('trim');
            $table->unique('police_code');
        });
    }
}
