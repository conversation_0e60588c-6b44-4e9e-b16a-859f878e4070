<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPaymentStatusInPaymentTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('card_to_card_payments', function (Blueprint $table) {
            $table->string('payment_status')->nullable();
        });

        Schema::table('idram_wallet_payments', function (Blueprint $table) {
            $table->string('payment_status')->nullable();
        });

        Schema::table('wallet_loan_payments', function (Blueprint $table) {
            $table->string('payment_status')->nullable();
        });

        Schema::table('wire_payments', function (Blueprint $table) {
            $table->string('payment_status')->nullable();
        });

        Schema::table('easypay_wallet_payments', function (Blueprint $table) {
            $table->string('payment_status')->nullable();
        });

        Schema::table('velox_payments', function (Blueprint $table) {
            $table->string('payment_status')->nullable();
            $table->dateTime('paid')->nullable()->change();
            $table->dateTime('withdrawn')->nullable()->change();
        });

        Schema::table('product_provisions', function (Blueprint $table) {
            $table->string('payment_status')->nullable();
        });

        Schema::table('cash_payments', function (Blueprint $table) {
            $table->string('payment_status')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('card_to_card_payments', function (Blueprint $table) {
            $table->dropColumn('payment_status');
        });

        Schema::table('idram_wallet_payments', function (Blueprint $table) {
            $table->dropColumn('payment_status');
        });

        Schema::table('wallet_loan_payments', function (Blueprint $table) {
            $table->dropColumn('payment_status');
        });

        Schema::table('wire_payments', function (Blueprint $table) {
            $table->dropColumn('payment_status');
        });

        Schema::table('easypay_wallet_payments', function (Blueprint $table) {
            $table->dropColumn('payment_status');
        });

        Schema::table('velox_payments', function (Blueprint $table) {
            $table->date('withdrawn')->nullable()->change();
            $table->date('paid')->nullable()->change();
            $table->dropColumn('payment_status');
        });

        Schema::table('product_provisions', function (Blueprint $table) {
            $table->dropColumn('payment_status');
        });

        Schema::table('cash_payments', function (Blueprint $table) {
            $table->dropColumn('payment_status');
        });
    }
}
