<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePurchaseSessionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('purchase_sessions', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('order_id');
            $table->string('payment_id');
            $table->integer('vendor_id')->nullable();
            $table->foreign('vendor_id')->references('id')->on('vendors');
            $table->dateTime('payment_id_exp');
            $table->string('callback_url')->nullable();
            $table->string('amount')->nullable();
            $table->string('currency')->nullable();
            $table->string('description')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('purchase_sessions');
    }
}
