<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAccruedServiceFeeAmountInTopUpOfferDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('top_up_offer_details', function (Blueprint $table) {
            $table->float('accrued_service_fee_amount')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('top_up_offer_details', function (Blueprint $table) {
            $table->dropColumn('accrued_service_fee_amount');
        });
    }
}
