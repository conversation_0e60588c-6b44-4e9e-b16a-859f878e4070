<?php

namespace App\Helpers;

use App\Exceptions\InternalErrorException;
use Closure;
use Log;
use Throwable;

class ObjectMerger
{
    public function __construct($context_schema)
    {
        $this->context_schema = $context_schema;
    }

    public function merge($data)
    {
        $result = [];
        foreach ($this->context_schema as $name => $schema) {
            if (!isset($schema['visible']) || $schema['visible']) {
                $mergeable = isset($schema['mergeable']) && $schema['mergeable'];

                try {
                    // A 'path' can be specified as both function and dot notation path
                    if ($schema['path'] instanceof Closure) {
                        $result[$name] = $schema['path']($data);
                    } else {
                        $result[$name] = DotNotationHelper::extractByDotNotation($schema['path'], $data, $mergeable);
                    }
                } catch (Throwable $e) {
                    Log::critical('Object merger: Unable to extract the path', [
                        'name' => $name,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                    ]);
                    throw new InternalErrorException();
                }

                // Format the extracted value, if a formatter is provided
                if (isset($schema['formatter'])) {
                    $result[$name] = $schema['formatter']($result[$name], $data);
                }
            }
        }

        return $result;
    }
}
