<?php

namespace App\Mocks;

use Carbon\Carbon;

class MockSoapWrapper
{
    public function add($name, $closure)
    {
        return $this;
    }

    public function call($name, $options)
    {
        if ($name === 'ARCA.CheckCardAsmx') {
            $res = [
                'CheckCardAsmxResult' => true,
            ];

            return (object) $res;
        }

        if ($name === 'ARCA.CardToCardTransferAsmx') {
            $res = [
                'CardToCardTransferAsmxResult' => (object) [
                    'ResponseCode' => '00',
                    'ProcessingCode' => '490000',
                    'SystemTraceAuditNumber' => '798309',
                    'LocalTransactionDate' => Carbon::now()->format('Y-m-d H:i:s'),
                    'RRN' => '001009980255',
                    'AuthorizationIdResponse' => '980257',
                ],
            ];

            return (object) $res;
        }
    }
}
