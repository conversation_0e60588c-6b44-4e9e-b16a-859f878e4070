<?php

namespace App\Schemas;

use App\Helpers\DateHelper;

class ContextSchema extends BaseSchema
{
    public function get()
    {
        return [
            'isDead' => [
                'path' => [
                    'ekeng.passport_data.IsDead',
                    'nork.IsDead',
                ],
                'visible' => true,
            ],
            'age' => [
                'path' => [
                    'ekeng.passport_data.AVVDocuments.AVVDocument.BirthDate' => function ($value) {
                        return carbon_create_format(constants('EKENG_DATE_FORMAT'), $value)->age;
                    },
                    'ekeng.passport_data.AVVDocuments.AVVDocument.0.BirthDate' => function ($value) {
                        return carbon_create_format(constants('EKENG_DATE_FORMAT'), $value)->age;
                    },
                    'nork.Birthdate' => function ($value) {
                        return carbon_create_format(constants('NORK_DATE_FORMAT'), $value)->age;
                    },
                ],
                'visible' => true,
            ],
            'factor' => [
                'path' => [
                    'walletInfo.Factor',
                ],
                'formatter' => function ($value) {
                    return $this->nullToZeroParser($value);
                },
                'visible' => true,
            ],
            'walletAmount' => [
                'path' => [
                    'walletInfo.Amount',
                ],
                'visible' => true,
            ],
            'walletEmail' => [
                'path' => [
                    'walletInfo.Email',
                ],
                'visible' => true,
            ],
            'walletUserDuration' => [
                'path' => [
                    'walletInfo.AccountCreated' => function ($value) {
                        return carbon_parse($value)->diffInDays(now());
                    },
                ],
                'visible' => true,
            ],
            'citizenship' => [
                'path' => [
                    'nork' => function ($nork) {
                        return $this->setCitizenship($nork);
                    },
                ],
                'visible' => true,
            ],
            'isNewOclCustomer' => [
                'path' => [
                    'isNewOclCustomer',
                ],
                'visible' => true,
            ],
            'isNewOvlCustomer' => [
                'path' => [
                    'is_new_ovl_customer',
                ],
                'visible' => true,
            ],
            'firstTransactionDuration' => [
                'path' => [
                    'firstTransactionDuration',
                ],
                'visible' => true,
            ],
            'walletLoansCount' => [
                'path' => [
                    'walletLoansCount',
                ],
                'visible' => true,
            ],
            'walletActiveLoansBalance' => [
                'path' => [
                    'walletActiveLoansBalance',
                ],
                'visible' => true,
            ],
            'drScore' => [
                'path' => [
                    'dr.dr_score',
                ],
                'formatter' => function ($value) {
                    return is_null($value) ? -1 : $value;
                },
                'visible' => true,
            ],
            'isQKH' => [
                'path' => [
                    'ekeng.passport_data' => function ($value) {
                        return $this->isQKH($value);
                    },
                ],
                'visible' => true,
            ],
            'salary' => [
                'path' => [
                    'nork.WorkData' => function ($workplaces) {
                        return $this->getTotalSalary($workplaces);
                    },
                ],
                'formatter' => function ($value) {
                    return empty($value) ? 0 : $value;
                },
                'visible' => true,
            ],
            'factoredSalary' => [
                'path' => [
                    'nork.WorkData' => function ($workplaces) {
                        return $this->getTotalSalaryWithMultipliers($workplaces);
                    },
                ],
                'visible' => true,
            ],
            'netSalary' => [
                'path' => [
                    'nork.WorkData' => function ($workplaces) {
                        return $this->getNetSalary($workplaces);
                    },
                ],
                'formatter' => function ($value) {
                    return empty($value) ? 0 : $value;
                },
                'visible' => true,
            ],
            'isNewOrganisation' => [
                'path' => [
                    'acra.PARTICIPIENT.TaxServiceInfo.Response.RegistrationDate' => function ($date) {
                        return $this->isNewOrganisation(carbon_create_format(constants('ACRA_DATE_FORMAT'), $date));
                    },
                ],
                'visible' => true,
            ],
            'fico' => [
                'path' => [
                    'acra.PARTICIPIENT' => function ($value) {
                        return $this->getFicoScore($value);
                    },
                ],
                'formatter' => function ($fico, $context) {
                    return $this->getEffectiveFicoScore($fico, $context);
                },
                'visible' => true,
            ],
            'existing_fico' => [
                'path' => [
                    'acra.PARTICIPIENT' => function ($value) {
                        return $this->getFicoScore($value);
                    },
                ],
                'visible' => true,
            ],
            'hasLongHistory' => [
                'path' => [
                    'acra.PARTICIPIENT.Loans.Loan' => function ($loans) {
                        return $this->hasLongHistory($loans);
                    },
                ],
            ],
            'acraActiveLoans' => [
                'path' => [
                    'acra.PARTICIPIENT.Loans.Loan' => function ($loans) {
                        return $this->AcraActiveLoanCount($loans);
                    },
                ],
                'formatter' => function ($value) {
                    return $this->nullToZeroParser($value);
                },
                'visible' => true,
            ],
            'acraStatus' => [
                'path' => [
                    'acra.PARTICIPIENT.ThePresenceData',
                ],
                'visible' => true,
            ],
            'vehicleMarketPrice' => [
                'path' => [
                    'vehicle.market_price',
                ],
                'formatter' => function ($value) {
                    return $this->nullToZeroParser($value);
                },
                'visible' => true,
            ],
            'vehicleExist' => [
                'path' => [
                    'vehicle.exist',
                ],
                'visible' => true,
            ],
            'checkTrade' => [
                'path' => [
                    'check_trade',
                ],
                'visible' => true,
            ],
            'vehicleVin' => [
                'path' => [
                    'vehicle.vin',
                ],
                'visible' => true,
            ],
            'ownerCertId' => [
                'path' => [
                    'vehicle.owner_cert_id',
                ],
                'visible' => true,
            ],
            'loanLoad' => [
                'path' => [
                    'acra.PARTICIPIENT.Loans.Loan' => function ($loans) {
                        return $this->calculateLoad($loans);
                    },
                ],
                'formatter' => function ($value) {
                    return $this->nullToZeroParser($value);
                },
                'visible' => true,
            ],
            'isValidLoanClass12' => [
                'path' => [
                    'acra.PARTICIPIENT' => function ($source) {
                        return $this->isValidLoanClass12($source);
                    },
                ],
                'visible' => true,
            ],
            'loanClass' => [
                'path' => [
                    'acra.PARTICIPIENT.TheWorstClassLoan',
                ],
                'visible' => true,
            ],
            'hasOverdueLoans' => [
                'path' => [
                    'acra.PARTICIPIENT.CurrentOverdueLoans.CurrentOverdueLoan' => function ($source) {
                        return $this->hasOverdueLoans($source);
                    },
                ],
                'visible' => true,
            ],
            'delayQuantity' => [
                'path' => [
                    'acra.PARTICIPIENT.DelayQuantity',
                ],
                'visible' => true,
            ],
            'delayQuantityInYear' => [
                'path' => [
                    'acra.PARTICIPIENT.Loans.Loan' => function ($loans) {
                        return $this->getDelayQuantityInYear($loans);
                    },
                ],
                'visibility' => true,
            ],
            'requestQuantity' => [
                'path' => [
                    'acra.PARTICIPIENT.RequestQuantity30',
                ],
                'visible' => true,
            ],
            'hasCredit' => [
                'path' => [
                    'hc.hasCredit',
                ],
                'visible' => true,
            ],
            'hasNonSyncedCredits' => [
                'path' => [
                    'hc.hasNonSyncedCredits',
                ],
                'visible' => true,
            ],
            'activeCreditBalance' => [
                'path' => [
                    'hc.activeCreditBalance',
                ],
                'visible' => true,
            ],
            'minMonthlyPaymentDatePassed' => [
                'path' => [
                    'hc.minMonthlyPaymentDatePassed',
                ],
                'visible' => true,
            ],
            'minDisbursementDatePassed' => [
                'path' => [
                    'hc.minDisbursementDatePassed',
                ],
                'visible' => true,
            ],
            'hasValidLoanClass' => [
                'path' => [
                    'hc.hasValidLoanClass',
                ],
                'visible' => true,
            ],
            'hasPendingMonthlyPayment' => [
                'path' => [
                    'hc.hasPendingMonthlyPayment',
                ],
                'visible' => true,
            ],
            'hasOverdueFee' => [
                'path' => [
                    'hc.hasOverdueFee',
                ],
                'visible' => true,
            ],
            'hasAdvancePayment' => [
                'path' => [
                    'hc.hasAdvancePayment',
                ],
                'visible' => true,
            ],
            'hasDayCloseIssue' => [
                'path' => [
                    'hc.hasDayCloseIssue',
                ],
                'visible' => true,
            ],
            'notAcceptableTopUpLoanTypeId' => [
                'path' => [
                    'notAcceptableTopUpLoanTypeId',
                ],
                'visible' => true,
            ],
            'totalCount' => [
                'path' => [
                    'loanTotal.totalCount',
                ],
                'visible' => true,
            ],
            'totalAmount' => [
                'path' => [
                    'loanTotal.totalAmount',
                ],
                'visible' => true,
            ],
            'totalNightCount' => [
                'path' => [
                    'loanTotal.totalNightCount',
                ],
                'visible' => true,
            ],
            'totalNightAmount' => [
                'path' => [
                    'loanTotal.totalNightAmount',
                ],
                'visible' => true,
            ],
            'isWeekend' => [
                'path' => [
                    'loanTotal.isWeekend',
                ],
                'visible' => true,
            ],
            'isNight' => [
                'path' => [
                    'loanTotal.isNight',
                ],
                'visible' => true,
            ],
            'minAmount' => [
                'path' => [
                    'loanTotal.minAmount',
                ],
                'visible' => true,
            ],
            'maxAmount' => [
                'path' => [
                    'loanTotal.maxAmount',
                ],
                'visible' => true,
            ],
            'vehicleMaxPrice' => [
                'path' => [
                    'vehicle_max_price',
                ],
                'visible' => true,
            ],
            'qrDiscount' => [
                'path' => [
                    'loanTotal.qr_discount',
                ],
                'visible' => true,
            ],
            'socialCard' => [
                'path' => [
                    'ekeng.SSN',
                    'nork.Soccard',
                ],
                'visible' => true,
            ],
            'isBlocked' => [
                'path' => [
                    'vehicle.is_blocked',
                ],
                'visible' => true,
            ],
            'allowedLending' => [
                'path' => [
                    'vehicle.lenders',
                ],
                'formatter' => function ($value) {
                    return $value === [] || $value === null;
                },
                'visible' => true,
            ],
            'hasSingleOwner' => [
                'path' => [
                    'vehicle.owners' => function ($source) {
                        return count($source) === constants('ALLOWED_OWNER_COUNT_OVL');
                    },
                ],
                'visible' => true,
            ],
            'allowedDuration' => [
                'path' => [
                    'ekeng.passport_data.AVVDocuments.AVVDocument.BirthDate' => function ($value) {
                        return DateHelper::maxDuration(
                            carbon_create_format(constants('EKENG_DATE_FORMAT'), $value),
                            constants('MAX_AGE_BY_MONTH.OVL')
                        );
                    },
                    'ekeng.passport_data.AVVDocuments.AVVDocument.0.BirthDate' => function ($value) {
                        return DateHelper::maxDuration(
                            carbon_create_format(constants('EKENG_DATE_FORMAT'), $value),
                            constants('MAX_AGE_BY_MONTH.OVL')
                        );
                    },
                    'nork.Birthdate' => function ($value) {
                        return DateHelper::maxDuration(
                            carbon_create_format(constants('NORK_DATE_FORMAT'), $value),
                            constants('MAX_AGE_BY_MONTH.OVL')
                        );
                    },
                ],
                'visible' => true,
            ],
            'allowedDurationREML' => [
                'path' => [
                    'ekeng.passport_data.AVVDocuments.AVVDocument.BirthDate' => function ($value) {
                        return DateHelper::maxDuration(
                            carbon_create_format(constants('EKENG_DATE_FORMAT'), $value),
                            constants('MAX_AGE_BY_MONTH.REML')
                        );
                    },
                    'ekeng.passport_data.AVVDocuments.AVVDocument.0.BirthDate' => function ($value) {
                        return DateHelper::maxDuration(
                            carbon_create_format(constants('EKENG_DATE_FORMAT'), $value),
                            constants('MAX_AGE_BY_MONTH.REML')
                        );
                    },
                    'nork.Birthdate' => function ($value) {
                        return DateHelper::maxDuration(
                            carbon_create_format(constants('NORK_DATE_FORMAT'), $value),
                            constants('MAX_AGE_BY_MONTH.REML')
                        );
                    },
                ],
                'visible' => true,
            ],
            'hasAddress' => [
                'path' => [
                    'ekeng.passport_data' => function ($value) {
                        return $this->hasAddress($value);
                    },
                ],
            ],
            'maxLimit' => [
                'path' => [
                    'vendor.maxLimit',
                ],
                'visible' => true,
            ],
            'duration' => [
                'path' => [
                    'vendor.duration',
                ],
                'visible' => true,
            ],
            'osm' => [
                'path' => [
                    function ($source) {
                        return $this->getOsm($source);
                    },
                ],
                'formatter' => function ($value) {
                    return $this->nullToZeroParser($value);
                },
                'visible' => true,
            ],
            'dstiRepayment' => [
                'path' => [
                    function ($source) {
                        return $this->getDstiRepayment($source)['repayment'];
                    },
                ],
                'formatter' => function ($value) {
                    return $this->nullToZeroParser($value);
                },
                'visible' => true,
            ],
            'dstiIncome' => [
                'path' => [
                    function ($source) {
                        return $this->dstiIncome($source);
                    },
                ],
                'formatter' => function ($value) {
                    return $this->nullToZeroParser($value);
                },
                'visible' => true,
            ],
            'realEstatePrice' => [
                'path' => [
                    'real_estate_price',
                ],
                'visible' => true,
            ],
            'realEstatePrepayment' => [
                'path' => [
                    'pre_payment',
                ],
                'visible' => true,
            ],
            'loanSubTypeId' => [
                'path' => [
                    'loan_subtype_id',
                ],
                'visible' => true,
            ],
            'premlSubType' => [
                'path' => [
                    function () {
                        return $this->resolveDeveloperCompany();
                    },
                ],
                'visible' => true,
            ],
            'acraLoansMonthlyRepayments' => [
                'path' => [
                    'acra.PARTICIPIENT.Loans.Loan' => function ($loans) {
                        return $this->acraLoansMonthlyRepayments($loans);
                    },
                ],
                'formatter' => function ($value) {
                    return $this->nullToZeroParser($value);
                },
                'visible' => true,
            ],
            'REMLDuration' => [
                'path' => [
                    'reml_duration',
                ],
                'visible' => true,
            ],
            'veloxLoanOfferDuration' => [
                'path' => [
                    'velox_loan_offer_duration',
                ],
                'visible' => true,
            ],
            'hasPendingVeloxLoan' => [
                'path' => [
                    'hasPendingVeloxLoan',
                ],
                'visible' => true,
            ],
            'hasPendingOCL' => [
                'path' => [
                    'hasPendingOCL',
                ],
                'visible' => true,
            ],
            'vlxProductCategoryId' => [
                'path' => [
                    'product_category_id',
                ],
                'visible' => true,
            ],
            'calculateCreditLimit' => [
                'path' => [
                    'calculate_credit_limit',
                ],
                'visible' => true,
            ],
            'rule_group' => [
                'path' => [
                    'rule_group',
                ],
                'formatter' => function ($value) {
                    return is_null($value) ? 'A' : $value;
                },
                'visible' => true,
            ],
            'hasActiveOclOrWallet' => [
                'path' => [
                    'hc.hasActiveOclOrWallet',
                ],
                'visible' => true,
            ],
            'convertedBnplBalance' => [
                'path' => [
                    'converted_bnpl_balance',
                ],
                'visible' => true,
            ],
        ];
    }
}
