<?php

namespace App\Schemas;

class VehicleInfoSchema
{
    public function get()
    {
        return [
            'model_name' => [
                'path' => [
                    'vehicle.model_name',
                ],
            ],
            'model' => [
                'path' => [
                    'vehicle.model',
                ],
            ],
            'model_code' => [
                'path' => [
                    'vehicle.model_code',
                    'vehicle.police_code',
                ],
            ],
            'number' => [
                'path' => [
                    'vehicle.number',
                ],
            ],
            'vin' => [
                'path' => [
                    'vehicle.vin',
                ],
            ],
            'owner_cert_id' => [
                'path' => [
                    'vehicle.owner_cert_id',
                ],
            ],
            'cert_num' => [
                'path' => [
                    'vehicle.cert_num',
                ],
            ],
            'vehicle_group' => [
                'path' => [
                    'vehicle.vehicle_group',
                ],
            ],
            'fuel_type' => [
                'path' => [
                    'vehicle.fuel_type',
                ],
            ],
            'color' => [
                'path' => [
                    'vehicle.color',
                ],
            ],
            'recording_date' => [
                'path' => [
                    'vehicle.recording_date',
                ],
            ],
            'released' => [
                'path' => [
                    'vehicle.released',
                ],
            ],
            'vehicle_type' => [
                'path' => [
                    'vehicle.vehicle_type',
                ],
            ],
            'body_type' => [
                'path' => [
                    'vehicle.body_type',
                ],
            ],
            'engine_num' => [
                'path' => [
                    'vehicle.engine_num',
                ],
            ],
            'owners' => [
                'path' => [
                    'vehicle.owners',
                ],
            ],
            'engine_power' => [
                'path' => [
                    'vehicle.engine_power',
                ],
            ],
            'engine_hp' => [
                'path' => [
                    'vehicle.engine_hp',
                ],
            ],
        ];
    }
}
