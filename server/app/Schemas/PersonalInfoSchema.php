<?php

namespace App\Schemas;

use App\Helpers\ArrayHelper;
use App\Helpers\PassportHelper;
use Carbon\Carbon;
use function Functional\filter;
use function Functional\first;
use function Functional\map;
use function Functional\sort;

class PersonalInfoSchema extends BaseSchema
{
    public function get()
    {
        return [
            'first_name' => [
                'path' => [
                    'ekeng.first_name',
                    'ekeng.first-name',
                    'acra.PARTICIPIENT.FirstName',
                    'nork.Firstname',
                ],
            ],
            'last_name' => [
                'path' => [
                    'ekeng.last_name',
                    'acra.PARTICIPIENT.LastName',
                    'nork.Lastname',
                ],
            ],
            'middle_name' => [
                'path' => [
                    'ekeng.passport_data.AVVDocuments.AVVDocument.MiddleName',
                    'ekeng.passport_data.AVVDocuments.AVVDocument.0.MiddleName',
                    'acra.PARTICIPIENT.MiddleName',
                    'nork.Middlename',
                ],
            ],
            'first_name_en' => [
                'path' => [
                    'ekeng.passport_data.AVVDocuments.AVVDocument.EnglishFirstName',
                    'ekeng.passport_data.AVVDocuments.AVVDocument.0.EnglishFirstName',
                ],
            ],
            'last_name_en' => [
                'path' => [
                    'ekeng.passport_data.AVVDocuments.AVVDocument.EnglishLastName',
                    'ekeng.passport_data.AVVDocuments.AVVDocument.0.EnglishLastName',
                ],
            ],
            'middle_name_en' => [
                'path' => [
                    'ekeng.passport_data.AVVDocuments.AVVDocument.EnglishMiddleName',
                    'ekeng.passport_data.AVVDocuments.AVVDocument.0.EnglishMiddleName',
                ],
            ],
            'gender' => [
                'path' => [
                    'ekeng.passport_data.AVVDocuments.AVVDocument.Gender' => function ($v) {
                        return constants('GENDERS.'.$v);
                    },
                    'ekeng.passport_data.AVVDocuments.AVVDocument.0.Gender' => function ($v) {
                        return constants('GENDERS.'.$v);
                    },
                    'nork.Gender',
                ],
            ],
            'birth_date' => [
                'path' => [
                    'ekeng.passport_data.AVVDocuments.AVVDocument.BirthDate' => function ($value) {
                        return Carbon::createFromFormat(constants('EKENG_DATE_FORMAT'), $value)->toDateTimeString();
                    },
                    'ekeng.passport_data.AVVDocuments.AVVDocument.0.BirthDate' => function ($value) {
                        return Carbon::createFromFormat(constants('EKENG_DATE_FORMAT'), $value)->toDateTimeString();
                    },
                    'nork.Birthdate' => function ($value) {
                        return Carbon::createFromFormat(constants('NORK_DATE_FORMAT'), $value)->toDateTimeString();
                    },
                ],
            ],
            'photo' => [
                'path' => [
                    'ekeng.passport_data.Photo',
                ],
            ],
            'ssn' => [
                'path' => [
                    'ekeng.SSN',
                ],
            ],
            'passports' => [
                'path' => [
                    function ($source) {
                        $passport_data = $source['ekeng']['passport_data'] ?? [];
                        $docs = $passport_data->AVVDocuments->AVVDocument ?? null;

                        $docs = PassportHelper::getPassportNumbers($docs);

                        $docs = map($docs, function ($d) {
                            $given_date = Carbon::createFromFormat(constants('EKENG_DATE_FORMAT'), $d->IssuanceDate)->toDateTimeString();
                            $expire_date = Carbon::createFromFormat(constants('EKENG_DATE_FORMAT'), $d->ValidityDate)->toDateTimeString();

                            return [
                                'type' => $d->DocumentIdentifier->DocumentType,
                                'passport_number' => $d->DocumentIdentifier->DocumentNumber,
                                'given_date' => $given_date,
                                'expire_date' => $expire_date,
                                'from' => $d->DocumentDepartment,
                                'primary' => false,
                            ];
                        });

                        array_push($docs, [
                            'type' => constants('SOC_CARD'),
                            'passport_number' => $passport_data->PNum ?? null,
                            'primary' => false,
                        ]);

                        return $docs;
                    },
                    function ($source) {
                        $source = $source['nork'] ?? [];

                        $doc_mapping = [
                            'Passport' => constants('NON_BIOMETRIC_PASSPORT'),
                            'Biometric' => constants('BIOMETRIC_PASSPORT'),
                            'IdCard' => constants('ID_CARD'),
                            'Soccard' => constants('SOC_CARD'),
                        ];

                        $res = array_map(function ($k) use ($doc_mapping, $source) {
                            $type = $doc_mapping[$k];
                            $passport_number = $source[$k] ?? null;
                            $given = $source[$k.'Date'] ?? null;
                            $expire = $source[$k.'Vdate'] ?? null;
                            $from = $source[$k.'Where'] ?? null;

                            // Appart from soc card all documents must have valid expiration date
                            if (
                                $type != constants('SOC_CARD') &&
                                (!$expire || Carbon::createFromFormat(constants('NORK_DATE_FORMAT'), $expire)->lt(Carbon::now()))) {
                                return null;
                            }

                            $given_date = $given ? Carbon::createFromFormat(constants('NORK_DATE_FORMAT'), $given)->toDateTimeString() : null;
                            $expire_date = $expire ? Carbon::createFromFormat(constants('NORK_DATE_FORMAT'), $expire)->toDateTimeString() : null;

                            return $passport_number ? [
                                'type' => $type,
                                'passport_number' => $passport_number,
                                'given_date' => $given_date,
                                'expire_date' => $expire_date,

                                'from' => $from,
                                'primary' => false,
                            ] : null;
                        }, array_keys($doc_mapping));

                        return array_values(array_filter($res));
                    },
                ],
                'formatter' => function ($value) {
                    return ArrayHelper::uniqueByKey($value, 'type');
                },
                'mergeable' => true,
            ],
            'citizenship' => [
                'path' => [
                    'nork.Citizenship',
                ],
            ],
            'country' => [
                'path' => [
                    'ekeng.passport_data' => function ($source) {
                        $passport_data = $source ?? [];
                        $docs = $passport_data->AVVDocuments->AVVDocument;
                        $docs = gettype($docs) == 'array' ? $docs : [$docs];

                        return $docs[0]->Citizenship->CountryName ?? null;
                    },
                ],
            ],
            'region' => [
                'path' => [
                    function ($source) {
                        [ $region, $community ] = $this->getRegionAndCommunityFromEkeng($source);

                        return $this->getRegion($region, $community);
                    },
                    function ($source) {
                        $source = $source['nork'] ?? [];

                        $region = $source['Region'] ?? '';
                        $community = $source['Community'] ?? '';

                        return $this->getRegion($region, $community);
                    },
                ],
            ],
            'location_code' => [
                'path' => [
                    'ekeng.passport_data.AVVRegistrationAddress.LocationCode',
                ],
            ],
            'city' => [
                'path' => [
                    function ($source) {
                        [ $region, $community ] = $this->getRegionAndCommunityFromEkeng($source);

                        return $this->getCity($region, $community);
                    },
                    function ($source) {
                        $source = $source['nork'] ?? [];

                        $region = $source['Region'] ?? '';
                        $community = $source['Community'] ?? '';

                        return $this->getCity($region, $community);
                    },
                ],
            ],
            'district' => [
                'path' => [
                    function ($source) {
                        [ $region, $community ] = $this->getRegionAndCommunityFromEkeng($source);

                        return $this->getCity($region, $community);
                    },
                    function ($source) {
                        $source = $source['nork'] ?? [];

                        $region = $source['Region'] ?? '';
                        $community = $source['Community'] ?? '';

                        return $this->getCity($region, $community);
                    },
                ],
            ],
            'street' => [
                'path' => [
                    'ekeng.passport_data.AVVRegistrationAddress.Street',
                    'nork.Street',
                ],
            ],
            'building' => [
                'path' => [
                    'ekeng.passport_data.AVVRegistrationAddress.Building',
                    'nork.Building',
                ],
            ],
            'apartment' => [
                'path' => [
                    'ekeng.passport_data.AVVRegistrationAddress.Apartment',
                    'nork.Apartment',
                ],
            ],
            'fico_score' => [
                'path' => [
                    'acra.PARTICIPIENT' => function ($value) {
                        return $this->getFicoScore($value);
                    },
                ],
                'formatter' => function ($fico, $context) {
                    return $this->getEffectiveFicoScore($fico, $context);
                },
                'visible' => true,
            ],
            'existing_fico_score' => [
                'path' => [
                    'acra.PARTICIPIENT' => function ($value) {
                        return $this->getFicoScore($value);
                    },
                ],
                'visible' => true,
            ],
            'spouse' => [
                'path' => [
                    'ekeng.e_civil' => function ($value) {
                        if (isset($value->status) && $value->status === 'failed') {
                            return null;
                        }

                        return $this->getSpouse($value);
                    },
                ],
            ],
            'salary' => [
                'path' => [
                    'nork.WorkData' => function ($value) {
                        return $this->getTotalSalary($value);
                    },
                ],
                'formatter' => function ($value) {
                    if ($value == []) {
                        return 0;
                    }

                    return $value;
                },
            ],
            'net_salary' => [
                'path' => [
                    'nork.WorkData' => function ($workplaces) {
                        return $this->getNetSalary($workplaces);
                    },
                ],
                'formatter' => function ($value) {
                    if ($value == []) {
                        return 0;
                    }

                    return $value;
                },
                'visible' => true,
            ],
            'estimated_income' => [
                'path' => [
                    function ($source) {
                        return $this->getOsm($source);
                    },
                ],
                'formatter' => function ($value) {
                    return $this->nullToZeroParser($value);
                },
                'visible' => true,
            ],
            'is_new_ovl_customer' => [
                'path' => [
                    'is_new_ovl_customer',
                ],
                'visible' => true,
            ],
            'osm' => [
                'path' => [
                    function ($source) {
                        return $this->getOsm($source);
                    },
                ],
                'formatter' => function ($value) {
                    return $this->nullToZeroParser($value);
                },
                'visible' => true,
            ],
            'dsti' => [
                'path' => [
                    function ($source) {
                        return $this->getDstiRepayment($source)['repayment'];
                    },
                ],
                'formatter' => function ($value) {
                    return $this->nullToZeroParser($value);
                },
                'visible' => true,
            ],
            'dsti_income' => [
                'path' => [
                    function ($source) {
                        return $this->dstiIncome($source);
                    },
                ],
                'formatter' => function ($value) {
                    return $this->nullToZeroParser($value);
                },
                'visible' => true,
            ],
            'qr_discount' => [
                'path' => [
                    'loanTotal.qr_discount',
                ],
            ],
            'acra_loans_monthly_repayments' => [
                'path' => [
                    'acra.PARTICIPIENT.Loans.Loan' => function ($loans) {
                        return $this->acraLoansMonthlyRepayments($loans);
                    },
                ],
                'visible' => true,
            ],
            'referral_code' => [
                'path' => [
                    'referral_code',
                ],
            ],
            'selected_loan_type_id' => [
                'path' => [
                    'selected_loan_type_id',
                ],
            ],
            'loan_subtype_id' => [
                'path' => [
                    'loan_subtype_id',
                ],
                'visible' => true,
            ],
        ];
    }

    private function getRegionAndCommunityFromEkeng($source)
    {
        $passport_data = $source['ekeng']['passport_data'] ?? [];

        $registration = $passport_data->AVVRegistrationAddress ?? [];

        $region = $registration->Region ?? '';
        $community = $registration->Community ?? '';
        $residence = $registration->Residence ?? '';

        return [
            $region,
            ($residence) ? $residence : $community,
        ];
    }

    private function getRegion($region, $community)
    {
        if ($region == constants('NKR')) {
            // Check if there are parentheses in community name and get text between.
            if (strpos($community, '(')) {
                $text_between_parentheses = '/\((.*)\)/';
                preg_match($text_between_parentheses, $community, $match);

                return $match[1];
            } else {
                return $community;
            }
        }

        return $region;
    }

    private function getMaritalActs($marriages)
    {
        return filter($marriages, function ($value) {
            return $value['type'] === 'marriage' || $value['type'] === 'divorce';
        });
    }

    private function getMaritalStatus($marriages)
    {
        $sorted = sort($marriages, function ($prev, $curr) {
            if (Carbon::parse($prev['cert_date']) === Carbon::parse($curr['cert_date'])) {
                return 0;
            }

            return (Carbon::parse($prev['cert_date'])->gt(Carbon::parse($curr['cert_date']))) ? -1 : 1;
        });
        $maritalStatus = first($sorted);

        return $maritalStatus['type'] === 'marriage' ? $maritalStatus : null;
    }

    private function getSpouse($source)
    {
        // Convert stdClass object to array
        $marriages = json_decode(json_encode($source), true);
        $marriages = $this->getMaritalActs($marriages);
        $marriage = $this->getMaritalStatus($marriages);

        if ($marriage === null) {
            return null;
        }

        return [
            'registered' => $marriage['cert_date'] ?? '',
            'person_passport' => $marriage['person']['id_number'] ?? '',
            'person_soc_card' => $marriage['person']['psn'] ?? '',
            'person' => $marriage['person']['base_info']['name'] ?? '',
            'person2_passport' => $marriage['person2']['id_number'] ?? '',
            'person2_soc_card' => $marriage['person2']['psn'] ?? '',
            'person2' => $marriage['person2']['base_info']['name'] ?? '',
        ];
    }
}
