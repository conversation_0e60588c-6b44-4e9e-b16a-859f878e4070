<?php

namespace App\Schemas;

class BusinessInfoSchema extends BaseSchema
{
    private $prefix = 'acra.PARTICIPIENT.TaxServiceInfo.Response';

    public function get()
    {
        return [
            'org_name' => [
                'path' => [
                    $this->prefix.'.OrganizationName',
                ],
            ],
            'org_status' => [
                'path' => [
                    $this->prefix.'.OrgStatus',
                ],
            ],
            'reg_date' => [
                'path' => [
                    $this->prefix.'.RegistrationDate',
                ],
                'formatter' => function ($value) {
                    return $this->orgRegDate($value);
                },
            ],
            'tax_identifier' => [
                'path' => [
                    $this->prefix.'.TIN',
                ],
            ],
            'address' => [
                'path' => [
                    function ($source) {
                        return $this->resolveBusinessJurAddress($source);
                    },
                ],
            ],
            'apartment' => [
                'path' => [
                    $this->prefix.'.OrgJurLocation.Apartment',
                ],
            ],
            'region' => [
                'path' => [
                    $this->prefix.'.OrgJurLocation.Region',
                ],
            ],
            'community' => [
                'path' => [
                    $this->prefix.'.OrgJurLocation.Community',
                ],
            ],
            'location' => [
                'path' => [
                    $this->prefix.'.OrgJurLocation.Location',
                ],
            ],
            'city' => [
                'path' => [
                    function ($source) {
                        [$region, $community] = $this->getRegionAndCommunityJurFromAcra($source);

                        return $this->getCity($region, $community);
                    },
                ],
            ],
        ];
    }

    private function getRegionAndCommunityJurFromAcra($source)
    {
        $jur_region = $source['acra']['PARTICIPIENT']['TaxServiceInfo']['Response']['OrgJurLocation']['Region'];
        $jur_community = $source['acra']['PARTICIPIENT']['TaxServiceInfo']['Response']['OrgJurLocation']['Community'];

        return [$jur_region, $jur_community];
    }
}
