<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CreditCard extends Model
{
    use SoftDeletes;

    const VISA = 'VISA';
    const MASTERCARD = 'MASTERCARD';
    const OTHER = 'OTHER';


    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'client_id',
        'binding_id',
        'pan',
        'type',
        'is_primary',
        'expiration_date',
        'name',
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'expiration_date',
    ];


    public function user()
    {
        return $this->belongsTo('App\Models\User');
    }
}
