<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Tag extends Model
{
    // Tag types
    const REJECT = 'REJECT';
    const APPROVE = 'APPROVE';

    protected $fillable = [
        'type',
        'name',
        'description',
    ];

    /**
     * Tags that belong to the loans.
     */
    public function loans()
    {
        return $this->belongsToMany('App\Models\Loan');
    }
}
