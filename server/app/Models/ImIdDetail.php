<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ImIdDetail extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'im_id_details';

    /**
     * Status constants.
     */
    public const PENDING = 'PENDING';
    public const VERIFIED = 'VERIFIED';
    public const EXPIRED = 'EXPIRED';
    public const FAILED = 'FAILED';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'ssn',
        'session_id',
        'notification_token',
        'phone_number',
        'status',
        'expiration',
        'loan_security_id',
    ];

    protected $dates = ['expiration'];

    protected $attributes = [
        'status' => self::PENDING,
    ];

    public static function getValidDetail($session_id)
    {
        return self::where('session_id', $session_id)
            ->where('expiration', '>', now())
            ->first();
    }

    public function loan_security()
    {
        return $this->belongsTo(LoanSecurity::class);
    }
}
