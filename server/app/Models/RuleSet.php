<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class RuleSet extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'version', 'hash', 'loan_type_id', 'start_date', 'end_date',
    ];

    public function rules()
    {
        return $this->hasMany('App\Models\Rule');
    }

    public static function getCurrent($loan_type_id)
    {
        return self::where('loan_type_id', $loan_type_id)->whereNull('end_date')->first();
    }

    public static function createNewVersion($hash, $loan_type_id)
    {
        $rule_set = self::getCurrent($loan_type_id);

        $new_version = 1;
        $now = Carbon::now();

        if($rule_set !== null) {
            $rule_set->update(['end_date' => $now]);

            $new_version = ++$rule_set->version;
        }

        return self::create([
            'loan_type_id' => $loan_type_id,
            'version' => $new_version,
            'hash' => $hash,
            'start_date' => $now,
        ]);
    }
}
