<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VehicleSeller extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'vehicle_id',
        'first_name',
        'last_name',
        'middle_name',
        'passport_number',
        'given_date',
        'from',
        'region',
        'city',
        'street',
        'building',
        'apartment',
    ];

    public function vehicle()
    {
        return $this->belongsTo('App\Models\Vehicle');
    }

    public function spouse()
    {
        return $this->hasOne('App\Models\SellerSpouse');
    }
}
