<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class BlacklistReason extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'title_hy',
        'type',
    ];

    public function scopeUnblockReasons(Builder $query): Builder
    {
        return $query->where('type', constants('BLACKLIST_TYPES.UNBLOCK'));
    }

    public function scopeBlockReasons(Builder $query): Builder
    {
        return $query->where('type', constants('BLACKLIST_TYPES.BLOCK'));
    }
}
