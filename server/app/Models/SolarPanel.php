<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SolarPanel extends Model
{
    const NOT_PRESENTED = 'NOT_PRESENTED';

    const PRESENTED = 'PRESENTED';

    const REFINANCED = 'REFINANCED';

    const NOT_REFINANCED = 'NOT_REFINANCED';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'loan_id',
        'seller_id',
        'solar_panel_type_id',
        'region_id',
        'village_id',
        'address',
        'term_id',
        'state',
        'expiration_date',
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'expiration_date',
    ];

    protected $attributes = [
        'state' => self::NOT_PRESENTED,
    ];

    public function loan()
    {
        return $this->belongsTo('App\Models\Loan');
    }

    public function solar_panel_type()
    {
        return $this->belongsTo('App\Models\SolarPanelType');
    }

    public function region()
    {
        return $this->belongsTo('App\Models\Region');
    }

    public function village()
    {
        return $this->belongsTo('App\Models\Village');
    }

    public function arpi_solar_media()
    {
        return $this->hasMany('App\Models\ArpiSolarMedia');
    }

    public function evaluation_report()
    {
        return $this->hasOne('App\Models\EvaluationReport');
    }

    public function agent()
    {
        return $this->belongsTo('App\Models\User', 'seller_id');
    }

    public function term()
    {
        return $this->hasOne('App\Models\ArpiSolarTerm', 'id', 'term_id');
    }

    public function isKFW()
    {
        if ($this->term) {
            return $this->term->isKfw();
        }

        return false;
    }

    public function getFullAddressAttribute()
    {
        if (isset($this->region->name) && isset($this->village->name)) {
            return $this->region->name.', '.$this->village->name.', '.$this->address;
        }

        return $this->address;
    }
}
