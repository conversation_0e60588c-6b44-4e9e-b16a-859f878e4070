<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VehicleMedia extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'mortgage_id',
        'path',
        'type',
    ];

    protected $appends = ['full_path'];

    /**
     * Get vehicle model vehicles.
     */
    public function mortgage()
    {
        return $this->belongsTo('App\Models\Mortgage');
    }

    public function getPathAttribute($value)
    {
        return env('VEHICLE_MEDIA_SUBDIR', '').$value;
    }

    public function getFullPathAttribute()
    {
        return env('AWS_MORTGAGE_MEDIA_URL').$this->path;
    }
}
