<?php

namespace App\Models;

use App\Helpers\NumberHelper;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class Blacklist extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'social_card_number',
        'vehicle_vin',
        'credit_card',
        'phone_number',
        'email',
        'loan_type_id',
        'expiration',
        'unblock_reason_id',
        'block_reason_id',
        'unblock_note',
        'block_note',
    ];

    protected $dates = [
        'expiration',
    ];

    public function unblock_reason()
    {
        return $this->belongsTo('App\Models\BlacklistReason', 'unblock_reason_id');
    }

    public function block_reason()
    {
        return $this->belongsTo('App\Models\BlacklistReason', 'block_reason_id');
    }

    public static function isRecordExist($ssn, $phone_number, $vin, $credit_card, $loan_type_id)
    {
        $phone_number = NumberHelper::phoneMask($phone_number);

        return self::where(function ($q) use ($ssn, $phone_number, $vin, $credit_card) {
            $q->where(function ($q) use ($ssn) {
                $q->whereNotNull('social_card_number')->where('social_card_number', $ssn);
            })->orWhere(function ($q) use ($phone_number) {
                $q->whereNotNull('phone_number')->where('phone_number', $phone_number);
            })->orWhere(function ($q) use ($vin) {
                $q->whereNotNull('vehicle_vin')->where('vehicle_vin', $vin);
            })->orWhere(function ($q) use ($credit_card) {
                $q->whereNotNull('credit_card')->where('credit_card', $credit_card);
            });
        })->where(function ($q) use ($loan_type_id) {
            $q->whereIn('loan_type_id', self::getLoanTypes($loan_type_id))->orWhereNull('loan_type_id');
        })
        ->where(function ($q) {
            $q->whereNull('expiration')->orWhere('expiration', '>', Carbon::now());
        })->exists();
    }

    public function setPhoneNumberAttribute($value)
    {
        $this->attributes['phone_number'] = NumberHelper::phoneMask($value);
    }

    public function isUnblocked()
    {
        return !is_null($this->expiration) && $this->expiration->lessThan(Carbon::now());
    }

    protected static function getLoanTypes($loan_type_id)
    {
        switch ($loan_type_id) {
            case constants('LOAN_TYPES.OCL'):
            case constants('LOAN_TYPES.OVL'):
            case constants('LOAN_TYPES.COMMON'):
                return [
                    constants('LOAN_TYPES.OCL'),
                    constants('LOAN_TYPES.OVL'),
                    constants('LOAN_TYPES.COMMON'),
                ];
            case constants('LOAN_TYPES.OIDL'):
            case constants('LOAN_TYPES.OTCL'):
            case constants('LOAN_TYPES.OEPL'):
            case constants('LOAN_TYPES.OUPL'):
            case constants('LOAN_TYPES.OIWL'):
            case constants('LOAN_TYPES.OFSL'):
            return [
                    constants('LOAN_TYPES.OIDL'),
                    constants('LOAN_TYPES.OTCL'),
                    constants('LOAN_TYPES.OEPL'),
                    constants('LOAN_TYPES.OUPL'),
                    constants('LOAN_TYPES.OIWL'),
                    constants('LOAN_TYPES.OFSL'),
            ];
            case constants('LOAN_TYPES.OIQL'):
            case constants('LOAN_TYPES.OASL'):
            case constants('LOAN_TYPES.PL'):
            case constants('LOAN_TYPES.REML'):
            case constants('LOAN_TYPES.VLX'):
            case constants('LOAN_TYPES.BNPL'):
            case constants('LOAN_TYPES.OBL'):
                return [$loan_type_id];
        }
    }
}
