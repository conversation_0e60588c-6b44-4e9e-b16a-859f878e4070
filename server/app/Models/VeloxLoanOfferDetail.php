<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VeloxLoanOfferDetail extends Model
{
    // awaiting approval
    const PENDING = 'PENDING';
    // loan offer declined
    const DECLINED = 'DECLINED';
    // loan approved
    const APPROVED = 'APPROVED';
    // loan offer not found
    const NOT_FOUND = 'NOT_FOUND';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'loan_security_id',
        'contract_number',
        'credit_code',
    ];

    /**
     * Get the payment's loan.
     */
    public function loan_security()
    {
        return $this->belongsTo('App\Models\LoanSecurity');
    }
}
