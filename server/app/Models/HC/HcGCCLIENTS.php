<?php

namespace App\Models\HC;

class HcGCCLIENTS extends HcModel
{
    protected $table = 'GCCLIENTS';

    const INDIVIDUAL = 21;
    const LEGAL_ENTITY = 22;

    public static function getBySocCard($soc_card, $client_type = self::INDIVIDUAL)
    {
        return self::where('fREGNUM', $soc_card)
            ->where('fJURSTAT', $client_type)
            ->whereNull('fDATECLOSE')
            ->first();
    }

    public static function getLegalEntityBySocCard($soc_card)
    {
        return self::where('fREGNUM', $soc_card)
            ->where('fJURSTAT', self::LEGAL_ENTITY)
            ->whereNull('fDATECLOSE')
            ->first();
    }
}
