<?php

namespace App\Models\HC;

class HcMortVehicle extends HcModel
{
    protected $table = 'MortVehicles';

    protected $dates = [
        'DATE',
        'DATECLOSE',
        'DATEPASS',
        'ESTIMDATE',
    ];

    protected $convertable = ['COLOR'];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'OUTER_CODE',
        'CODE',
        'NUMBER',
        'DATE',
        'DATECLOSE',
        'VEHICLETYPE',
        'STATELICPLATE',
        'CARBRANDNAME',
        'VEHREGCYPHER',
        'PASBY',
        'DATEPASS',
        'BODYTYPE',
        'COLOR',
        'DATEPUBLISH',
        'VIN',
        'BODYNUMBER',
        'NUMBENGINE',
        'POWERENGINE',
        'ESTIMORG',
        'ESTIMDATE',
        'ESTIMACTNUMB',
        'CURRENCY',
        'SUMMA',
        'R1',
        'NOTDATEPASS',
        'LIQ<PERSON><PERSON><PERSON>',
        'COMNOTNUMBER',
        'NOTPASBY',
    ];
}
