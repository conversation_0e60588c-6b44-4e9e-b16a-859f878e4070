<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\MorphPivot;

class Paymentable extends MorphPivot
{
    public $incrementing = true;

    public $table = 'paymentables';

    protected $fillable = [
        'loan_id',
        'paymentable_type',
        'paymentable_id',
        'failure_reason',
    ];

    // Payment pending
    const PAYMENT_PENDING = 'PAYMENT_PENDING';

    // Payment processing
    const PAYMENT_PROCESSING = 'PAYMENT_PROCESSING';

    // Payment failed
    const PAYMENT_FAILED = 'PAYMENT_FAILED';

    // Payment processed
    const PAYMENT_PROCESSED = 'PAYMENT_PROCESSED';

    // Payment expired
    const PAYMENT_EXPIRED = 'PAYMENT_EXPIRED';

    const FAILURE_REASONS = ['INSUFFICIENT_BALANCE' => 'INSUFFICIENT_BALANCE'];

    const PAYMENTABLE_TYPES = [
        'App\Models\CashPayment' => 'cashPayment',
        'App\Models\CardToCardPayment' => 'cardToCardPayment',
        'App\Models\IdramWalletPayment' => 'idramWalletPayment',
        'App\Models\WalletLoanPayment' => 'walletLoanPayment',
        'App\Models\WirePayment' => 'wirePayment',
        'App\Models\EasypayWalletPayment' => 'easypayWalletPayment',
        'App\Models\ProductProvision' => 'productProvision',
        'App\Models\VeloxPayment' => 'veloxPayment',
        'App\Models\ManualTransfer' => 'manualTransfer',
    ];

    const PAYMENTABLE_TYPES_WITH_ALIAS = [
        'cp' => 'App\Models\CashPayment',
        'ctc' => 'App\Models\CardToCardPayment',
        'iwp' => 'App\Models\IdramWalletPayment',
        'ewp' => 'App\Models\EasypayWalletPayment',
        'wp' => 'App\Models\WirePayment',
        'pp' => 'App\Models\ProductProvision',
        'wlp' => 'App\Models\WalletLoanPayment',
        'vp' => 'App\Models\VeloxPayment',
        'p2p' => 'App\Models\ManualTransfer',
    ];

    public static function setFailureReason($loan)
    {
        $payment_id = $loan->payment->id;

        return $loan->payments()->where('paymentable_id', $payment_id)
            ->update(['failure_reason' => self::FAILURE_REASONS['INSUFFICIENT_BALANCE']]);
    }

    public function paymentable()
    {
        return $this->morphTo();
    }
}
