<?php

namespace App\Models;

use App\Models\HC\HcGCCLIENTS;
use Illuminate\Database\Eloquent\Model;

class HcClientDefault extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'client_type',
        'key',
        'value',
    ];

    public static function getClientDefaults($client_type = HcGCCLIENTS::INDIVIDUAL)
    {
        return self::where('client_type', $client_type)
            ->get()
            ->reduce(function ($carry, $default) {
                $key = $default['key'];
                $carry[$key] = $default['value'];

                return $carry;
            }, []);
    }
}
