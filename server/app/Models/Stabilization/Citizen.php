<?php

namespace App\Models\Stabilization;

use App\Models\Citizen as CashMeCitizen;

class Citizen extends CashMeCitizen
{
    protected $connection = 'pgsql_stabilization_loan';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'loan_id',
        'first_name',
        'last_name',
        'middle_name',
        'first_name_en',
        'last_name_en',
        'middle_name_en',
        'gender',
        'phone_number',
        'additional_phone_number',
        'email',
        'citizenship',
        'region',
        'city',
        'location_code',
        'street',
        'building',
        'apartment',
        'district',
        'birth_date',
        'salary',
        'estimated_income',
        'outer_id',
    ];

    public function loan()
    {
        return $this->belongsTo('App\Models\Stabilization\Loan');
    }

    public function getPrimaryDocument()
    {
        return $this->passports->where('primary', true)->first();
    }
}
