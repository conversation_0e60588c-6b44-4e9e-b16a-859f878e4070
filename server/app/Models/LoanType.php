<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LoanType extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'disabled',
        'description',
    ];

    public static function withLoanConfigs($type_id)
    {
        return LoanType::with('loan_configs')->find($type_id);
    }

    /**
     * Loans that belong to the type.
     */
    public function loans()
    {
        return $this->hasMany('App\Models\Loan');
    }

    /**
     * Loan configs that belong to the type.
     */
    public function loan_configs()
    {
        return $this->hasMany('App\Models\LoanConfig');
    }

    /**
     * Default armsoft values that belong to the type.
     */
    public function hc_defaults()
    {
        return $this->hasMany('App\Models\HcDefault');
    }

    /**
     * Transfer types that belong to the loan type.
     */
    public function transfer_types()
    {
        return $this->belongsToMany('App\Models\TransferType');
    }

    /**
     * Pallaton products that belong to the loan type.
     */
    public function products()
    {
        return $this->belongsToMany('App\Models\Pallaton\Product');
    }

    /**
     * Cash office types that belong to the loan type.
     */
    public function cash_office_types()
    {
        return $this->belongsToMany('App\Models\CashOfficeType');
    }

    /**
     * The rules that belong to the loan type.
     */
    public function rules()
    {
        return $this->belongsToMany('App\Models\Rule');
    }

    public static function getAvailableTypes($product_id)
    {
        return self::whereHas('products', function ($q) use ($product_id) {
            $q->where('id', $product_id)
              ->where('loan_type_product.disabled', false);
        })->select('id', 'name')->get();
    }
}
