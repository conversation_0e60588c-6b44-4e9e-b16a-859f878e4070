<?php

namespace App\Models;

use App\Helpers\NumberHelper;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class LoanSecurity extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'loan_id',
        'document_number',
        'selected_loan_type_id',
        'loan_type_id',
        'term_id',
        'solar_panel_type_id',
        'pipe_type_id',
        'suuid',
        'suuid_exp',
        'identity_verification_token',
        'identity_verification_token_sent',
        'identity_verification_token_exp',
        'phone_number',
        'email',
        'ssn',
        'vin',
        'verification_code',
        'verification_code_sent',
        'verification_code_exp',
        'verification_code_recv',
        'identity_verification_code',
        'identity_verification_code_sent',
        'identity_verification_code_exp',
        'identity_verification_code_recv',
        'email_verification_code',
        'email_verification_code_sent',
        'email_verification_code_exp',
        'email_verification_code_recv',
        'email_verification_code_attempts',
        'get_email_verification_code_attempts',
        'sms_verification_attempts',
        'security_answer_attempts',
        'get_verification_code_attempts',
        'get_identity_verification_code_attempts',
        'is_offline',
        'owl_token',
        'payment_id',
        'qr_token',
        'referral_code',
        're_token',
        'loan_subtype_id',
        'user_id',
        'type',
        'referrer_source',
        'requested_loan_amount',
        'requested_type',
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'verification_code_sent',
        'verification_code_exp',
        'verification_code_recv',
        'identity_verification_token_sent',
        'identity_verification_token_exp',
        'identity_verification_code_sent',
        'identity_verification_code_exp',
        'identity_verification_code_recv',
        'email_verification_code_sent',
        'email_verification_code_exp',
        'email_verification_code_recv',
    ];

    public function loan()
    {
        return $this->belongsTo('App\Models\Loan');
    }

    public function loan_history()
    {
        return $this->hasOne('App\Models\LoanHistory');
    }

    public function user()
    {
        return $this->belongsTo('App\Models\User');
    }

    public function solar_panel_type()
    {
        return $this->belongsTo('App\Models\SolarPanelType');
    }

    public function arpi_solar_term()
    {
        return $this->belongsTo('App\Models\ArpiSolarTerm', 'term_id');
    }

    public function security_metas()
    {
        return $this->hasMany('App\Models\LoanSecurityMeta');
    }

    public function citizen_face_recognitions()
    {
        return $this->hasMany('App\Models\CitizenFaceRecognition');
    }

    public function video_archives()
    {
        return $this->hasMany('App\Models\VideoArchive');
    }

    public function loan_disbursements()
    {
        return $this->hasMany('App\Models\LoanDisbursement')->latest();
    }

    public function security_answers()
    {
        return $this->hasMany('App\Models\SecurityAnswer');
    }

    public function credit_offers()
    {
        return $this->hasMany('App\Models\CreditOffer');
    }

    public function preapproved_credit_offers()
    {
        return $this->hasMany('App\Models\PreapprovedCreditOffer')->latest();
    }

    public function loan_application_order()
    {
        return $this->hasOne('App\Models\LoanApplicationOrder');
    }

    public function dr_predicitions()
    {
        return $this->hasMany('App\Models\DataRobotPrediction', 'application_id');
    }

    public function preapproved_credit_offers_top_up()
    {
        return $this->preapproved_credit_offers()
            ->whereHas('preapproved_credit_offer_type', function ($q) {
                $q->where('name', PreapprovedCreditOfferType::TOP_UP_LOAN_APPLICATION);
                $q->orWhere('name', PreapprovedCreditOfferType::TOP_UP_PERIODIC_OFFER);
            });
    }

    public function fingerprints()
    {
        return $this->belongsToMany('App\Models\Fingerprint', 'fingerprint_loan_security')->withTimestamps();
    }

    public function preapproved_credit_offers_pivot()
    {
        return $this->belongsToMany('App\Models\PreapprovedCreditOffer', 'loan_security_preapproved_credit_offer')->withTimestamps();
    }

    public function storeMeta($meta, $step, $isSuccess)
    {
        $step = $step ?? constants('META_STEPS.COMMON');

        return $this->security_metas()->create([
            'step' => $step,
            'success' => $isSuccess,
            'content' => json_encode(array_merge($meta, [
                'timestamp' => Carbon::now()->timestamp,
            ]), JSON_UNESCAPED_UNICODE),
        ]);
    }

    public function setPhoneNumberAttribute($value)
    {
        $this->attributes['phone_number'] = NumberHelper::phoneMask($value);
    }

    public function setEmailAttribute($value)
    {
        $this->attributes['email'] = $value ? strtolower($value) : null;
    }

    public function selected_loan_type()
    {
        return $this->belongsTo('App\Models\LoanType', 'selected_loan_type_id');
    }

    public function real_estate_details()
    {
        return $this->hasOne('App\Models\RealEstateDetail');
    }

    public function setReferrerSourceAttribute($value)
    {
        $this->attributes['referrer_source'] = $value ? strtoupper($value) : $value;
    }

    public static function getRegistrationInstance($ssn)
    {
        return self::where('ssn', $ssn)
            ->where('type', constants('SECURITY_ENTRY.REGISTRATION'))
            ->latest()
            ->first();
    }

    public static function getLoanSecurityByToken($token)
    {
        return LoanSecurity::where('re_token', $token)->where('suuid_exp', '>', now())->first();
    }

    public function purchase_orders()
    {
        return $this->hasMany('App\Models\CreditLine\PurchaseOrder');
    }

    public function velox_details()
    {
        return $this->hasMany('App\Models\VeloxDetail');
    }

    public function velox_loan_offer_detail()
    {
        return $this->hasOne('App\Models\VeloxLoanOfferDetail');
    }

    public function detectOVLLoanSubType($tech_passport)
    {
        if ($this->loan_type_id === constants('LOAN_TYPES.OVL') && $this->loan_subtype_id !== constants('LOAN_SUBTYPES.OVIL')) {
            $this->update([
                'loan_subtype_id' => $tech_passport ? constants('LOAN_SUBTYPES.OVTL') : constants('LOAN_SUBTYPES.OVPL'),
            ]);
        }
    }
}
