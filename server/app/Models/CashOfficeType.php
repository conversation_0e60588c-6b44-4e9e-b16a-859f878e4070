<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CashOfficeType extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'disabled',
        'name_hy',
        'phone_number',
    ];

    /**
     * Loan types that belong to the cash office type.
     */
    public function loan_types()
    {
        return $this->belongsToMany('App\Models\LoanType');
    }

    /**
     * Cash offices that belong to the office type.
     */
    public function offices()
    {
        return $this->hasMany('App\Models\Office');
    }

    public function getPhoneNumberAttribute($value)
    {
        if ($value !== null) {
            return substr($value, 0, 3).' '.substr($value, 3);
        }

        return $value;
    }
}
