<?php

namespace App\Models\Pallaton;

use Illuminate\Database\Eloquent\Model;

class Profile extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'first_name',
        'last_name',
        'middle_name',
        'birth_date',
        'email',
        'phone_number',
        'additional_phone_number',
        'document_number',
        'ssn',
        'address',
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'birth_date',
    ];

    public function setEmailAttribute($value)
    {
        $this->attributes['email'] = $value ? strtolower($value) : null;
    }

    public function user()
    {
        return $this->belongsTo('App\Models\User');
    }

    public static function isCitizenProfileExists($ssn, $phone_number = null, $email = null)
    {
        return self::where('ssn', $ssn)
            ->orWhere('phone_number', $phone_number)
            ->orWhere('email', $email)
            ->exists();
    }

    public static function getProfileBySsn($ssn)
    {
        return self::where('ssn', $ssn)
            ->first();
    }
}
