<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CardToCardPayment extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'card_number',
        'embossed_name',
        'year',
        'month',
        'partner_identificator',
        'paid',
        'withdrawn',
        'withdraw_operator_id',
        'bank',
        'payment_status',
    ];

    protected $dates = [
       'paid',
       'withdrawn',
    ];

    /**
     * Get the payment's loan.
     */
    public function loan()
    {
        return $this->morphToMany(Loan::class, 'paymentable')->withTimestamps();
    }
}
