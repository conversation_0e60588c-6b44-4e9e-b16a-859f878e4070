<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PredefinedRealEstate extends Model
{
    use SoftDeletes;

    const FREE = 'FREE';
    const RESERVED = 'RESERVED';
    const SOLD = 'SOLD';
    const DISABLED = 'DISABLED';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'token',
        'price',
        'address',
        'region_id',
        'developer_company_id',
        'status',
        'disabled',
        'apartment_id',
        'apartment_number',
        'area',
        'building',
        'building_deadline',
        'room_count',
        'floor',
    ];

    /**
     * @var string[]
     */
    protected $dates = ['deleted_at', 'building_deadline'];
}
