<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SettingsStatus extends Model
{
    public const GENERAL_SECTION_TYPE = 1;

    public const WEB = 'WEB';
    public const APP = 'APP';
    public const TOP_UP = 'TOP_UP';
    public const FACE_RECOGNITION = 'FACE_RECOGNITION';
    public const GET_EKENG_DATA_FROM_DB = 'GET_EKENG_DATA_FROM_DB';
    public const IM_ID = 'IM_ID';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'disabled',
        'description',
    ];
}
