<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PreapprovedContext extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'preapproved_credit_offer_id',
        'name',
        'value',
    ];

    public function preapproved_credit_offer()
    {
        return $this->belongsTo('App\Models\PreapprovedCreditOffer');
    }
}
