<?php

namespace App\Http\Middleware;

use App\Interfaces\CreditLine\ICreditLineException;
use App\Models\CreditLine\PurchaseSession;
use App\Models\LoanSecurity;
use Closure;

class PlFailureCallback
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $response = $next($request);

        if ($response->exception instanceof ICreditLineException) {
            // TODO: for consistency change this to pass payment_id with
            // each request, since we don't have suuid in payment screen
            $payment_id = $this->resolvePaymentId($request) ?? null;
            $public_code = $response->exception->public_code;

            $url = $this->getFailureCallback($payment_id, $public_code);

            return response()->json(['callback' => $url]);
        }

        return $response;
    }

    protected function resolvePaymentId($request)
    {
        $suuid = $request->header('suuid');
        $payment_id = $request->get('payment_id');
        $purchase_session = PurchaseSession::where('payment_id', $payment_id)->first();
        $payment_id = $purchase_session ? $payment_id : null;

        if (!$payment_id) {
            $loan_security = LoanSecurity::where('suuid', $suuid)->first();

            $payment_id = $loan_security->payment_id ?? null;
        }

        return $payment_id ?? null;
    }

    protected function getFailureCallback($payment_id, $public_code)
    {
        $purchase_service = resolve('App\Services\CreditLine\PurchaseServicePL');
        $purchase_session = $purchase_service->getPurchaseSession($payment_id);

        return $purchase_service->composeFailureCallback($purchase_session, $public_code);
    }
}
