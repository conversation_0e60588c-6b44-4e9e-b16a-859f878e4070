<?php

namespace App\Http\Middleware;

use Closure;

class OIDLDown
{
    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $settings_service = resolve('App\Interfaces\ISettingsService');

        if ($settings_service->isLoanTypeDisabled(constants('LOAN_TYPES.OIDL'))) {
            return redirect(route('oidl_down'));
        }

        return $next($request);
    }
}
