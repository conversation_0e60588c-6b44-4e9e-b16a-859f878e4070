<?php

namespace App\Http\Middleware;

use App\Exceptions\BlacklistedException;
use App\Exceptions\CreditLine\DeclinedPurchaseException;
use App\Exceptions\CreditLine\DuplicateOrderIdException;
use App\Exceptions\CreditLine\ZeroBalanceException;
use App\Exceptions\InternalErrorException;
use App\Interfaces\IInternalRuleException;
use Closure;
use Exception;
use Log;

class CheckAvailabilityBNPL
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $credit_line_service = resolve('App\Interfaces\CreditLine\ICreditLineService');
        $purchase_service = resolve('App\Interfaces\CreditLine\IPurchaseService');
        $security_service = resolve('App\Interfaces\ISecurityService');
        $citizen_service = resolve('App\Interfaces\CreditLine\ICitizenServiceBNPL');

        $loan_security = $security_service->resolveLoanSecurity();

        try {
            if ($loan_security) {
                Log::debug('Starting BNPL purchase availability checks');

                $payload = $request->only([
                    'order_id',
                    'amount',
                    'market_name',
                ]);

                $credit_line_service->checkIsCitizenBlocked($loan_security);
                $credit_line_service->checkMerchant($payload['market_name']);

                $loan = $credit_line_service->resolveCredit();

                $payload = array_merge($payload, [
                    'loan_id' => $loan->id,
                ]);
                $purchase_session = $purchase_service->createSession($payload);

                $credit_line_service->storeDetails($purchase_session, $payload);

                $citizen_service->runInternalCreditChecks($loan);

                $credit_line_service->processLoanLimitUpdate($loan_security);

                $purchase_service->adjustPurchaseAmount($loan_security->loan, $purchase_session);

                $request->merge(['payment_id' => $purchase_session->payment_id]);
            }
        } catch (ZeroBalanceException $e) {
            $purchase_service->rejectSession($purchase_session);

            $failure_response = $this->composeZeroBalanceResponse($purchase_session);

            return response()->json($failure_response);
        } catch (DuplicateOrderIdException $e) {
            Log::warning('BNPL CheckAvailability, DuplicateOrderIdException', ['error' => $e->getMessage()]);

            throw $e;
        } catch (IInternalRuleException | BlacklistedException $e) {
            Log::warning('BNPL CheckAvailability, '.get_class($e), ['error' => $e->getMessage()]);

            $purchase_service->rejectSession($purchase_session);

            throw new DeclinedPurchaseException();
        } catch (Exception $e) {
            Log::error('BNPL CheckAvailability, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw new InternalErrorException();
        }

        return $next($request);
    }

    private function composeZeroBalanceResponse($purchase_session): array
    {
        return [
            'balance' => 0,
            'purchase' => $purchase_session->amount,
        ];
    }
}
