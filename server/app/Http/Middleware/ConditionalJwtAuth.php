<?php

namespace App\Http\Middleware;

use Closure;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use <PERSON>mon\JWTAuth\Http\Middleware\BaseMiddleware;

class ConditionalJwtAuth extends BaseMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @throws \Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $this->authService = resolve('App\Interfaces\IAuthService');

        $auth = function ($request) {
            $this->authenticate($request);
        };

        if (!$this->authService->hasAccess($request, $auth)) {
            throw new AccessDeniedHttpException();
        }

        return $next($request);
    }
}
