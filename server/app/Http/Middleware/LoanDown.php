<?php

namespace App\Http\Middleware;

use App\Exceptions\LoanDownException;
use App\Helpers\Security;
use App\Services\SecurityUtilityService;
use Closure;

class LoanDown
{
    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $settings_service = resolve('App\Interfaces\ISettingsService');

        $loan_type_id = $request->loan_type_id ?? $request->get('loan_type_id');

        if (!$loan_type_id) {
            $suuid = Security::getSuuid();

            $loan_type_id = SecurityUtilityService::resolveLoanType($suuid);
        }

        if (isset($loan_type_id)) {
            if ($settings_service->isLoanTypeDisabled($loan_type_id)) {
                throw new LoanDownException();
            }
        }

        return $next($request);
    }
}
