<?php

namespace App\Http\Middleware;

use App\Exceptions\AppDownException;
use Closure;
use Log;

class OldMobileVersion
{
    protected $ips = [
        '***********', // Armed IP
    ];

    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (is_dev() || $this->isValidIp($request->ip()) || (bool) $request->header('X-Device-ID')) {
            return $next($request);
        }

        Log::info('Old mobile version', ['headers' => $request->header()]);

        throw new AppDownException();
    }

    protected function isValidIp($ip)
    {
        return in_array($ip, $this->ips);
    }
}
