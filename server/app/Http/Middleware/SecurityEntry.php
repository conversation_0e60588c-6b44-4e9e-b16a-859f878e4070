<?php

namespace App\Http\Middleware;

use App;
use App\Exceptions\InternalRuleViolationVeloxException;
use App\Exceptions\InternalVeloxErrorException;
use App\Exceptions\InvalidSuuidException;
use App\Exceptions\ResourceLockedException;
use App\Exceptions\UnprocessableApplicationException;
use App\Factory\CitizenServiceFactory;
use App\Factory\OWLServiceFactory;
use App\Factory\SecurityServiceFactory;
use App\Helpers\PassportHelper;
use App\Interfaces\IInternalRuleException;
use App\Models\HC\HcGCCRDTCODE;
use App\Models\ImIdDetail;
use App\Models\LoanSecurity;
use App\Models\PreapprovedCreditOffer;
use App\Models\RealEstateDetail;
use App\Models\Region;
use App\Services\SecurityUtilityService;
use Closure;
use Illuminate\Support\Facades\Log;
use Throwable;

class SecurityEntry
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return mixed
     */
    public function handle($request, Closure $next, $type = 'loan_application')
    {
        $ekeng_service = resolve('App\Interfaces\IEkengService');

        $user = auth()->user();
        $suuid = generate_uuid();

        $im_id_session_id = $request->get('im_id_session_id');
        $request_gcd = $request->get('gcd');
        $im_id_detail = null;
        $phone_number = null;
        if ($im_id_session_id && !$request_gcd) {
            $im_id_detail = ImIdDetail::where('session_id', $im_id_session_id)->first();
            $phone_number = $im_id_detail->phone_number;
            $citizen = $ekeng_service->getCitizen(constants('SOC_CARD'), $im_id_detail->ssn);
            $gcd = PassportHelper::getDocumentNumber($citizen['passport_data']);
        }

        $payload = [
            'document_number' => $gcd ?? strtoupper($request_gcd),
            'selected_loan_type_id' => $request->get('selected_loan_type_id'),
            'loan_type_id' => $request->get('loan_type_id') ?? constants('LOAN_TYPES.COMMON'),
            'suuid' => $suuid,
            'suuid_exp' => now()->addDays(1),
            'phone_number' => $phone_number ?? $request->get('phone_number'),
            'email' => $request->get('email') ? $request->get('email') : null,
            'term_id' => $request->get('term_id'),
            'solar_panel_type_id' => $request->get('solar_panel_type_id'),
            'pipe_type_id' => $request->get('pipe_type_id'),
            'owl_token' => $request->route('token'),
            'payment_id' => $request->get('payment_id'),
            'qr_token' => $request->get('qr_token'),
            're_token' => $request->get('re_token'),
            'user_id' => $user->id ?? null,
            'type' => $type,
            'referrer_source' => $request->get('referrer_source'),
            're_type' => $request->get('re_type'),
            'loan_subtype_id' => $request->get('loan_subtype_id') ?? null,
        ];

        if ($payload['loan_type_id'] === constants('LOAN_TYPES.OBL')) {
            $loan_security = LoanSecurity::getLoanSecurityByToken($request->get('token'));

            if (!$loan_security) {
                Log::info('Pay4me, InvalidSuuidExceptiond', ['token' => $request->get('token')]);
                throw new InvalidSuuidException();
            }

            $payload['suuid'] = $loan_security->suuid;
            $payload['re_token'] = $loan_security->re_token;
            $payload['suuid_exp'] = $loan_security->suuid_exp;
        }

        $request->headers->set('suuid', $payload['suuid']);

        // Pallaton user has Internal BNPL check or BNPL check
        if ($loan_security = $this->getExistingBNPLLoanSecurity($payload['loan_type_id'], $user->profile->ssn ?? $request->get('ssn'))) {
            $request->headers->set('suuid', $loan_security->suuid);

            $user_id = $payload['user_id'];
            // If BNPL loan created via non Cashme App, that user may not have user_id attached to loan security;
            // or the user re-registered and the user_id has changed,
            //that's why we need try to associate it
            $this->updateLoanSecurityUserId($loan_security, $user_id);

            $response = $next($request);
            $response->header('suuid', $loan_security->suuid);

            return $response;
        }

        $aggregator_service = resolve('App\Interfaces\IAggregatorService');

        // Pallaton case
        if ($user && $user->profile) {
            // Process periodic top-up offer if available
            $top_up_offer_response = $this->handleTopUpOffer($user, $payload, $request, $next);
            if ($top_up_offer_response) {
                return $top_up_offer_response;
            }

            $payload['document_number'] = $user->profile->document_number;
            $payload['email'] = $user->email;
            $payload['phone_number'] = $user->phone_number;
            $payload['type'] = constants('SECURITY_ENTRY.LOAN_APPLICATION_MOBILE');
        }

        if (SecurityUtilityService::isREML($payload['loan_type_id'])) {
            $payload['loan_subtype_id'] = SecurityUtilityService::getREMLSubtypeId($payload['re_token'], $payload['re_type']);
        }

        if ($payload['loan_type_id'] === constants('LOAN_TYPES.VLX')) {
            return $this->processVLX($request, $next, $payload);
        }

        if (SecurityUtilityService::isOwlLoan($payload['loan_type_id'])) {
            return $this->processOWL($request, $next, $payload);
        }

        if (SecurityUtilityService::isTelcellVehicle($payload['loan_type_id'], $payload['referrer_source'])) {
            return $this->processTelcellVehicle($request, $next, $payload);
        }

        $ekeng_data = $aggregator_service->getEkengData($payload['document_number']);
        $ssn = $ekeng_data['SSN'] ?? null;
        if ($payload['type'] === constants('SECURITY_ENTRY.REGISTRATION')) {
            SecurityUtilityService::isProfileExists($ssn, $payload['phone_number'], $payload['email']);
        }

        $this->checkIsCitizenBlocked($ssn, $payload);

        $this->checkIsCitizenLocked($ssn, $payload);

        // Swap security service instance to make sure suuid is present
        App::instance('App\Interfaces\ISecurityService', SecurityServiceFactory::build($payload['suuid']));

        if ($payload['loan_type_id'] === constants('LOAN_TYPES.OBL')) {
            $loan_security = LoanSecurity::getLoanSecurityByToken($request->get('token'));

            $loan_security->update(array_merge($payload, [
                    'ssn' => $ssn,
                ])
            );
        } else {
            $loan_security = LoanSecurity::create(array_merge($payload, [
                    'ssn' => $ssn,
                ])
            );
        }

        if ($im_id_detail) {
            $im_id_detail->loan_security()->associate($loan_security);
            $im_id_detail->save();
            $loan_security->update([
                'phone_number' => $im_id_detail->phone_number,
            ]);
        }

        if ($payload['type'] !== constants('SECURITY_ENTRY.REGISTRATION')) {
            $citizen_service = CitizenServiceFactory::build($payload['loan_type_id']);

            $passport_numbers_as_array = PassportHelper::getEkengPassportNumbersAsArray($ekeng_data['passport_data'] ?? []);
            $citizen_service->checkInternalCredits($ssn, $payload['loan_type_id'], $passport_numbers_as_array);
        }

        if ($payload['loan_type_id'] === constants('LOAN_TYPES.PL')) {
            SecurityUtilityService::isUserExist($ssn, $payload['phone_number']);
        }

        if (SecurityUtilityService::isREML($payload['loan_type_id']) &&
            ($payload['re_token'] || SecurityUtilityService::isBanali($payload['referrer_source']))
        ) {
            $details = [];

            if ($payload['re_token']) {
                $predefined_real_estate_service = resolve('App\Interfaces\IPredefinedRealEstateService');
                $details = $predefined_real_estate_service->getDetails($payload['re_token']);
            } elseif (SecurityUtilityService::isBanali($payload['referrer_source'])) {
                $details = [
                    'address' => $request->get('re_address'),
                    'price' => $request->get('re_price'),
                    'region_id' => Region::getIdByName($request->get('re_region')),
                ];
            }

            $real_estate_details = new RealEstateDetail($details);
            $real_estate_details->loan_security()->associate($loan_security);
            $real_estate_details->save();
        }

        $security_service = resolve('App\Interfaces\ISecurityService');
        $security_service->recordMetaOnce($ekeng_service->cutEkengPhoto($ekeng_data), constants('META_STEPS.EKENG_DATA'));

        $response = $next($request);
        $response->header('suuid', $payload['suuid']);

        return $response;
    }

    private function getExistingBNPLLoanSecurity($loan_type_id, $ssn)
    {
        if ($loan_type_id === constants('LOAN_TYPES.BNPL')) {
            $loan_security = SecurityUtilityService::getExistingBNPLLoanSecurity($ssn);

            if ($loan_security) {
                return $loan_security;
            }
        }

        return null;
    }

    private function processOWL($request, $next, $payload)
    {
        $owl_service = OWLServiceFactory::build($payload['loan_type_id']);
        $owl_service->checkTokenAvailability($payload['owl_token']);

        LoanSecurity::create($payload);

        // Swap security service instance to make sure suuid is present
        App::instance('App\Interfaces\ISecurityService', SecurityServiceFactory::build($payload['suuid']));

        $response = $next($request);
        $response->header('suuid', $payload['suuid']);

        // Limit the cookie to /api/wallet_key/request path only,
        // since it's used only in wallet loan
        $path = SecurityUtilityService::getWalletKey($payload['loan_type_id']);

        return $response->cookie(
            'suuid_token', encrypt($payload['suuid']), now()->diffInMinutes($payload['suuid_exp']), "/api/$path/request", null, null, false
        );
    }

    protected function processTelcellVehicle($request, $next, $payload)
    {
        LoanSecurity::create($payload);

        $response = $next($request);
        $response->header('suuid', $payload['suuid']);

        return $response;
    }

    protected function checkIsCitizenBlocked($ssn, $payload)
    {
        $is_blocked = SecurityUtilityService::isCitizenBlocked($ssn, $payload['phone_number'], null, null, $payload['loan_type_id']);
        if ($is_blocked) {
            Log::info('SecurityEntry, Citizen Blocked',
                ['ssn' => $ssn, 'phone_number' => $payload['phone_number']]
            );

            if (SecurityUtilityService::isBlockable($payload['loan_type_id'])) {
                throw new UnprocessableApplicationException();
            }
        }
    }

    protected function checkIsCitizenLocked($ssn, $payload)
    {
        $is_locked = SecurityUtilityService::isCitizenLocked($ssn, $payload['phone_number']);
        if ($is_locked) {
            Log::info('SecurityEntry, Parameter Set Locked',
                ['ssn' => $ssn, 'phone_number' => $payload['phone_number']]
            );

            if (SecurityUtilityService::isLockable($payload['loan_type_id'])) {
                throw new UnprocessableApplicationException();
            }
        }
    }

    protected function processVLX($request, $next, $payload)
    {
        try {
            $ssn = $request->get('ekengData')['SSN'];

            $redis_service = resolve('App\Interfaces\IRedisService');
            // We need to lock this request to avoid parallel API calls
            $lock = $redis_service->lock("vlx_loan_offer_$ssn", constants('VELOX_LOAN_OFFER_LOCK_TIMEOUT'));
//            if (!$lock->acquire()) {
//                Log::warning('Trying to access Velox locked resource, ResourceLockedException', ['ssn' => $ssn]);
//                throw new ResourceLockedException();
//            }


            $citizen_service = resolve('App\Interfaces\ICitizenService');
            [
                'requested_amount' => $requested_amount,
                'document_number' => $payload['document_number'],
                'phone_number' => $payload['phone_number'],
            ] = $citizen_service->getVeloxCitizenInfo($request->all());

            $this->checkIsCitizenLocked($ssn, $payload);

            // We need to expire existing(previous) offer before creation new one,
            //to avoid multiple loan offer submission for the same citizen
            $this->expireExistingCreditOffer($ssn);

            LoanSecurity::create(array_merge($payload, [
                    'ssn' => $ssn,
                    'requested_loan_amount' => $requested_amount,
                ])
            );

            $citizen_service->checkInternalCredits($ssn, $payload['loan_type_id'], $payload['document_number']);

            App::instance('App\Interfaces\ISecurityService', SecurityServiceFactory::build($payload['suuid']));
            $security_service = resolve('App\Interfaces\ISecurityService');

//            $security_service->recordMetaOnce([], constants('META_STEPS.IM_ID_REGISTRATION'), true);
//            $security_service->recordMetaOnce([], constants('META_STEPS.CHECK_LIVENESS'), true);
//            $security_service->recordMetaOnce([], constants('META_STEPS.GET_LOAN'), true);

            $response = $next($request);

            $lock->release();

            Log::debug('Velox loan offer processed, releasing lock');

            return $response;
        } catch (ResourceLockedException $e) {
            Log::warning('Process Velox Offer, ResourceLockedException', ['error' => $e->getTraceAsString()]);

            throw $e;
        } catch (IInternalRuleException $e) {
            Log::warning('Process Velox Offer, '.get_class($e), ['error' => $e->getMessage()]);
            optional($lock)->release();
            Log::warning('Releasing locked velox loan offer request coz, '.get_class($e), ['ssn' => $ssn]);

            throw new InternalRuleViolationVeloxException();
        } catch (Throwable $e) {
            Log::error('Process Velox Offer, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            optional($lock)->release();
            Log::warning('Releasing locked velox loan offer request coz Exception', ['ssn' => $ssn]);

            throw new InternalVeloxErrorException();
        }
    }

    public function expireExistingCreditOffer($ssn)
    {
        $now = now();

        LoanSecurity::whereSsn($ssn)
            ->where('suuid_exp', '>', $now)
            ->where('loan_type_id', constants('LOAN_TYPES.VLX'))
            ->update(['suuid_exp' => $now]);
    }

    /**
     * Handle periodic top-up offer logic.
     */
    private function handleTopUpOffer($user, &$payload, $request, $next)
    {
        // Check if valid top-up offer exists
        $top_up_offer = PreapprovedCreditOffer::getValidPeriodicTopUpOffer($user->profile->ssn);

        if ($top_up_offer && in_array($payload['loan_type_id'], [constants('LOAN_TYPES.OCL'), constants('LOAN_TYPES.COMMON')])) {
            $loan_security = $top_up_offer->loan_security;
            $suuid = $loan_security->suuid;

            App::instance('App\Interfaces\ISecurityService', SecurityServiceFactory::build($suuid));

            $top_up_service_ocl = resolve('App\Services\TopUpServiceOCL');
            //We need to make sure loan balance not changed
            //or the loan not closed in HC between the periodic top-up offer creation and user loan application,
            // then expire periodic offer and process flow as we have in standard cases
            if (HcGCCRDTCODE::isCreditClosed($top_up_offer->top_up_offer_detail->contract_number) ||
                $top_up_service_ocl->isTopUpLoanBalanceChanged($loan_security->ssn, $top_up_offer)
            ) {
                PreapprovedCreditOffer::expireExistingTopUpOfferByLoanType($loan_security->loan_type_id, $top_up_offer);
                $loan_security->update(['suuid_exp' => now()]);

                Log::info('Top-up periodic offer and loan security expired during loan application', ['top_up_offer' => $top_up_offer]);

                return null;
            }

            $user_id = $payload['user_id'] ?? null;

            // Update loan security's user_id if necessary
            $this->updateLoanSecurityUserId($loan_security, $user_id);
            // Every time we need to set initial loan_type_id to common to process Common loan type services
            $loan_security->update(['loan_type_id' => constants('LOAN_TYPES.COMMON')]);

            // Update top-up offer details to calculate loan schedule correctly
            $top_up_service_ocl->updateTopUpOfferDetails($loan_security->ssn, $top_up_offer);

            // Set headers and resolve security service
            return $this->processLoanSecurity($request, $next, $suuid);
        }

        return null;
    }

    private function updateLoanSecurityUserId($loan_security, $user_id)
    {
        if (isset($user_id) && (!$loan_security->user_id || $loan_security->user_id != $user_id)) {
            $loan_security->update(['user_id' => $user_id]);
        }
    }

    private function processLoanSecurity($request, $next, $suuid)
    {
        $request->headers->set('suuid', $suuid);

        $response = $next($request);
        $response->header('suuid', $suuid);

        return $response;
    }
}
