<?php

namespace App\Http\Middleware;

use App\Helpers\Security;
use App\Interfaces\ISecurityService;
use Browser;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;

class MetaCollector
{
    public function __construct(ISecurityService $security_service)
    {
        $this->security_service = $security_service;
    }

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @param string $step Current step identifier
     * @param mixed ...$required_steps
     * @return mixed
     */
    public function handle($request, Closure $next, $step, ...$required_steps)
    {
        // Make sure suuid is provided
        if (is_null(Security::getSuuid())) {
            $this->throwAccessDenied();
        }

        // Make sure suuid didn't expire, citizen wasn't blocked
        $loan_security = $this->security_service->resolveLoanSecurity();
        if (is_null($loan_security)) {
            $this->throwAccessDenied();
        }

        // Make sure all required steps were done
        if (count($required_steps) > 0) {
            $completed_steps = $loan_security
                ->security_metas()
                ->distinct()
                ->where('success', true)
                ->pluck('step')
                ->toArray();

            foreach ($required_steps as $requirement) {
                if (!$this->validateStepRequirement($requirement, $completed_steps)) {
                    $this->throwAccessDenied();
                }
            }
        }

        $response = $next($request);

        $status = $response->status();
        $isSuccess = in_array($status, range(200, 399));

        // We should remove 'citizen_image' key from the request(if exists that key) for DB optimization
        $request_data = array_except($request->all(), ['citizen_image']);

        $loan_security->storeMeta([
            'client' => Browser::detect(),
            'request' => [
                'method' => $request->method(),
                'headers' => $request->header(),
                'payload' => $request_data,
                'cookies' => $request->cookie(),
                'ajax' => $request->ajax(),
                'secure' => $request->secure(),
                'url' => $request->fullUrl(),
                'root' => $request->root(),
                'query' => $request->query(),
                'files' => $request->allFiles(),
                'ip' => $request->ip(),
                'server' => $request->server(),
            ],
            'response' => [
                'status' => $status,
                'body' => $response->content(),
            ],
        ], $step, $isSuccess);

        return $response;
    }

    /**
     * Throw access denied exception
     */
    private function throwAccessDenied()
    {
        throw new AccessDeniedHttpException('Access denied', null, config('error_codes.INVALID_SUUID'));
    }

    /**
     * Validate a step requirement (either regular or conditional)
     */
    private function validateStepRequirement($requirement, $completed_steps): bool
    {
        if ($this->isConditionalStep($requirement)) {
            return $this->validateConditionalStep($requirement, $completed_steps);
        }

        return in_array($requirement, $completed_steps);
    }

    /**
     * Check if a step requirement is conditional (contains alternatives)
     */
    private function isConditionalStep($step): bool
    {
        return strpos($step, '|') !== false;
    }

    /**
     * Validate conditional step requirement
     * Format: "step1|step2|step3" means at least one of these steps must be completed
     */
    private function validateConditionalStep($requirement, $completed_steps): bool
    {
        $alternative_steps = explode('|', $requirement);

        foreach ($alternative_steps as $alternative) {
            if (in_array(trim($alternative), $completed_steps)) {
                return true; // At least one conditional step is completed
            }
        }

        return false; // None of the conditional steps are completed
    }
}
