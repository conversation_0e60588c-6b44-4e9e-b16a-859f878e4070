<?php
namespace App\Tools;

use Illuminate\Contracts\Support\Arrayable;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;

// put line to composer.json "require" part- "phpoffice/phpspreadsheet": "^1.6",

class MyReadFilter implements \PhpOffice\PhpSpreadsheet\Reader\IReadFilter {

    protected $fromRow;
    protected $toRow;
    protected $column = null;

    public function __construct($fromRow,$toRow,$column){
        $this->fromRow = $fromRow;
        $this->toRow = $toRow;
        $this->column = $column;
    }

    public function readCell($column, $row, $worksheetName = '') {
        if (($row >= $this->fromRow && $row <= $this->toRow) &&  (($this->column ==null )? true:  $column == $this->column)) {
            return true;
        }
        return false;
    }
}

class Excel implements Arrayable {

    protected $file;
    protected $fromRow;
    protected $toRow;
    protected $column = null;

    public function __construct($file,$fromRow,$toRow,$column){
        $this->file = $file;
        $this->fromRow = $fromRow;
        $this->toRow = $toRow;
        $this->column = $column;
    }

    public function toArray(){
        $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($this->file);
        $reader->setReadFilter(new MyReadFilter($this->fromRow,$this->toRow,$this->column));
        $reader->setReadDataOnly(true);
        $spreadsheet = $reader->load($this->file);

        return $spreadsheet->getActiveSheet()->toArray();
    }
}