<?php

namespace App\Observers;

use App\Models\Pallaton\Profile;
use App\Models\User;

class ProfileObserver
{
    /**
     * Handle the Profile "created" event.
     *
     * @param \App\Profile $profile
     *
     * @return void
     */
    public function created(Profile $profile)
    {
    }

    /**
     * Handle the Profile "updated" event.
     *
     * @param \App\Profile $profile
     *
     * @return void
     */
    public function updated(Profile $profile)
    {
    }

    /**
     * Handle the Profile "deleted" event.
     *
     * @param \App\Profile $Profile
     *
     * @return void
     */
    public function deleted(Profile $profile)
    {
        if (auth()->user()->isAdmin() || auth()->user()->hasPermissionTo('remove-users')) {
            $user = User::find($profile->user_id);
            if (isset($user)) {
                $user->forceDelete();
            }
            $profile->delete();
        }
    }

    /**
     * Handle the Profile "restored" event.
     *
     * @param \App\Profile $profile
     *
     * @return void
     */
    public function restored(Profile $profile)
    {
    }

    /**
     * Handle the Profile "force deleted" event.
     *
     * @param \App\Profile $profile
     *
     * @return void
     */
    public function forceDeleted(Profile $profile)
    {
    }
}
