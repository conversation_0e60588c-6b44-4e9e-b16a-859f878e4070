<?php

namespace App\Observers;

use App\Models\HC\HcLoansCredDisbursements;

class HcLoansCredDisbursementsObserver
{
    protected static $disbursement_reference;

    public static function setDisbursementRef($disbursement_ref)
    {
        self::$disbursement_reference = $disbursement_ref;
    }

    /**
     * Handle the hc loans cred disbursements "created" event.
     *
     * @return void
     */
    public function created(HcLoansCredDisbursements $hc_loans_cred_disbursements)
    {
    }

    /**
     * Handle the hc loans cred disbursements "creating" event.
     *
     * @return void
     */
    public function creating(HcLoansCredDisbursements $hc_loans_cred_disbursements)
    {
        $ref = self::$disbursement_reference;

        if (isset($ref->id)) {
            $comment = $hc_loans_cred_disbursements->COMMENT;

            if (!empty($comment)) {
                $hc_loans_cred_disbursements->COMMENT = "{$comment}, REF:{$ref->id}";
            } else {
                $hc_loans_cred_disbursements->COMMENT = "REF:{$ref->id}";
            }
        }
    }

    /**
     * Handle the hc loans cred disbursements "updated" event.
     *
     * @return void
     */
    public function updated(HcLoansCredDisbursements $hc_loans_cred_disbursements)
    {
    }

    /**
     * Handle the hc loans cred disbursements "deleted" event.
     *
     * @return void
     */
    public function deleted(HcLoansCredDisbursements $hc_loans_cred_disbursements)
    {
    }

    /**
     * Handle the hc loans cred disbursements "restored" event.
     *
     * @return void
     */
    public function restored(HcLoansCredDisbursements $hc_loans_cred_disbursements)
    {
    }

    /**
     * Handle the hc loans cred disbursements "force deleted" event.
     *
     * @return void
     */
    public function forceDeleted(HcLoansCredDisbursements $hc_loans_cred_disbursements)
    {
    }
}
