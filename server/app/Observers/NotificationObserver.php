<?php

namespace App\Observers;

use App\Jobs\Pallaton\SendNotificationReadToNotifier;
use App\Models\Pallaton\Notification;

class NotificationObserver
{
    /**
     * Handle the notification "created" event.
     *
     * @return void
     */
    public function created(Notification $notification)
    {
    }

    /**
     * Handle the notification "updated" event.
     *
     * @return void
     */
    public function updated(Notification $notification)
    {
        if ($notification->isDirty('read_at')) {
            SendNotificationReadToNotifier::dispatch($notification);
        }
    }

    /**
     * Handle the notification "deleted" event.
     *
     * @return void
     */
    public function deleted(Notification $notification)
    {
    }

    /**
     * Handle the notification "restored" event.
     *
     * @return void
     */
    public function restored(Notification $notification)
    {
    }

    /**
     * Handle the notification "force deleted" event.
     *
     * @return void
     */
    public function forceDeleted(Notification $notification)
    {
    }
}
