<?php

namespace App\Strategies\Confirmation;

use App\Factory\LoanServiceFactory;
use App\Jobs\GenerateLawyerDocument;
use App\Jobs\SendConfirmationNotification;
use App\Jobs\SendConfirmationSMS;
use App\Models\Loan;
use App\Models\Pallaton\UserDevice;
use Log;

class DirectStrategy extends BaseConfirmationStrategy
{
    public function finalize()
    {
        $loan_service = LoanServiceFactory::build($this->loan->loan_type_id);

        $status = $loan_service->processLoanPayment($this->loan);

        if ($status !== Loan::FAILED) {
            $loan_service->saveLoanToHC($this->loan);

            Log::debug('Generating lawyer document');
            GenerateLawyerDocument::dispatch($this->loan);
        }

        $this->sendConfirmation();
    }

    private function sendConfirmation()
    {
        $ssn = $this->loan->loan_security->ssn;
        $user_device = UserDevice::where('user_uuid', $ssn)->first();

        if ($user_device) {
            Log::info('Send confirmation notification');
            SendConfirmationNotification::dispatch($ssn, $this->loan);
        } else {
            Log::info('Send confirmation SMS');
            SendConfirmationSMS::dispatch($this->loan);
        }
    }
}
