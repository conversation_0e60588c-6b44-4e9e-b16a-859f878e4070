<?php

namespace App\Strategies\Transfer;

use App\Exceptions\PaymentFailureException;
use App\Factory\ArcaServiceFactory;
use Carbon\Carbon;
use Log;

class CardToCardTransferStrategy extends FoundTransferStrategy
{
    public function __construct($loan, $card_number, $year, $month)
    {
        parent::__construct($loan);

        $this->card_number = $card_number;
        $this->year = $year;
        $this->month = $month;
    }

    public function storeTransfer()
    {
        $paymentService = resolve('App\Interfaces\IPaymentService');

        return $paymentService->createCardToCardPayment(
            $this->loan,
            $this->card_number,
            $this->year,
            $this->month
        );
    }

    public function makeTransfer()
    {
        $bank = $this->loan->payment->bank ?? null;
        if ($bank === null) {
            Log::error('Card To Card Transfer Strategy Make Transfer', ['error' => 'loan payment bank is empty']);

            throw new PaymentFailureException();
        }

        $arca_service = ArcaServiceFactory::build($bank);

        $res = $arca_service->makeTransfer($this->loan, $this->loan->payment->card_number);

        parent::withdrawTransfer();

        return $this->loan->payment->where('id', $this->loan->payment->id)->update([
            'partner_identificator' => $res['partner_identificator'] ?? null,
            'paid' => Carbon::now(),
            'withdrawn' => Carbon::now(),
        ]);
    }
}
