<?php

namespace App\Exceptions;

use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class AwsException extends BadRequestHttpException
{
    public function __construct($verifications = [], $message = 'Can\'t process aws, please try again', $error_code = 400)
    {
        $this->verifications = $verifications;
        parent::__construct($message, null, $error_code);
    }

    public function getVerifications()
    {
        return $this->verifications;
    }
}
