<?php

namespace App\Exceptions;

use App\Interfaces\IInternalRuleException;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;

class PaymentOnApplicationDayException extends AccessDeniedHttpException implements IInternalRuleException
{
    public function __construct($message = 'Citizen has payment on application day')
    {
        parent::__construct($message, null, config('error_codes.PAYMENT_DAY'));
    }
}
