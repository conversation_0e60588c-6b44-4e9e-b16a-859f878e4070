<?php

namespace App\Exceptions\CreditLine;

use App\Interfaces\CreditLine\ICreditLineException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class InvalidPaymentIdException extends BadRequestHttpException implements ICreditLineException
{
    public function __construct($message = 'Invalid payment id')
    {
        parent::__construct($message, null, config('error_codes.CREDIT_LINE.INVALID_PAYMENT_ID'));

        $this->public_code = config('error_codes.CREDIT_LINE.PUBLIC.INVALID_PAYMENT_ID');
    }

    public function getPublicCode()
    {
        return $this->public_code;
    }
}
