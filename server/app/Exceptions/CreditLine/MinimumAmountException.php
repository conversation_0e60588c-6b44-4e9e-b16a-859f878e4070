<?php

namespace App\Exceptions\CreditLine;

use App\Interfaces\CreditLine\ICreditLineException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class MinimumAmountException extends BadRequestHttpException implements ICreditLineException
{
    public function __construct($message = 'Minimum amount is not met')
    {
        parent::__construct($message, null, config('error_codes.CREDIT_LINE.MIN_AMOUNT_NOT_MET'));

        $this->public_code = config('error_codes.CREDIT_LINE.PUBLIC.REJECTED_PAYMENT');
    }

    public function getPublicCode()
    {
        return $this->public_code;
    }
}
