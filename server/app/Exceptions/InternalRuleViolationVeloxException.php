<?php

namespace App\Exceptions;

use App\Interfaces\IInternalRuleException;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;

class InternalRuleViolationVeloxException extends AccessDeniedHttpException implements IInternalRuleException
{
    public function __construct($message = 'Internal rule(s) violation')
    {
        parent::__construct($message, null, config('error_codes.VLX.INTERNAL_RULE_VIOLATION'));
    }
}
