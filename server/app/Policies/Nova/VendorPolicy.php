<?php

namespace App\Policies\Nova;

use App\Models\CreditLine\Vendor;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class VendorPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view the vendor.
     *
     * @param \App\Vendor $vendor
     *
     * @return mixed
     */
    public function view(User $user, Vendor $vendor)
    {
        return $user->isAdmin() || $user->hasPermissionTo('view-vendor');
    }

    /**
     * Determine whether the user can create vendors.
     *
     * @return mixed
     */
    public function create(User $user)
    {
        return $user->isAdmin() || $user->hasPermissionTo('create-vendor');
    }

    /**
     * Determine whether the user can update the vendor.
     *
     * @param \App\Vendor $vendor
     *
     * @return mixed
     */
    public function update(User $user, Vendor $vendor)
    {
        return $user->isAdmin() || $user->hasPermissionTo('create-vendor');
    }

    /**
     * Determine whether the user can delete the vendor.
     *
     * @param \App\Vendor $vendor
     *
     * @return mixed
     */
    public function delete(User $user, Vendor $vendor)
    {
        return false;
    }
}
