<?php

namespace App\Policies\Nova;

use App\Models\LoanHistory;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class LoanUpdateHistoryPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view the loan.
     *
     * @return true
     */
    public function view(User $user, LoanHistory $loan_history)
    {
        return true;
    }

    /**
     * Determine whether the user can create loans.
     *
     * @return mixed
     */
    public function create(User $user)
    {
        return false;
    }

    /**
     * Determine whether the user can update the loan.
     *
     * @return mixed
     */
    public function update(User $user, LoanHistory $loan_history)
    {
        return false;
    }

    /**
     * Determine whether the user can delete the loan.
     *
     * @return mixed
     */
    public function delete(User $user, LoanHistory $loan_history)
    {
        return false;
    }
}
