<?php

namespace App\Policies\Nova;

use App\Models\RealEstateSeller;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class RealEstateSellerPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view the loan.
     *
     *
     * @return bool
     */
    public function view(User $user, RealEstateSeller $seller): bool
    {
        return true;
    }

    /**
     * Determine whether the user can create loans.
     *
     * @return bool
     */
    public function create(User $user): bool
    {
        return false;
    }

    /**
     * Determine whether the user can update the loan.
     *
     *
     * @return bool
     */
    public function update(User $user, RealEstateSeller $seller): bool
    {
        return false;
    }

    /**
     * Determine whether the user can delete the loan.
     **
     * @return bool
     */
    public function delete(User $user, RealEstateSeller $seller): bool
    {
        return false;
    }
}
