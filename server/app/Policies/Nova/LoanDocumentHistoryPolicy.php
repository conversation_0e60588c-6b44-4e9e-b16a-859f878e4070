<?php

namespace App\Policies\Nova;

use App\Models\LoanDocumentHistory;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class LoanDocumentHistoryPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view the loan.
     *
     * @return false
     */
    public function view(User $user, LoanDocumentHistory $loan_document_history)
    {
        return false;
    }

    /**
     * Determine whether the user can create loans.
     *
     * @return mixed
     */
    public function create(User $user)
    {
        return false;
    }

    /**
     * Determine whether the user can update the loan.
     *
     * @return mixed
     */
    public function update(User $user, LoanDocumentHistory $loan_document_history)
    {
        return false;
    }

    /**
     * Determine whether the user can delete the loan.
     *
     * @return mixed
     */
    public function delete(User $user, LoanDocumentHistory $loan_document_history)
    {
        return false;
    }
}
