<?php

namespace App\Policies\Nova;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class QrOwnerPolicy
{
    use HandlesAuthorization;

    /**
     * @param User $user
     * @return bool
     */
    public function view(User $user)
    {
        return false;
    }

    /**
     * @param User $user
     * @return false
     */
    public function create(User $user)
    {
        return false;
    }

    /**
     * @param User $user
     * @return false
     */
    public function update(User $user)
    {
        return false;
    }

    /**
     * @param User $user
     * @return false
     */
    public function delete(User $user)
    {
        return false;
    }
}
