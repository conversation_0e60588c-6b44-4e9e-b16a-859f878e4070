<?php

namespace App\Services;

use App\Abstracts\AbstractLoanService;
use App\Exceptions\CheckLoanAvailableAmountException;
use App\Exceptions\DoubleLoanException;
use App\Exceptions\DoublePaymentException;
use App\Exceptions\LoanNotFoundException;
use App\Exceptions\TopUpValidationException;
use App\Factory\HcServiceFactory;
use App\Models\Loan;
use App\Models\PreapprovedCreditOffer;
use App\Strategies\Confirmation\ConfirmationStrategyContext;
use App\Traits\Transaction;
use Illuminate\Support\Facades\Log;

class LoanServiceOCL extends AbstractLoanService
{
    use Transaction;

    public function __construct()
    {
        parent::__construct(constants('LOAN_TYPES.OCL'));
    }

    protected function extractCredit($citizen, $details = null)
    {
        // In the case of top-up offer submission, we need to set amount from UI, because it is already calculated (top-up amount + activeLoanBalance)
        if ($this->isTopUpOfferSubmission()) {
            $citizen['credit']['amount'] = $details['amount'];
        }

        return $citizen['credit'];
    }

    protected function resolveCreditOffer($loan_security)
    {
        if ($this->isTopUpOfferSubmission()) {
            // Check for Top-Up Offers with associated $loan_security
            $credit_offer = $loan_security->preapproved_credit_offers_top_up()->first();

            // Fallback to Valid Top-Up Offers by Loan Type and SSN
            if (!$credit_offer) {
                $credit_offer = PreapprovedCreditOffer::getValidTopUpOfferByLoanType($this->type, $loan_security->ssn);
            }

            return $credit_offer;
        }

        // Fallback to the Latest Standard Offer by Loan Type
        return parent::resolveCreditOffer($loan_security);
    }

    protected function insertLoan($loan_security, $details, $schedule)
    {
        return $this->startTransaction(function () use ($loan_security, $details, $schedule) {
            if ($this->isTopUpOfferSubmission()) {
                return $this->insertOrUpdateTopUpLoan($details, $schedule);
            } else {
                return parent::insertLoan($loan_security, $details, $schedule);
            }
        });
    }

    protected function updateLoan($loan, $details, $schedule)
    {
        return $this->startTransaction(function () use ($loan, $details, $schedule) {
            if ($this->isTopUpOfferSubmission()) {
                return $this->insertOrUpdateTopUpLoan($details, $schedule, true);
            } else {
                return parent::updateLoan($loan, $details, $schedule);
            }
        });
    }

    public function isLoanExists($loan_security): bool
    {
        // In some cases we need to handle correctly multiple submission of different loan types from UI
        if ($this->isTopUpOfferSubmission() &&
            !$loan_security->loan_history()->exists()
        ) {
            return false;
        }

        return parent::isLoanExists($loan_security);
    }

    public function isValidLoan($schedule_summary, $citizen_credit, $data, $citizen = null, $schedule = null): bool
    {
        if ($this->isTopUpOfferSubmission()) {
            $rule_engine_credit = $citizen['credit'];

            $top_up_amount = $rule_engine_credit['amount'];
            $active_loan_balance = $rule_engine_credit['top_up_offer']['balance'];
            // The Final top-up loan amount should be this calculation
            $loan_amount = $top_up_amount + $active_loan_balance;

            if (($data['amount'] != $loan_amount) || ($data['top_up_offer']['amount'] != $top_up_amount)) {
                Log::info('Is Valid Loan, Invalid top-up loan approve payload', [
                    'payload_loan_amount' => $data['amount'],
                    'payload_top_up_offer' => $data['top_up_offer'],
                    'rule_engine_credit' => $rule_engine_credit,
                ]);

                return false;
            }
        }

        return parent::isValidLoan($schedule_summary, $citizen_credit, $data, $citizen, $schedule);
    }

    protected function getServiceFeeRate($configs, $credit, $discount = 0)
    {
        if (isset($credit['service_fee_rate']) && $credit['service_fee_rate']) {
            return round($credit['interest_rate'] - $credit['service_fee_rate'], constants('PRECISION'));
        }

        return $configs['service_fee_rate'];
    }

    public function processLoanConfirmation($loan)
    {
        $this->validateConfirmation($loan);

        $this->expireLoan($loan);

        $this->expireTopUpOffers($loan);

        $context = new ConfirmationStrategyContext($loan);

        $context->finalize();
    }

    public function shouldUpdateContractNumber($loan): bool
    {
        return true;
    }

    public function insertOrUpdateTopUpLoan($details, $schedule, $is_update = false)
    {
        $contract_number = $details['top_up_offer']['contract_number'];
        $loan = Loan::getLoanByContractNumber($contract_number);

        if (!$loan) {
            Log::info('Loan not found during top-up loan insert or update', ['contract_number' => $contract_number]);

            throw new LoanNotFoundException();
        }

        $security_service = resolve('App\Interfaces\ISecurityService');
        $loan_security = $security_service->resolveLoanSecurity();

        // We need to update some columns to set the initial state of loan
        $updatable_attrs = array_merge($details, [
            'status' => Loan::PENDING_TOP_UP,
            'top_up_amount' => $details['top_up_offer']['amount'],
        ]);

        if (!$is_update) {
            $this->setNullableAttrs($updatable_attrs);
            $loan->processLoanUpdates($updatable_attrs, $loan_security->id);
            $this->createCitizenForTopUp($loan, $details);
        }

        // Associate security information to a loan
        $loan_security->loan()->associate($loan);
        $loan_security->update();

        if ($is_update) {
            $loan->update($updatable_attrs);
            $loan->deleteLoanSchedule();
        }

        // Associate security information to a preapproved top-up offer
        $this->associateLoanSecurityWithPreapprovedOffer($loan_security);

        $loan->storeLoanSchedule($schedule);

        return $loan;
    }

    private function setNullableAttrs(&$updatable_attrs)
    {
        $nullable_columns = [
            'verified_at',
            'confirmed_at',
            'sign_date',
            'payment_id',
            'payment_type',
            'payment_status',
            'payment_failed',
        ];

        foreach ($nullable_columns as $column) {
            $updatable_attrs[$column] = null;
        }
    }

    protected function cleanupBeforeUpdate($loan)
    {
        if (!$loan->isTopUp()) {
            parent::cleanupBeforeUpdate($loan);
        }
    }

    public function validateConfirmation($loan)
    {
        $hc_service = HcServiceFactory::build($loan->loan_type_id);

        //We need some other top-up checks to avoid parallel loan confirmations and HC storing issues
        if ($loan->isTopUp()) {
            try {
                $this->validateTopUpConfirmation($loan);
            } catch (CheckLoanAvailableAmountException | TopUpValidationException $e) {
                $this->handleTopUpConfirmationException($loan, $e);
            }
        } else {
            if ($hc_service->hasCreditByLoan($loan)) {
                throw new DoubleLoanException();
            }
        }

        if ($loan->payment->paid) {
            throw new DoublePaymentException();
        }
    }

    public function validateTopUpConfirmation($loan)
    {
        $top_up_service_ocl = resolve('App\Services\TopUpServiceOCL');
        $top_up_service_ocl->validateTopUpEligibilityDetails($loan);

        $this->checkLoanAvailableAmount($loan);
    }

    private function handleTopUpConfirmationException($loan, $exception)
    {
        Log::warning('Top-up validation exception: '.get_class($exception), ['message' => $exception->getMessage()]);

        $this->expireLoan($loan);
        $this->expireTopUpOffers($loan);

        throw $exception;
    }

    public function getScheduleAndSummary($calculator, $monthly = null, $start_date = null)
    {
        $schedule = $calculator->generateSchedule($monthly, $start_date);

        Log::debug('Generate schedule', ['schedule' => $schedule]);

        $schedule_summary = $calculator->summarizeSchedule($schedule);

        Log::info('Schedule summary', ['schedule' => $schedule_summary]);

        return ['schedule' => $schedule, 'schedule_summary' => $schedule_summary];
    }
}
