<?php

namespace App\Services;

use App\Exceptions\ImIdInternalErrorException;
use App\Interfaces\IImIdService;
use App\Models\ImIdDetail;
use App\Models\Pallaton\Profile;
use Firebase\JWT\JWT;
use Guz<PERSON><PERSON>ttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Throwable;
use Tymon\JWTAuth\Facades\JWTAuth;

class ImIdService implements IImIdService
{
    const ERROR_USER_CANCEL = 'user_cancel';
    const TOKEN_TYPE = 'Bearer';
    const EXPIRATION_TIME_MINUTES = 3;

    protected $httpClient;

    public function __construct()
    {
        $this->httpClient = new Client([
            'base_uri' => config('im_id.base_url'),
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded',
                'Accept-Encoding' => 'identity',
            ],
        ]);
    }

    public function processAuthentication($phone_number)
    {
        try {
            Log::info('Starting ImId authentication process', ['phone_number' => $phone_number]);
            $session_id = generate_uuid();
            $notification_token = generate_uuid();

            $this->createImIdDetails($phone_number, $session_id, $notification_token);
            Log::info('ImIdDetails created', ['session_id' => $session_id]);

            $client_request = $this->generateClientRequest($phone_number, $session_id, $notification_token);
            $jwt_payload = $this->getJwtPayload($client_request);

            $response = $this->httpClient->post('/idp/yesem/oidc/mc/authorize', [
                 'form_params' => [
                     'client_id' => config('im_id.client_id'),
                     'response_type' => config('im_id.response_type'),
                     'scope' => config('im_id.scope'),
                     'request' => $jwt_payload,
                 ],
             ]);

            Log::info('ImId authorization response received', ['status' => $response->getStatusCode()]);

            return [
                'session_id' => $session_id,
            ];
        } catch (RequestException $e) {
            ImIdDetail::getValidDetail($session_id)->update(['status' => ImIdDetail::FAILED]);
            Log::error('ImId http request Exception', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    public function handleCallback($payload, $auth_token)
    {
        try {
            $im_id_detail = ImIdDetail::where('session_id', $payload['correlation_id'])->first();
            if (!$im_id_detail) {
                Log::warning('ImId detail not found', ['session_id' => $payload['correlation_id']]);
                throw new ImIdInternalErrorException();
            }

            if ($im_id_detail->expiration < now()) {
                $im_id_detail->update(['status' => ImIdDetail::EXPIRED]);
                Log::warning('ImId detail expired', ['session_id' => $payload['correlation_id']]);
                throw new ImIdInternalErrorException();
            }

            if (isset($payload['error']) && $payload['error'] === self::ERROR_USER_CANCEL) {
                $im_id_detail->update(['status' => ImIdDetail::FAILED]);
                Log::warning('User cancelled the operation', ['session_id' => $payload['correlation_id']]);
                throw new ImIdInternalErrorException();
            }

            if (!Str::contains($auth_token, $im_id_detail->notification_token) && $payload['token_type'] !== self::TOKEN_TYPE) {
                $im_id_detail->update(['status' => ImIdDetail::FAILED]);
                Log::warning('Invalid authorization token', ['session_id' => $payload['correlation_id'], 'auth_token' => $auth_token]);
                throw new ImIdInternalErrorException();
            }

            $user_info = $this->imIdGetUserInfo($im_id_detail->session_id, $payload['access_token']);
            if (empty($user_info) || !isset($user_info['ssn'])) {
                throw new ImIdInternalErrorException();
            }

            $im_id_detail->update([
                'ssn' => $user_info['ssn'],
                'status' => ImIdDetail::VERIFIED,
            ]);
        } catch (RequestException $e) {
            if ($im_id_detail) {
                $im_id_detail->update(['status' => ImIdDetail::FAILED]);
            }
            Log::error('ImId http request Exception', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    public function checkImIdStatus($payload)
    {
        $im_id_detail = ImIdDetail::getValidDetail($payload['im_id_session_id']);

        if (!$im_id_detail || $im_id_detail->status === ImIdDetail::FAILED) {
            Log::warning('ImId detail not found or expired', ['im_id_session_id' => $payload['im_id_session_id']]);
            throw new ImIdInternalErrorException();
        }

        $response['status'] = $im_id_detail->status;

        $ssn = $im_id_detail->ssn;
        $user_profile = Profile::where('ssn', $ssn)->first();
        if ($user_profile) {
            $token = JWTAuth::fromUser($user_profile->user);
            $response['token'] = $token;
            Log::info('User profile found and token generated', ['ssn' => $ssn]);
        }

        return response()->json($response);
    }

    public function createRegistrationSession($im_id_session_id)
    {
        $imi_detail = ImIdDetail::where('session_id', $im_id_session_id)->first();

        if (!$imi_detail) {
            Log::warning('ImId session not found', ['im_id_session_id' => $im_id_session_id]);
            throw new ImIdInternalErrorException();
        }

        if ($imi_detail->expiration < now()) {
            $imi_detail->update(['status' => ImIdDetail::EXPIRED]);
            Log::warning('ImId session expired', ['im_id_session_id' => $im_id_session_id]);
            throw new ImIdInternalErrorException();
        }

        return $imi_detail->loan_security->suuid;
    }

    public function updateRegistrationDetails($payload)
    {
        $security_service = resolve('App\Interfaces\ISecurityService');
        $loan_security = $security_service->resolveLoanSecurity();

        SecurityUtilityService::isProfileExists($loan_security->im_id_detail->ssn, $loan_security->phone_number, $payload['email']);
        Log::info('Update registration details', ['payload' => $payload]);
        $loan_security->update(['email' => $payload['email']]);
        Log::debug('Update loan security details successfully');
    }

    private function createImIdDetails($phone_number, $session_id, $notification_token)
    {
        return ImIdDetail::create([
            'phone_number' => $phone_number,
            'session_id' => $session_id,
            'notification_token' => $notification_token,
            'expiration' => now()->addMinutes(self::EXPIRATION_TIME_MINUTES),
        ]);
    }

    private function imIdGetUserInfo($session_id, $access_token)
    {
        try {
            $response = $this->httpClient->post(config('im_id.user_info_url'), [
                'headers' => [
                    'Authorization' => 'Bearer '.$access_token,
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ],
                'form_params' => [
                    'correlation_id' => $session_id,
                ],
            ]);

            $user_info = json_decode($response->getBody(), true);
            Log::info('ImId user info', ['user_info' => $user_info]);

            return $user_info;
        } catch (RequestException $e) {
            Log::error('ImId get user info http request Exception', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    private function generateClientRequest($phone_number, $session_id, $notification_token)
    {
        Log::info('ImId generating client request', [
            'phone_number' => $phone_number,
            'session_id' => $session_id,
        ]);

        $params = [
            'client_id' => config('im_id.client_id'),
            'scope' => config('im_id.scope'),
            'aud' => config('im_id.base_url'),
            'response_type' => config('im_id.response_type'),
            'version' => 'mc_si_r2_v1.0',
            'nonce' => generate_uuid(),
            'login_hint' => $phone_number,
            'acr_values' => '4',
            'binding_message' => 'Authorize imID Access',
            'context' => 'Do you want to authenticate using imID?',
            'correlation_id' => $session_id,
            'client_notification_token' => $notification_token,
            'notification_uri' => config('im_id.redirect_url'),
        ];

        Log::info('ImId Client request parameters generated', [
            'params' => $params,
        ]);

        return $params;
    }

    private function getJwtPayload($client_request)
    {
        try {
            Log::debug('ImId start generating JWT payload');

            $private_key_path = storage_path(config('im_id.private_key_path'));
            Log::debug('ImId loading private key from path', [
                'path' => $private_key_path,
            ]);

            $private_key = openssl_pkey_get_private('file://'.$private_key_path);

            if (!$private_key) {
                Log::warning('ImId failed to load private key', [
                    'openssl_error' => openssl_error_string(),
                ]);
                throw new ImIdInternalErrorException();
            }

            $jwt = JWT::encode($client_request, $private_key, config('im_id.jwt_algorithm'));
            Log::debug('ImId JWT encoded successfully');

            openssl_free_key($private_key);

            Log::info('ImId JWT configured successfully');

            return $jwt;
        } catch (Throwable $e) {
            Log::error('ImId failed to configure JWT payload', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ImIdInternalErrorException();
        }
    }
}
