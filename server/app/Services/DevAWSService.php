<?php

namespace App\Services;

use App\Abstracts\AbstractAWSService;
use File;

class DevAWSService extends AbstractAWSService
{
    public function getPdfStorageName()
    {
        return 'local_loan_documents';
    }

    public function getMediaStorageName()
    {
        return 'local_loan_documents';
    }

    public function getQRCodeStorageName()
    {
        return 'local_qr_codes';
    }

    public function getReferralCodePdfStorageName()
    {
        return 'local_referral_code_pdfs';
    }

    public function getEkengStorageName()
    {
        return 'local_ekeng_media';
    }

    public function getFaceRecognitionStorageName()
    {
        return 'local_recognition_media';
    }

    public function prepareDirectory()
    {
        $pdfs_path = public_path(env('DOCUMENTS_SUBDIR'));

        if (!File::exists($pdfs_path)) {
            File::makeDirectory($pdfs_path);
        }

        $directory = $this->generateDirectoryName();

        $path = "$pdfs_path/$directory";

        File::makeDirectory($path);
        chmod($path, 0777);

        return $directory;
    }
}
