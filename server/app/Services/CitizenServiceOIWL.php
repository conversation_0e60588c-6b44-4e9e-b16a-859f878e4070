<?php

namespace App\Services;

use App\Factory\OWLServiceFactory;
use Log;

class CitizenServiceOIWL extends CitizenServiceOWL
{
    const INITIAL_AMOUNT = 150000;

    public function __construct()
    {
        parent::__construct(constants('LOAN_TYPES.OIWL'));
    }

    protected function prepareCredit($citizen, $calculate_allowance): array
    {
        $citizen_credit = parent::prepareCredit($citizen, $calculate_allowance);

        if (!empty($citizen_credit['credit'])) {
            $citizen_credit['credit']['rounded_requested_loan_amount'] = $this->ceilRequestedLoanAmount($citizen['requested_loan_amount']);
        }

        return $citizen_credit;
    }

    protected function getWalletSchema()
    {
        return [];
    }

    protected function aggregateWalletInfo($params)
    {
        Log::debug('start aggregate wallet info');

        $owl_service = OWLServiceFactory::build($this->type);
        $duration = $owl_service->getWalletDuration();

        Log::debug('end aggregate wallet info');

        return [
            'Amount' => self::INITIAL_AMOUNT,
            'duration' => $duration,
        ];
    }

    public function collectCitizenData($payload): array
    {
        return $this->aggregateCitizenData($payload['document_number']);
    }

    protected function ceilRequestedLoanAmount($amount)
    {
        $loan_config_service = resolve('App\Services\LoanConfigService');
        $loan_configs = $loan_config_service->getConfigs($this->type);

        // In this case rounded_requested_loan_amount will be handled by OCL service
        if ($amount > $loan_configs['max_amount']) {
            return null;
        }

        // Find the first wallet amount that is greater than or equal to the input amount
        foreach ($loan_configs['wallet_available_amounts'] as $wallet_amount) {
            if ($amount <= $wallet_amount) {
                return $wallet_amount;
            }
        }
    }
}
