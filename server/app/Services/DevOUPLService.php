<?php

namespace App\Services;

use Log;

class DevOUPLService extends OUPLService
{
    protected function requestWalletInfo($token, $suuid)
    {
        Log::info('Requesting user data from Upay with token', ['token' => $token, 'suuid' => $suuid]);

        $res = FakerService::getMockedUpayPayload($token);

        Log::info('Requesting user data from Upay', ['result' => $res]);

        return $res;
    }

    public function transfer($token, $amount, $agreement_id, $suuid)
    {
    }
}
