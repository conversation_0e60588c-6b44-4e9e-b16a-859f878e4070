<?php

namespace App\Services;

use App\Exceptions\SmsException;
use App\Interfaces\ISmsService;
use Carbon\Carbon;
use Config;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Log;

class SmsHTTPService extends SmsService implements ISmsService
{
    private $smsResource = 'xml/sms_content';

    public function __construct()
    {
        $this->client = new Client();
    }

    public function send($phone_number, $text)
    {
        Log::info('Sending sms', ['phone_number' => $phone_number, 'text' => $text]);
        $payload = $this->composeSmsPayload($phone_number, $text);

        $headers = [
            'Content-Type' => 'application/xml',
        ];

        $xml = $this->prepareXML($this->smsResource, $payload);
        $request = new Request('POST', env('SMS_HTTP_SERVICE_URL'), $headers, $xml);

        $response = $this->client->send($request);
        $result = $this->xmlToArray($response->getBody());

        if (!$result) {
            Log::error('Send sms, SmsException');
            throw new SmsException($phone_number, $text);
        }

        Log::info('Send sms response', ['response' => json_encode($result)]);

        return $result;
    }

    protected function composeSmsPayload($phone_number, $text)
    {
        $now = Carbon::createFromFormat(Config::get('constants.SMS_DATE_FORMAT'), Carbon::now());

        return [
            'id' => str_random(6),
            'refId' => $now,
            'phoneNumber' => $phone_number,
            'deferDate' => $now,
            'code' => $text,
        ];
    }
}
