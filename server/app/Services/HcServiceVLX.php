<?php

namespace App\Services;

use App\Abstracts\AbstractHcService;
use App\Exceptions\HcException;
use App\Models\CitizenPassport;
use App\Models\HC\HcGCCRDTCODE;
use Log;
use Throwable;

class HcServiceVLX extends AbstractHcService
{
    public function save($loan)
    {
        return $this->startTransaction(function () use ($loan) {
            try {
                parent::save($loan);
            } catch (Throwable $e) {
                Log::info('Store to HC error', ['err' => $e->getTraceAsString()]);
                throw new HcException($e->getMessage());
            }
        });
    }

    protected function getOuterCodeType()
    {
        return constants('HC.LOAN_OUTER_CODE_TYPE_VLX');
    }

    protected function resolveNote2($loan)
    {
        return $loan->nominal_rate;
    }

    public function hasCreditByLoan($loan)
    {
        $ssn = $loan->citizen->getSocCard()->passport_number;

        $has_credit = $this->hasCredit($ssn);
        if (!$has_credit) {
            return $this->hasNonSyncedCredits($ssn);
        }

        return $has_credit;
    }

    protected function clientHasCredit($hc_gc_client, $config)
    {
        if (!$hc_gc_client) {
            return false;
        }

        return HcGCCRDTCODE::hasVlxCredit($hc_gc_client->fCODE);
    }

    public function veloxClosedCredits($soc_card)
    {
        $hcGCClient = $this->getHcGCClientBySocCard($soc_card);

        if (!$hcGCClient) {
            return null;
        }

        return HcGCCRDTCODE::oclClosedCredits($hcGCClient->fCODE);
    }

    protected function getCitizenBySsn($ssn)
    {
        $latest_citizen = 0;

        return CitizenPassport::getCitizenLatestPassportsBySsn($ssn)[$latest_citizen]->citizen ?? null;
    }
}
