<?php

namespace App\Services;

use App\Abstracts\AbstractDocumentService;
use App\Helpers\NumberHelper;
use App\Jobs\GenerateLoanDocuments;
use App\Models\LoanDocument;

class LoanDocumentServiceOIWL extends AbstractDocumentService
{
    public function composePdfData($loan)
    {
        $pdf_data = parent::composePdfData($loan);

        $pdf_data_oiwl = [
            'contract_number' => $loan->contract_number,
            'sign_date' => $loan->sign_date->format('Y-m-d'),
            'rate_in_words' => NumberHelper::floatToText($loan->interest_rate),
        ];

        return array_merge($pdf_data, $pdf_data_oiwl);
    }

    public function getTempPublicDocs($loan)
    {
        return array_merge(
            [
                LoanDocument::CONTRACT_OIWL['name'],
                LoanDocument::PERSONAL_SHEET_OIWL['name'],
            ],
            parent::getTempPublicDocs($loan)
        );
    }

    public function getTempPrivateDocs($loan = null)
    {
        return array_merge(
            [
                LoanDocument::CONTRACT_OIWL_PRIVATE['name'],
                LoanDocument::PERSONAL_SHEET_OIWL_PRIVATE['name'],
            ],
            parent::getTempPrivateDocs($loan)
        );
    }

    public function getDocumentTypes($obfuscated = null, $loan = null)
    {
        $documents = [
            LoanDocument::CONTRACT_OIWL,
            LoanDocument::WHAT_TO_DO,
            LoanDocument::PERSONAL_SHEET_OIWL,
        ];

        if ($obfuscated) {
            $documents = [
                LoanDocument::CONTRACT_OIWL_PRIVATE,
                LoanDocument::PERSONAL_SHEET_OIWL_PRIVATE,
            ];
        }

        return array_merge($documents, parent::getDocumentTypes($obfuscated, $loan));
    }

    public function getLoanDocumentsJob($loan)
    {
        return GenerateLoanDocuments::withChain([
            new GenerateLoanDocuments($loan, $obfuscate = false),
        ]);
    }
}
