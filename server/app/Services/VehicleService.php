<?php

namespace App\Services;

use App\Exceptions\VehicleExcelValidationException;
use App\Exceptions\VehicleNotFoundException;
use App\Helpers\ObjectMerger;
use App\Interfaces\IVehicleService;
use App\Models\PoliceCode;
use App\Models\VehicleModel;
use App\Models\VehiclePrice;
use App\Schemas\PledgedVehicleInfoSchema;
use App\Schemas\VehicleInfoSchema;
use Exception;
use function Functional\reduce_left;
use Illuminate\Database\QueryException;
use Log;
use Rap2hpoutre\FastExcel\Facades\FastExcel;

class VehicleService implements IVehicleService
{
    private const NEED_UPDATE_SERIES_CODES = true;

    public function getVehicles($citizen_data, $params = [])
    {
        // In case of Trade_OVL loan, we get vehicle from Police
        $trade_vehicle = [];
        if (isset($params['tech_passport']) && $params['tech_passport'] !== null) {
            [
                'vehicle_number' => $vehicle_number,
                'tech_passport' => $tech_passport,
                'trade_amount' => $trade_amount,
            ] = $params;

            $trade_vehicle = $this->getTradeVehicle($vehicle_number, $trade_amount, $tech_passport) ?? [];
        }

        $citizen_vehicles = $this->getCitizenVehicles($citizen_data['ekeng']) ?? [];

        return $this->enhanceVehiclesDetails(array_merge($citizen_vehicles, $trade_vehicle));
    }

    protected function getCitizenVehicles($citizen_data)
    {
        $vehicle_info = $citizen_data['vehicle_info'];

        // If citizen doesn't have vehicle, in ekeng response we get status - failed
        if (isset($vehicle_info->status) || $vehicle_info == '' || $vehicle_info == []) {
            return null;
        }

        return $vehicle_info;
    }

    protected function getTradeVehicle($vehicle_number, $trade_amount, $tech_passport)
    {
        $policeService = resolve('App\Interfaces\IPoliceService');
        $trade_vehicle = $policeService->getVehicle($vehicle_number);

        if (!$trade_vehicle || !isset($trade_vehicle->cert_num) || $trade_vehicle->cert_num !== $tech_passport) {
            Log::error('Trade vehicle unmatched tech passport', ['cert_num' => $trade_vehicle->cert_num ?? null, 'tech_passport' => $tech_passport]);

            throw new VehicleNotFoundException();
        }

        $trade_vehicle->trade_amount = $trade_amount;
        $trade_vehicle->tech_passport = $tech_passport;

        return [$trade_vehicle];
    }

    public function enhanceVehiclesDetails($vehicle_info)
    {
        if (!$vehicle_info) {
            return [];
        }

        return reduce_left($vehicle_info, function ($vehicle, $index, $collection, $reduction) {
            $vehicle_model = VehicleModel::getVehicleModelByPoliceCode($vehicle->model_code ?? $vehicle->police_code);

            if (is_null($vehicle_model)) {
                $vehicle->market_price = 0;
                $vehicle->exist = false;
            } else {
                $released = $vehicle->released;
                $vehicle->market_price = $vehicle_model->getMarketPrice($released);
                $vehicle->exist = true;
                $vehicle->model_name = $vehicle->model_name ?? $vehicle_model->mark;
                $vehicle->model = $vehicle->model ?? $vehicle_model->model;
            }

            array_push($reduction, clone $vehicle);

            return $reduction;
        }, []);
    }

    public function prepareVehicleInfo($citizen)
    {
        $schema = new VehicleInfoSchema();
        $vehicle_info_schema = $schema->get();

        $objectMerger = new ObjectMerger($vehicle_info_schema);
        $vehicle_info = $objectMerger->merge($citizen);
        Log::info('Merged data', ['vehicle_context' => $vehicle_info]);

        return ['vehicle' => $vehicle_info];
    }

    /**
     * Function to get lenders info from specific citizen vehicle.
     *
     * @param $citizen
     * @param $vin
     *
     * @return mixed
     */
    public function getPledgedVehicleInfo($citizen, $vin)
    {
        $schema = new PledgedVehicleInfoSchema($vin);
        $vehicle_info_schema = $schema->get();

        $objectMerger = new ObjectMerger($vehicle_info_schema);
        $vehicle_info = $objectMerger->merge($citizen);

        return $vehicle_info['vehicle'];
    }

    public function updateVehicleModels($file)
    {
        Log::debug('updating Vehicles Prices - Started');

        $old_vehicles = VehicleModel::select('id', 'police_code', 'trim', 'model', 'mark')
            ->get()
            ->keyBy(function ($item) {
                // We combine the values for composing unique key
                return "{$item['police_code']}{$item['trim']}";
            });

        $excel_vehicles = FastExcel::import($file)->toArray(); // Convert to array since lazy loading might not work

        // In the Excel table, we have columns by year, where indicated the car prices.
        // Therefore, getting the names of the columns where the year of issue is indicated,
        //we can dynamically insert prices by year.
        $released_years = array_filter(array_keys($excel_vehicles[0]), 'is_numeric');

        $filtered_vehicles = $this->validateAndFilterExcelVehicles($excel_vehicles);

        Log::info('Update Vehicles Prices', ['excel_vehicles_count' => count($excel_vehicles), 'filtered_vehicles_count' => count($filtered_vehicles)]);

        foreach ($filtered_vehicles as $new_vehicle) {
            $key = $new_vehicle['police_code'].$new_vehicle['trim'];

            $old_vehicle = $old_vehicles[$key] ?? null;
            if ($old_vehicle) {
                $this->updateVehicle($old_vehicle, $new_vehicle, $released_years);
            } else {
                $this->insertVehicle($new_vehicle, $released_years);
            }

            if (self::NEED_UPDATE_SERIES_CODES) {
                PoliceCode::updateOrCreateSeriesCodes($new_vehicle);
            }

            // Remove processed police code from the $old_vehicles reference to free up memory usage as much as possible
            unset($old_vehicles[$key]);
        }

        Log::debug('updating Vehicles Prices - Done');
    }

    protected function updateVehicle($old_vehicle, $new_vehicle, $released_years)
    {
        if ($this->isVehicleModified($old_vehicle, $new_vehicle)) {
            VehicleModel::updateVehicleModel($new_vehicle);
        }

        $old_prices = $old_vehicle->prices->pluck('market_price', 'released_year')->toArray();
        $bulk_insert = [];
        foreach ($released_years as $year) {
            $old_price = $old_prices[$year] ?? null;
            $new_price = VehiclePrice::normalizePrice($new_vehicle[$year] ?? null);

            if (isset($old_price)) {
                if ($old_price != $new_price) {
                    VehiclePrice::where('vehicle_model_id', $old_vehicle->id)
                        ->where('released_year', $year)
                        ->update(['market_price' => $new_price]);
                }
            } else {
                $bulk_insert[] = [
                    'vehicle_model_id' => $old_vehicle->id,
                    'released_year' => $year,
                    'market_price' => $new_price,
                ];
            }
        }

        if (!empty($bulk_insert)) {
            VehiclePrice::insert($bulk_insert);
        }
    }

    protected function isVehicleModified($old_vehicle, $new_vehicle)
    {
        return strtoupper($old_vehicle['model']) != strtoupper($new_vehicle['model'])
            || strtoupper($old_vehicle['mark']) != strtoupper($new_vehicle['mark']);
    }

    protected function insertVehicle($new_vehicle, $released_years)
    {
        try {
            $vehicle_model_id = VehicleModel::insertVehicleModel($new_vehicle);
        } catch (QueryException $e) {
            $errorCode = $e->errorInfo[1];
            if ($errorCode == constants('DUPLICATE_KEY_EXCEPTION')) {
                Log::info('Vehicle Modele Duplicate key Exception', ['police_code' => $new_vehicle['police_code'], 'trim' => $new_vehicle['trim']]);

                return;
            }

            throw $e;
        } catch (Exception $e) {
            Log::info('Insert Vehicle Modele ', ['Exception' => $e]);
            throw $e;
        }

        $bulk_insert = [];
        foreach ($released_years as $year) {
            $bulk_insert[] = [
                'vehicle_model_id' => $vehicle_model_id,
                'released_year' => $year,
                'market_price' => VehiclePrice::normalizePrice($new_vehicle[$year] ?? null),
            ];
        }

        if (!empty($bulk_insert)) {
            VehiclePrice::insert($bulk_insert);
        }
    }

    public function getVehiclesMarks()
    {
        $user = auth()->user();
        $merchant = $user->merchants()->first();

        $eco_motors_payer_id = '00233358';
        $is_eco_motors_merchant = $merchant->mi_tax_payer_id == $eco_motors_payer_id;

        // Get the marks that have series code in our system, that's why we need to join it with police_codes table
        return VehicleModel::join('police_codes as pc', function ($join) use ($is_eco_motors_merchant) {
            $query = $join->on('pc.police_code', '=', 'vehicle_models.police_code');
            //TODO this solution is temporary, we need find logic to differentiate mark/models by merchant type if needed
            // because some merchant(s) should see specified group models and another one is not

            if ($is_eco_motors_merchant) {
                $query->where('vehicle_models.police_code', 'ilike', 'E%');
            } else {
                $query->where('vehicle_models.police_code', 'not ilike', 'E%');
            }
        })->get()
            ->groupBy('mark');
    }

    public function validateAndFilterExcelVehicles($excel_vehicles)
    {
        $filtered_vehicles = [];
        $police_code_series_map = [];
        $format_errors = [];
        $duplicate_errors = [];

        foreach ($excel_vehicles as $vehicle) {
            // Validate `police_code` and `series_code`
            if ($vehicle['police_code'] == null || $vehicle['police_code'] == '' ||
                $vehicle['series_code'] == null || $vehicle['series_code'] == ''
            ) {
                // Skip rows with empty police_code/series_code
                continue;
            }

            $police_code = trim($vehicle['police_code']);
            $series_code = trim($vehicle['series_code']);

            // Validate `police_code` format (must be like XXX.XXXX)
            if (!preg_match('/^[a-zA-Z0-9]{3,4}\.[a-zA-Z0-9]{4}$/', $police_code)) {
                $format_errors[] = $police_code;
            }

            $unique_key = "{$police_code}-{$series_code}";

            if (isset($police_code_series_map[$police_code])) {
                // If the same police_code has different series_code values, throw an error
                if (!isset($police_code_series_map[$police_code][$series_code])) {
                    $duplicate_errors[] = $police_code;
                }
            }

            // Keep the last occurrence (if `police_code` and `series_code` match)
            $police_code_series_map[$police_code][$series_code] = true;
            $filtered_vehicles[$unique_key] = $vehicle;
        }

        // If there are format errors, display all of them
        if (!empty($format_errors)) {
            throw new VehicleExcelValidationException(lang('nova.exceptions.InvalidPoliceCodeFormatException').' '.implode(', ', $format_errors));
        }

        // If there are duplicate series_code errors, display all of them
        if (!empty($duplicate_errors)) {
            throw new VehicleExcelValidationException(lang('nova.exceptions.DuplicatePoliceCodeException').' '.implode(', ', $duplicate_errors));
        }

        return array_values($filtered_vehicles);
    }
}
