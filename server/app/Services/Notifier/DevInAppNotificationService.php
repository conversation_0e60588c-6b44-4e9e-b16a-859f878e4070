<?php

namespace App\Services\Notifier;

use App\Abstracts\AbstractNotificationService;
use App\Interfaces\Notifier\IInAppNotificationService;
use Illuminate\Support\Facades\Log;

class DevInAppNotificationService extends AbstractNotificationService implements IInAppNotificationService
{
    public function getAll()
    {
        Log::info('Get auth user`s in-app Notifications from DevInAppNotificationService');

        return [];
    }

    public function create($payload)
    {
        Log::info('Create in-app notification from DevInAppNotificationService', ['payload' => $payload]);

        return ['id' => null, 'reference_id' => null];
    }

    public function markAsRead($notification_id, $all)
    {
        Log::info('markAsRead in-app notification from DevInAppNotificationService', ['notification_id' => $notification_id]);

        return [];
    }

    public function inAppNotifierWebhook($notification_id, $status)
    {
    }
}
