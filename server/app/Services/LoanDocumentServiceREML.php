<?php

namespace App\Services;

use App\Abstracts\AbstractDocumentService;
use App\Helpers\ArrayHelper;
use App\Helpers\NumberHelper;
use App\Helpers\ObjectMerger;
use App\Jobs\GenerateLoanDocuments;
use App\Models\LoanDocument;
use App\Models\PredefinedRealEstate;
use Carbon\Carbon;
use Lang;

class LoanDocumentServiceREML extends AbstractDocumentService
{
    public function composePdfData($loan)
    {
        $pdf_data = parent::composePdfData($loan);

        $real_estate_details = ArrayHelper::pick($loan->loan_security->real_estate_details, ['address', 'price', 'sreml_mortgage_contract_notes', 'sreml_mortgage_contract_quantity']);
        $real_estate_details['real_estate_address'] = $real_estate_details['address'];
        unset($real_estate_details['address']);
        $real_estate_details_private = $this->makePrivate($real_estate_details);
        $mortgage = lang('personal_sheet.mortgage_exist_REML').$loan->loan_security->real_estate_details->address;
        $price = (float) $real_estate_details['price'];
        $loan_mortgage_ratio = ($loan->amount / $price) * 100;
        $insurance = ($loan->amount * 0.22) / 100;
        $company_name = $loan->real_estate_mortgage->evaluation_company->name;

        $real_estate_mortgage = ArrayHelper::pick($loan->real_estate_mortgage, ['evaluated_price', 'evaluated_date', 'subject_type', 'subject_address', 'subject_description', 'ownership_cert_given_date', 'ownership_cert_number', 'ownership_cert_password', 'unified_cadastral_ref_given_date', 'unified_cadastral_ref_number', 'unified_cadastral_ref_password']);
        $rated_date = $real_estate_mortgage['evaluated_date'] ? Carbon::parse($real_estate_mortgage['evaluated_date'])->format(constants('STANDARD_DASHED_DATE_FORMAT')) : 'XXX';
        $real_estate_mortgage['unified_cadastral_ref_given_date'] = $real_estate_mortgage['unified_cadastral_ref_given_date'] ? Carbon::parse($real_estate_mortgage['unified_cadastral_ref_given_date'])->format(constants('STANDARD_DASHED_DATE_FORMAT')) : 'XXX';

        $seller = ArrayHelper::pick($loan->real_estate_mortgage->real_estate_seller, ['first_name', 'last_name', 'middle_name', 'passport_number', 'from', 'address', 'phone_number', 'registration_address', 'bank', 'bank_account', 'birthday', 'given_date']);
        $seller['birthday'] = $seller['birthday'] ? Carbon::parse($seller['birthday'])->format(constants('STANDARD_DASHED_DATE_FORMAT')) : 'XXX';
        $seller['given_date'] = $seller['given_date'] ? Carbon::parse($seller['given_date'])->format(constants('STANDARD_DASHED_DATE_FORMAT')) : 'XXX';

        $real_estate_value = $real_estate_mortgage['evaluated_price'];
        $min_price = $real_estate_value ? min($price, $real_estate_value) : $price;

        $preml_law_mortgage_data = $this->getPremlLawMortgageData($loan->loan_security);

        $pdf_data_reml = array_merge(
            $real_estate_details,
            $real_estate_mortgage,
            $real_estate_details_private,
            [
                'application_name' => lang('application.name_REML'),
                'mortgage_contract_number' => $loan->real_estate_mortgage->contract_number,
                'loan_mortgage_ratio' => NumberHelper::numberToStringDram($loan_mortgage_ratio),
                'mortgage_exist' => $mortgage,
                'company_name' => $company_name,
                'loan_mortgage_ratio_in_words' => NumberHelper::numberInWords($loan_mortgage_ratio),
                'apr_in_words' => NumberHelper::numberInWords($loan->apr),
                'insurance' => NumberHelper::numberToStringDram($insurance),
                'insurance_in_words' => NumberHelper::numberInWords($insurance),
                'insurance_float' => $insurance,
                'price_in_words' => NumberHelper::numberInWords($real_estate_details['price']),
                'price' => NumberHelper::numberToStringDram($real_estate_details['price']),
                'pre_payment' => NumberHelper::numberToStringDram($loan->real_estate_mortgage->prepayment),
                'pre_payment_in_words' => NumberHelper::numberInWords($loan->real_estate_mortgage->prepayment),
                'min_price' => $min_price,
                'min_price_in_words' => NumberHelper::numberInWords($min_price),
                'real_estate_value' => $real_estate_value,
                'rated_date' => $rated_date,
                'evaluated_price' => NumberHelper::numberToStringDram($real_estate_value),
                'evaluated_price_in_words' => NumberHelper::numberInWords($real_estate_value),
                'seller' => $seller,
                'preml_law_mortgage_data' => $preml_law_mortgage_data,
                'interest_rate_in_words' => NumberHelper::numberInWords($loan->interest_rate),
            ]
        );

        return array_merge($pdf_data, $pdf_data_reml);
    }

    private function getPremlLawMortgageData($loan_security)
    {
        if ($loan_security->loan_subtype_id === constants('LOAN_SUBTYPES.PREML')) {
            $predefined_real_estate = PredefinedRealEstate::where('token', $loan_security->re_token)->first();

            if (isset($predefined_real_estate)) {
                $preml_law_mortgage_data = ArrayHelper::pick($predefined_real_estate, ['building', 'floor', 'apartment_number', 'area', 'building_deadline']);
                $preml_law_mortgage_data['building_deadline'] = $this->composeLocalizedDate($preml_law_mortgage_data['building_deadline']);

                return $preml_law_mortgage_data;
            }
        }

        return [];
    }

    public function composePdfDataForReport($loan)
    {
        $pdf_data = $this->composePdfData($loan);

        $citizen_report_schema = resolve('App\Schemas\CitizenReportSchema');
        $citizen_report = $this->composeCitizenReport($loan->loan_security);

        $net_salary = (float) $citizen_report['net_salary'];
        $oti_coefficient = (((float) $loan->monthly_payment + $pdf_data['insurance_float']) / $net_salary) * 100;

        $acra_loans_history = $citizen_report['acraLoansHistory'];
        $guarantee_count = $citizen_report['guaranteeCount'];

        $acra_loans_data = [];
        $balance_limits = null;
        if ($acra_loans_history) {
            $acra_loans_data = $citizen_report_schema->getActiveLoansWithMonthlyPayment($acra_loans_history);
            $amounts = array_column($acra_loans_data, 'Amount');
            $balance_limits = ['min' => min($amounts), 'max' => max($amounts)];
        }

        $pdf_data_reml = array_merge(
            $pdf_data,
            [
                'net_salary' => $net_salary,
                'current_workplaces' => $citizen_report['currentWorkplaces'],
                'acra_loans_history' => $acra_loans_history,
                'acra_loans_data' => $acra_loans_data,
                'balance_limits' => $balance_limits,
                'delay_quantity' => $citizen_report['delayQuantity'],
                'delay_quantity_in_year' => $citizen_report['delayQuantityInYear'],
                'oti_coefficient' => $oti_coefficient,
                'guarantee_count' => $guarantee_count,
            ]
        );

        return array_merge($pdf_data, $pdf_data_reml);
    }

    protected function composeCitizenReport($loan_security)
    {
        $meta = $loan_security->security_metas()->where('step', 'CITIZEN_INFO')->first();
        $decoded_content = json_decode($meta->content, true);
        $citizen = ArrayHelper::pick($decoded_content, ['nork', 'acra']);
        $citizen_report_schema = resolve('App\Schemas\CitizenReportSchema');
        $citizen_report_schema_data = $citizen_report_schema->get();
        $objectMerger = new ObjectMerger($citizen_report_schema_data);

        return $objectMerger->merge($citizen);
    }

    public function getDocumentTypes($obfuscated = null, $loan = null)
    {
        // Todo this list will be grow up on the future
        $documents = [
            LoanDocument::ARBITRATION_REML,
            LoanDocument::PERSONAL_SHEET_REML,
            LoanDocument::APPLICATION,
            LoanDocument::BENEFICARY_REML,
            LoanDocument::PROJECT_REML,
        ];

        if ($obfuscated) {
            $documents = [
                LoanDocument::PERSONAL_SHEET_REML_PRIVATE,
                LoanDocument::APPLICATION_PRIVATE,
            ];
        }

        return array_merge($this->getDocumentTypesBySubType($obfuscated, $loan),
            $documents,
            parent::getDocumentTypes($obfuscated, $loan)
        );
    }

    public function getLoanDocumentsJob($loan)
    {
        return GenerateLoanDocuments::withChain([
            new GenerateLoanDocuments($loan, $obfuscate = false),
        ]);
    }

    protected function getDocumentTypesBySubType($obfuscated = null, $loan = null)
    {
        $loan_subtype = $loan->loan_security->loan_subtype_id;

        if ($loan_subtype === constants('LOAN_SUBTYPES.SREML')) {
            if ($obfuscated) {
                return [
                    LoanDocument::CONTRACT_SREML_PRIVATE,
                    LoanDocument::MORTGAGE_CONTRACT_SREML_PRIVATE,
                ];
            }

            return [
                LoanDocument::CONTRACT_SREML,
                LoanDocument::MORTGAGE_CONTRACT_SREML,
             ];
        }

        if ($loan_subtype === constants('LOAN_SUBTYPES.PREML')) {
            $company_id = SecurityUtilityService::getDeveloperCompany($loan->loan_security->re_token);

            if ($company_id === constants('DEVELOPER_COMPANIES.RENSHIN.ID')) {
                if ($obfuscated) {
                    return [
                        LoanDocument::CONTRACT_PREML_RENSHIN_PRIVATE,
                        LoanDocument::LAW_MORTGAGE_PREML_RENSHIN_PRIVATE,
                    ];
                }

                return [
                    LoanDocument::SPECIAL_ACCOUNT_PREML_RENSHIN,
                    LoanDocument::LAW_MORTGAGE_PREML_RENSHIN,
                    LoanDocument::CONTRACT_PREML_RENSHIN,
                    LoanDocument::PROJECT_PREML_RENSHIN,
                ];
            }
            if ($company_id === constants('DEVELOPER_COMPANIES.LUYSER.ID')) {
                if ($obfuscated) {
                    return [
                        LoanDocument::CONTRACT_PREML_LUYSER_PRIVATE,
                        LoanDocument::LAW_MORTGAGE_PREML_LUYSER_PRIVATE,
                    ];
                }

                return [
                    LoanDocument::SPECIAL_ACCOUNT_PREML_LUYSER,
                    LoanDocument::LAW_MORTGAGE_PREML_LUYSER,
                    LoanDocument::CONTRACT_PREML_LUYSER,
                ];
            }
        }
    }
}
