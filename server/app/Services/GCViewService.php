<?php

namespace App\Services;

use App\Interfaces\IGCViewService;
use App\Models\CitizenFaceRecognition;
use App\Models\Loan;
use App\Models\LoanSecurity;
use Carbon\Carbon;
use function Functional\map;

class GCViewService implements IGCViewService
{
    public function persistIdentityVerificationCode($document_number, $code)
    {
        $redis_service = resolve('App\Interfaces\IRedisService');

        $redis_service->update("gc-view:gcd:$document_number", $code, constants('GC_VIEW_IDENTITY_VERIFICATION_CODE_EXP'));
    }

    public function getIdentityVerificationCode($document_number)
    {
        $redis_service = resolve('App\Interfaces\IRedisService');

        return $redis_service->get("gc-view:gcd:$document_number");
    }

    public function getExposedDocuments($code)
    {
        $loan_security = $this->getLoanSecurityByVerificationCode($code);
        $documents = $loan_security->loan->documents ?? [];

        return map($documents, function ($d) {
            if (strpos($d->path, 'private') === false) {
                return env('APP_URL')."/$d->path";
            }
        });
    }

    public function markAsOffline($code)
    {
        $loan_security = $this->getLoanSecurityByVerificationCode($code);
        $loan_security->update(['is_offline' => true]);
    }

    private function getLoanSecurityByVerificationCode($code)
    {
        return LoanSecurity::where('identity_verification_code', $code)->with('loan.documents')->latest()->first();
    }

    public function loanInfo($from, $to, $status, $ls, $sort, $available_ids = [], $isNotIn = false)
    {
        $utcFrom = Carbon::createFromFormat('Y-m-d', $from, constants('ARM_TIMEZONE'))->setTime(0, 0, 0)->setTimezone(constants('UTC_TIME'));
        $utcTo = Carbon::createFromFormat('Y-m-d', $to, constants('ARM_TIMEZONE'))->setTime(0, 0, 0)->setTimezone(constants('UTC_TIME'));
        $q = CitizenFaceRecognition::with(['loan_security', 'ekeng_photo'])
            ->where('created_at', '>=', $utcFrom)
            ->where('created_at', '<=', $utcTo)
            ->where('type', CitizenFaceRecognition::CAMERA_PHOTO)
            ->orderBy('created_at', $sort);

        if ($status) {
            $q->where('status', $status);
        }

        if (count($available_ids) > 0 && !$isNotIn) {
            $q->whereIn('id', $available_ids);
        } elseif ($isNotIn) {
            $q->whereNotIn('id', $available_ids);
        }

        if (!$ls) {
            return $q->get();
        }

        if ($ls == 'true') {
            $q->has('loan_security.loan');
        } else {
            $q->doesnthave('loan_security.loan');
        }

        return $q->get();
    }

    public function moderatorLoanInfo($from, $to, $status, $ls, $sort, $available_ids = [], $isNotIn = false)
    {
        $utcFrom = Carbon::createFromFormat('Y-m-d', $from, constants('ARM_TIMEZONE'))->setTime(0, 0, 0)->setTimezone(constants('UTC_TIME'));
        $utcTo = Carbon::createFromFormat('Y-m-d', $to, constants('ARM_TIMEZONE'))->setTime(0, 0, 0)->setTimezone(constants('UTC_TIME'));

        $q = Loan::where('loans.created_at', '>=', $utcFrom)
            ->where('loans.created_at', '<=', $utcTo)
            ->where('loan_type_id', constants('LOAN_TYPES.OCL'))
            ->whereIn('status', [Loan::CONFIRMED, Loan::REJECTED])
            ->orderBy('loans.created_at', $sort);

        $confirmed = Loan::where('status', Loan::CONFIRMED)
            ->where('loan_type_id', constants('LOAN_TYPES.OCL'))
            ->where('created_at', '>=', $utcFrom)
            ->where('created_at', '<=', $utcTo)->count();

        $rejected = Loan::where('status', Loan::REJECTED)
            ->where('loan_type_id', constants('LOAN_TYPES.OCL'))
            ->where('created_at', '>=', $utcFrom)
            ->where('created_at', '<=', $utcTo)->count();

        if ($status) {
            $q->where('status', $status);
        }

        return [
            'loans' => $q->get(),
            'confirmed' => $confirmed,
            'rejected' => $rejected,
        ];
    }
}
