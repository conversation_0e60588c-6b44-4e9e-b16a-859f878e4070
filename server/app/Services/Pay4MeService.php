<?php

namespace App\Services;

use App\Exceptions\InternalErrorException;
use App\Exceptions\InvalidLoanException;
use App\Exceptions\InvalidSuuidException;
use App\Exceptions\InvalidTokenException;
use App\Helpers\EnvironmentHelper;
use App\Helpers\PassportHelper;
use App\Interfaces\IPay4MeService;
use App\Models\Loan;
use App\Models\LoanSecurity;
use Log;

class Pay4MeService implements IPay4MeService
{
    const UNAPPROVED = 'UNAPPROVED';
    const IN_PROGRESS = 'IN_PROGRESS';
    const APPROVED = 'APPROVED';

    public function createSession($token)
    {
        if (LoanSecurity::where('re_token', $token)->exists()) {
            Log::error('Token already used', ['token' => $token]);

            throw new InvalidTokenException();
        }

        $loan_security = $this->createLoanSecurity($token);

        return [
            'token' => $loan_security->re_token,
            'suuid' => $loan_security->suuid,
            'url' => $this->getPay4meReferralLink($loan_security->re_token),
        ];
    }

    private function getPay4meReferralLink($token)
    {
        return EnvironmentHelper::generateUrl("/home?source=pay4me&token=$token");
    }

    public function checkStatus($suuid)
    {
        $loan_security = $this->getLoanSecurityBySuuid($suuid);

        if (is_null($loan_security)) {
            Log::error('Check status - Invalid suuid', ['loan_security' => $loan_security]);

            throw new InvalidSuuidException();
        }

        $status = $this->getStatus($loan_security);

        return [
            'status' => $status,
            'suuid' => $suuid,
        ];
    }

    public function getDetails($suuid)
    {
        $loan_security = $this->getLoanSecurityBySuuid($suuid);
        if (!$loan_security) {
            Log::error('Get Details - loan security dose not exist');

            throw new InvalidSuuidException();
        }

        $loan = $loan_security->loan;
        if (!$loan) {
            Log::error('Get Details - loan dose not exist');

            throw new InvalidLoanException();
        }

        if ($loan->status !== Loan::CONFIRMED) {
            throw new InternalErrorException();
        }

        $business = $loan->business;
        $citizen = $loan->citizen;
        $loan_config_service = resolve('App\Services\LoanConfigService');
        $loan_configs = $loan_config_service->getConfigsByType(constants('LOAN_TYPES.OBL'));

        return [
            'limit' => $loan->amount,
            'start_date' => $loan->sign_date
                ->setTimezone(constants('ARM_TIMEZONE'))
                ->format(constants('SERVER_DATE_TIME_FORMAT')),
            'end_date' => now()->setTimezone(constants('ARM_TIMEZONE'))
                ->addMonths($loan_configs['duration'])
                ->format(constants('SERVER_DATE_TIME_FORMAT')),
            'contract_number' => $loan->contract_number,
            'credit_code' => $loan->credit_code,
            'service_fee_rate' => $loan->service_fee_rate,
            'org_name' => $business->org_name,
            'tax_identifier' => $business->tax_identifier,
            'suuid' => $suuid,
            'email' => $citizen->email,
            'phone_number' => $citizen->phone_number,
            'additional_phone_number' => $citizen->additional_phone_number,
            'address' => PassportHelper::composeAddress($citizen),
            'penalty_base_amount' => $loan_configs['penalty_base_amount'],
            'penalty_percentage_amount' => $loan_configs['penalty_percentage_amount'],
        ];
    }

    protected function getStatus($loan_security)
    {
        $loan = $loan_security->loan;

        if ($loan) {
            if ($loan->status === Loan::VERIFIED || $loan->status === Loan::CONFIRMED) {
                return self::APPROVED;
            }
        }

        if ($loan_security->suuid_exp < now()) {
            return self::UNAPPROVED;
        }

        return self::IN_PROGRESS;
    }

    private function createLoanSecurity($token)
    {
        $payload = [
            'loan_type_id' => constants('LOAN_TYPES.OBL'),
            'suuid' => generate_uuid(),
            're_token' => $token,
            'suuid_exp' => now()->addDay(),
        ];

        return LoanSecurity::create($payload);
    }

    private function getLoanSecurityBySuuid($suuid)
    {
        return LoanSecurity::where('suuid', $suuid)->with('loan')->first();
    }
}
