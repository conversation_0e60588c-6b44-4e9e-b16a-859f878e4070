<?php

namespace App\Services;

use App\Exceptions\ExpiredPassportException;
use App\Exceptions\SpouseDocumentException;
use App\Helpers\ObjectMerger;
use App\Helpers\PassportHelper;
use App\Schemas\PersonalInfoSchema;
use Carbon\Carbon;
use Exception;
use function Functional\filter;
use Log;

class SpouseService
{
    public function prepareSpouseInfo($citizen_info)
    {
        $spouse_ekeng = $this->getSpouse($citizen_info);

        return $this->composeSpouseInfo($spouse_ekeng);
    }

    protected function getSpouse($citizen_info)
    {
        // Check is citizen married
        if (!$citizen_info || $citizen_info['spouse'] === null) {
            return null;
        }

        // To get a spouse's social card, verify the citizen by name and take the other social card:
        $spouse_name = array_search($citizen_info['first_name'], $citizen_info['spouse']);
        $spouse_key = ($spouse_name === 'person') ? 'person2_soc_card' : 'person_soc_card';

        if (!PassportHelper::isSocialCard($citizen_info['spouse'][$spouse_key])) {
            throw new SpouseDocumentException();
        }

        $spouse_soc_card = $citizen_info['spouse'][$spouse_key];

        $aggregator_service = resolve('App\Interfaces\IAggregatorService');

        try {
            $spouse_ekeng['ekeng'] = $aggregator_service->getEkengData($spouse_soc_card);
        } catch (ExpiredPassportException $e) {
            throw new SpouseDocumentException();
        } catch (Exception $e) {
            throw $e;
        }

        if ($spouse_ekeng['ekeng']['passport_data']->IsDead === 'true') {
            return null;
        }

        return ['ekeng' => $spouse_ekeng['ekeng']];
    }

    public function composeSpouseInfo($citizen)
    {
        if ($citizen === null) {
            return [];
        }

        $schema = new PersonalInfoSchema();
        $spouse_info_schema = $schema->get();

        $object_merger = new ObjectMerger($spouse_info_schema);
        $spouse_info = $object_merger->merge($citizen);

        $spouse_passports = filter($spouse_info['passports'], function ($p) {
            return $p['type'] !== constants('SOC_CARD');
        });

        if (empty($spouse_passports) || Carbon::createFromFormat(constants('SERVER_DATE_TIME_FORMAT'), $spouse_passports[0]['expire_date'])->lt(Carbon::now())) {
            throw new SpouseDocumentException();
        }

        Log::info('composeSpouseInfo, Merged data', ['spouse_info' => $spouse_info]);

        // Getting first passport for Spouse
        $spouse = array_merge($spouse_info, $spouse_passports[0]);

        return ['spouse_info' => $spouse];
    }
}
