<?php

namespace App\Services;

use App\Abstracts\AbstractOWLService;
use App\Exceptions\IdramInvalidRequestInfoException;
use App\Exceptions\IdramInvalidTokenException;
use App\Exceptions\IdramUnableToTransferException;
use App\Exceptions\OWLInsufficientBalanceException;
use App\Models\LoanSecurity;
use App\Traits\MeasureExecutionTime;
use Exception;
use Log;

class OIDLService extends AbstractOWLService
{
    use MeasureExecutionTime;
    const SUCCESS_OPCODE = 0;
    const INSUFFICIENT_BALANCE_OPCODE = 5;

    protected function requestWalletInfo($token, $suuid = null)
    {
        try {
            $request_body = [
                'base_uri' => env('IDRAM_LOAN_URL'),
                'verify' => false,
                'json' => ['Token' => $token],
                'headers' => [
                    '_SessionId_' => env('IDRAM_LOAN_SESSION_ID'),
                    '_EncMethod_' => 'NONE',
                ],
            ];

            Log::info('Requesting user data from Idram', ['request_body' => $request_body]);

            $response = $this->measureExecutionTime(function () use ($request_body) {
                return $this->client->post('GetInfo', $request_body);
            },
                __FUNCTION__,
                ['Request body' => $request_body]
            );

            $body = $response->getBody();
            $res = json_decode($body->getContents(), true);

            Log::info('Requesting user data from Idram', ['result' => $res]);
        } catch (Exception $e) {
            Log::error('Requesting user data from Idram, Exception', ['error' => $e->getTraceAsString()]);
            throw $e;
        }

        if ($res['OpCode'] != self::SUCCESS_OPCODE) {
            Log::error('Requesting user data from Idram, IdramInvalidRequestInfoException', ['err' => $res['OpDesc']]);
            throw new IdramInvalidRequestInfoException();
        }

        return $res;
    }

    public function transfer($token, $amount, $agreement_id, $suuid)
    {
        try {
            $response = $this->measureExecutionTime(function () use ($token, $amount, $agreement_id) {
                return $this->client->post('Transfer',
                    [
                        'base_uri' => env('IDRAM_LOAN_URL'),
                        'verify' => false,
                        'json' => ['Token' => $token, 'Amount' => $amount, 'AgreementId' => $agreement_id, 'Pin' => env('IDRAM_LOAN_API_PIN')],
                        'headers' => [
                            '_SessionId_' => env('IDRAM_LOAN_SESSION_ID'),
                            '_EncMethod_' => 'NONE',
                        ],
                    ]);
            },
                __FUNCTION__,
                ['Token' => $token, 'Amount' => $amount, 'AgreementId' => $agreement_id]
            );

            $body = $response->getBody();
            $res = json_decode($body->getContents(), true);

            Log::info('Transfer idram loan', ['result' => $res]);
        } catch (Exception $e) {
            Log::error('Transfer idram loan, Exception', ['error' => $e->getTraceAsString()]);
            throw $e;
        }

        if ($res['OpCode'] != self::SUCCESS_OPCODE) {
            if ($res['OpCode'] == self::INSUFFICIENT_BALANCE_OPCODE) {
                $alert_service = resolve('App\Services\AlertService');

                $alert_service->processAlerting(
                    constants('ALERT.ACTIONS.IDRAM_NO_BALANCE'),
                    constants('ALERT.CASES.INSUFFICIENT_BALANCE'),
                    []
                );

                throw new OWLInsufficientBalanceException();
            }

            Log::error('Transfer idram loan, IdramUnableToTransferException');
            throw new IdramUnableToTransferException();
        }
    }

    protected function getLoanConfigs($loan)
    {
        /** @var LoanConfigService $loan_config_service */
        $loan_config_service = resolve('App\Services\LoanConfigService');
        $loan_configs = $loan_config_service->getConfigs(constants('LOAN_TYPES.OIDL'));

        $idram_details = $loan->wallet_details;

        return [
            'idram_id' => $idram_details->account_id,
            'penalty_base_amount' => $loan_configs['penalty_base_amount'],
            'penalty_percentage_amount' => $loan_configs['penalty_percentage_amount'],
        ];
    }

    public function checkTokenAvailability($token)
    {
        if (LoanSecurity::where('owl_token', $token)->exists()) {
            Log::warning('Idram Invalid Token Exception', ['token' => $token]);

            throw new IdramInvalidTokenException();
        }
    }

    protected function getWalletKey(): string
    {
        return 'idram';
    }

    public function getWalletDuration(): int
    {
        $loan_config_service = resolve('App\Services\LoanConfigService');
        $loan_configs = $loan_config_service->getConfigs(constants('LOAN_TYPES.OIDL'));

        return $loan_configs['max_months'];
    }
}
