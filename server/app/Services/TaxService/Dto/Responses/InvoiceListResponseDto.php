<?php

namespace App\Services\TaxService\Dto\Responses;

use App\Dtos\AbstractResponseDto;
use App\Services\TaxService\Dto\Entities\InvoiceDto;

class InvoiceListResponseDto extends AbstractResponseDto
{
    /**
     * @var bool
     */
    protected $ok;

    /**
     * @var InvoiceDto[]
     */
    private $payload = [];

    public function addInvoice(InvoiceDto $invoice): void
    {
        $this->payload[] = $invoice;
    }

    public function getPayload()
    {
        return $this->payload;
    }

    public static function createFromArray(array $data): self
    {
        $self_instance = new self();
        $self_instance->setValue('ok', $data['ok']);

        foreach ($data['payload'] as $invoice) {
            $invoice_dto = InvoiceDto::createFromArray($invoice);

            $self_instance->addInvoice($invoice_dto);
        }

        return $self_instance;
    }

    /**
     * {@inheritdoc}
     */
    public function toArray(): array
    {
        $result = [];
        foreach ($this->payload as $invoice_dto) {
            $result[] = $invoice_dto->toArray();
        }

        return $result;
    }
}
