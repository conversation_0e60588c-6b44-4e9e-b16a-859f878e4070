<?php

namespace App\Services\TaxService;

use App\Exceptions\TaxService\TaxServiceGetInvoiceDetailsByIdException;
use App\Exceptions\TaxService\TaxServiceGetInvoiceProductDetailsByIdException;
use App\Exceptions\TaxService\TaxServiceGetInvoicesException;
use App\Services\TaxService\Dto\Entities\InvoiceDto;
use App\Services\TaxService\Dto\Entities\TaxServiceSoapClientDto;
use App\Services\TaxService\Dto\Requests\InvoiceDetailsByIdRequestDto;
use App\Services\TaxService\Dto\Requests\InvoiceListRequestDto;
use App\Services\TaxService\Dto\Requests\InvoiceProductDetailsByIdRequestDto;
use App\Services\TaxService\Dto\Responses\InvoiceDetailsByIdResponseDto;
use App\Services\TaxService\Dto\Responses\InvoiceListResponseDto;
use App\Services\TaxService\Dto\Responses\InvoiceProductDetailsByIdResponseDto;
use App\Traits\MeasureExecutionTime;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Throwable;

class EInvoicingService
{
    use MeasureExecutionTime;

    /**
     * Authentication client.
     *
     * @var TaxServiceSoapClient
     */
    private $soap_client;
    private $client;

    /**
     * @throws \Exception
     */
    public function __construct(TaxServiceSoapClientDto $tax_service_soap_client_dto)
    {
        $this->soap_client = new TaxServiceSoapClient($tax_service_soap_client_dto);
        $this->client = $this->getConnection();
    }

    protected function getConnection(): Client
    {
        if (!$this->client) {
            $this->client = new Client([
                'base_uri' => config('tax_service.e_invoicing_base_url'),
                'verify' => false,
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'Cookie' => 'jwt-auth-token='.$this->soap_client->getAuthToken(),
                ],
            ]);
        }

        return $this->client;
    }

    public function getInvoiceList(InvoiceListRequestDto $dto): InvoiceListResponseDto
    {
        try {
            Log::info('Fetching invoice list', $dto->toArray());

            $response = $this->client->post('api/invoice/invoice-list', [
                'json' => $dto->toArray(),
            ]);

            $body = $response->getBody();
            $result = json_decode($body->getContents(), true);

            if (!$result['ok'] || !isset($result['payload'])) {
                throw new TaxServiceGetInvoicesException($result['failure']['message'] ?? null);
            }

            Log::info('Invoice list fetched', $dto->toArray());

            return InvoiceListResponseDto::createFromArray($result);
        } catch (Throwable $e) {
            Log::error('Failed to fetch invoice list', ['error' => $e->getMessage()]);

            throw $e;
        }
    }

    public function getInvoiceDetailsById(InvoiceDetailsByIdRequestDto $dto, InvoiceDto $invoice_dto): InvoiceDetailsByIdResponseDto
    {
        try {
            Log::info('Fetching invoice details by id', $dto->toArray());

            $response = $this->client->post('api'.$this->resolveInvoiceDetailsUrlByInvoiceType($invoice_dto), [
                'json' => $dto->toArray(),
            ]);
            $body = $response->getBody();
            $result = json_decode($body->getContents(), true);

            if (!$result['ok'] || !isset($result['payload'])) {
                throw new TaxServiceGetInvoiceDetailsByIdException($result['failure']['message'] ?? null);
            }
            Log::info('Invoice details fetched', $result);

            return InvoiceDetailsByIdResponseDto::createFromArray($result);
        } catch (Throwable $e) {
            Log::error('Failed to fetch invoice details by id', ['error' => $e->getMessage()]);

            throw $e;
        }
    }

    public function resolveInvoiceDetailsUrlByInvoiceType(InvoiceDto $invoice_dto): string
    {
        $available_types = $invoice_dto->getAvailableTypes();

        switch ($invoice_dto->getValue('type')) {
            case $available_types['ACC_DOC_GOODS']:
                return '/acc-doc-goods/acc-doc-goods-by-id';
            case $available_types['ACC_DOC_SERVICES']:
                return '/acc-doc-services/acc-doc-services-by-id';
            case $available_types['EXCISE']:
                return '/excise/excise-by-id';
            case $available_types['GOODS']:
                return '/goods/goods-by-id';
            case $available_types['LEASING']:
                return '/leasing/leasing-by-id';
            case $available_types['SERVICES']:
                return '/services/services-by-id';
            default:
                return '';
        }
    }

    public function getInvoiceProductDetailsById(InvoiceProductDetailsByIdRequestDto $dto, InvoiceDto $invoice_dto): InvoiceProductDetailsByIdResponseDto
    {
        try {
            Log::info('Fetching invoice product details by id', $dto->toArray());

            $response = $this->client->post('api'.$this->resolveInvoiceProductDetailsUrlByInvoiceType($invoice_dto), [
                'json' => $dto->toArray(),
            ]);
            $body = $response->getBody();
            $result = json_decode($body->getContents(), true);

            if (!$result['ok'] || !isset($result['payload'])) {
                throw new TaxServiceGetInvoiceProductDetailsByIdException($result['failure']['message'] ?? null);
            }
            Log::info('Invoice product details fetched', $result);

            return InvoiceProductDetailsByIdResponseDto::createFromArray($result);
        } catch (Throwable $e) {
            Log::error('Failed to fetch invoice product details by id', ['error' => $e->getMessage()]);

            throw $e;
        }
    }

    public function resolveInvoiceProductDetailsUrlByInvoiceType(InvoiceDto $invoice_dto): string
    {
        $available_types = $invoice_dto->getAvailableTypes();

        switch ($invoice_dto->getValue('type')) {
            case $available_types['ACC_DOC_GOODS']:
                return '/acc-doc-goods/acc-doc-goods-product-by-invoice-id';
            case $available_types['ACC_DOC_SERVICES']:
                return '/acc-doc-services/acc-doc-services-product-by-invoice-id';
            case $available_types['EXCISE']:
                return '/excise/excise-product-by-invoice-id';
            case $available_types['GOODS']:
                return '/goods/goods-product-by-invoice-id';
            case $available_types['LEASING']:
                return '/leasing/leasing-product-by-invoice-id';
            case $available_types['SERVICES']:
                return '/services/services-product-by-invoice-id';
            default:
                return '';
        }
    }
}
