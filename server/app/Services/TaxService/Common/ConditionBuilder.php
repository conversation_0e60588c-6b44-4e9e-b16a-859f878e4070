<?php

namespace App\Services\TaxService\Common;

use Carbon\Carbon;

/**
 * Class ConditionBuilder.
 */
class ConditionBuilder
{
    /**
     * @var string
     */
    private $conditions = '';

    /**
     * Create and return new instance.
     *
     * @return self
     */
    public static function make()
    {
        return new self();
    }

    /**
     * Add a WHERE condition.
     *
     * @param string|Closure $column   The column name or a closure to build nested conditions
     * @param string|null    $operator the comparison operator
     * @param mixed|null     $value    the value to compare with
     * @param string         $join     the join operator, default 'and'
     *
     * @return $this
     */
    public function where($column, ?string $operator = null, $value = null, string $join = 'and'): self
    {
        $this->conditions .= $this->conditions ? " $join " : '';
        if (is_callable($column)) {
            $query = new self();
            call_user_func($column, $query);
            $this->conditions .= $query->build();
        } else {
            $this->conditions .= "(#$column $operator {$this->formattedValue($value)})";
        }

        return $this;
    }

    /**
     * Add an 'or' WHERE condition.
     *
     * @param string|Closure $column   the column name or a closure to build nested conditions
     * @param string|null    $operator the comparison operator
     * @param mixed|null     $value    the value to compare with
     *
     * @return $this
     */
    public function orWhere(mixed $column, ?string $operator = null, mixed $value = null): self
    {
        return $this->where($column, $operator, $value, 'or');
    }

    /**
     * Add a WHERE IN condition with default 'AND' join.
     *
     * @param string $column the column name
     * @param array  $values the array of values
     *
     * @return $this
     */
    public function whereIn(string $column, array $values, string $join = 'and'): self
    {
        $this->conditions .= ($this->conditions ? " $join " : '')."(#$column in [".$this->formattedValue($values).'])';

        return $this;
    }

    /**
     * Add a WHERE IN condition with 'OR' join.
     *
     * @param string $column the column name
     * @param array  $values the array of values
     *
     * @return $this
     */
    public function orWhereIn(string $column, array $values): self
    {
        return $this->whereIn($column, $values, 'or');
    }

    /**
     * Build the condition string.
     *
     * @return string the constructed condition string
     */
    public function build(): string
    {
        return '('.$this->conditions.')';
    }

    /**
     * Return type formatted value.
     *
     * @param mixed $value
     */
    private function formattedValue($value): string
    {
        if (is_array($value)) {
            $result = '';
            foreach ($value as $element) {
                if (is_int($element) || is_float($element)) {
                    $result .= "$element, ";
                } else {
                    $result .= "'$element', ";
                }
            }

            return rtrim($result, ', ');
        }

        if ($value instanceof Carbon) {
            return "date('{$value->toDateTimeString()}')";
        }

        if (!is_int($value) && !is_float($value)) {
            return "'$value'";
        }

        return "$value";
    }
}
