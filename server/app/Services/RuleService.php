<?php

namespace App\Services;

use App\Interfaces\IRuleService;
use App\Models\RuleSet;
use App\RuleEngine\Phases\Factory\BnplPhaseFactory;
use App\RuleEngine\Phases\Factory\OaslPhaseFactory;
use App\RuleEngine\Phases\Factory\OblPhaseFactory;
use App\RuleEngine\Phases\Factory\OclPhaseFactory;
use App\RuleEngine\Phases\Factory\OeplPhaseFactory;
use App\RuleEngine\Phases\Factory\OfslPhaseFactory;
use App\RuleEngine\Phases\Factory\OidlPhaseFactory;
use App\RuleEngine\Phases\Factory\OiqlPhaseFactory;
use App\RuleEngine\Phases\Factory\OiwlPhaseFactory;
use App\RuleEngine\Phases\Factory\OtclPhaseFactory;
use App\RuleEngine\Phases\Factory\OuplPhaseFactory;
use App\RuleEngine\Phases\Factory\OvlPhaseFactory;
use App\RuleEngine\Phases\Factory\PlPhaseFactory;
use App\RuleEngine\Phases\Factory\RemlPhaseFactory;
use App\RuleEngine\Phases\Factory\VlxPhaseFactory;
use App\RuleEngine\RuleContext;
use App\RuleEngine\RuleContextBuilder;
use App\RuleEngine\RuleEngine;
use App\Traits\Transaction;
use League\Pipeline\PipelineBuilder;

class RuleService implements IRuleService
{
    use Transaction;

    public function processRules($citizen, $loan_type_id): array
    {
        $context = $this->buildContext($citizen);

        $security_service = resolve('App\Interfaces\ISecurityService');
        $security_service->recordMetaOnce($context, constants('META_STEPS.CONTEXT_INFO'));

        $rule_set = RuleSet::getCurrent($loan_type_id);
        $rules = $rule_set->rules()->groupedByPhases();
        $phases = $rules->keys();

        $pipeline = $this->buildPipeline($phases, $loan_type_id);

        $rule_engine = new RuleEngine($context, $rules, $pipeline);
        $result = $rule_engine->run();

        $security_service->recordMetaOnce([], constants('META_STEPS.RULE_ENGINE'));

        return ['result' => $result, 'rule_set' => $rule_set];
    }

    public function buildContext($citizen)
    {
        $context_schema = RuleContext::getContextSchema();
        $builder = new RuleContextBuilder($citizen, $context_schema);
        $rule_context = $builder->buildContext();

        return $rule_context->getContext();
    }

    protected function buildPipeline($phases, $loan_type_id)
    {
        $factory = $this->getPhaseFactory($loan_type_id);

        $pipeline_builder = new PipelineBuilder();

        foreach ($phases as $phase) {
            $pipeline_builder->add($factory::build($phase));
        }

        return $pipeline_builder->build();
    }

    private function getPhaseFactory($loan_type_id)
    {
        if ($loan_type_id === constants('LOAN_TYPES.OCL')) {
            return OclPhaseFactory::class;
        }
        if ($loan_type_id === constants('LOAN_TYPES.OVL')) {
            return OvlPhaseFactory::class;
        }
        if ($loan_type_id === constants('LOAN_TYPES.OASL')) {
            return OaslPhaseFactory::class;
        }
        if ($loan_type_id === constants('LOAN_TYPES.OIQL')) {
            return OiqlPhaseFactory::class;
        }
        if ($loan_type_id === constants('LOAN_TYPES.OIDL')) {
            return OidlPhaseFactory::class;
        }
        if ($loan_type_id === constants('LOAN_TYPES.PL')) {
            return PlPhaseFactory::class;
        }
        if ($loan_type_id === constants('LOAN_TYPES.OTCL')) {
            return OtclPhaseFactory::class;
        }
        if ($loan_type_id === constants('LOAN_TYPES.OEPL')) {
            return OeplPhaseFactory::class;
        }
        if ($loan_type_id === constants('LOAN_TYPES.OUPL')) {
            return OuplPhaseFactory::class;
        }
        if ($loan_type_id === constants('LOAN_TYPES.REML')) {
            return RemlPhaseFactory::class;
        }
        if ($loan_type_id === constants('LOAN_TYPES.VLX')) {
            return VlxPhaseFactory::class;
        }
        if ($loan_type_id === constants('LOAN_TYPES.BNPL')) {
            return BnplPhaseFactory::class;
        }
        if ($loan_type_id === constants('LOAN_TYPES.OIWL')) {
            return OiwlPhaseFactory::class;
        }
        if ($loan_type_id === constants('LOAN_TYPES.OBL')) {
            return OblPhaseFactory::class;
        }
        if ($loan_type_id === constants('LOAN_TYPES.OFSL')) {
            return OfslPhaseFactory::class;
        }
    }

    public function isCreditRejected($credit): bool
    {
        return !is_null($credit['amount']) && $credit['amount'] == 0;
    }
}
