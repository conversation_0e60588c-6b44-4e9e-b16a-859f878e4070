<?php

namespace App\Services;

use App\Abstracts\AbstractStreamRecorderService;
use App\Exceptions\OpenTokException;
use App\Models\VideoArchive;
use Exception;
use Log;
use OpenTok\MediaMode;
use OpenTok\OpenTok;

class StreamRecorderService extends AbstractStreamRecorderService
{
    private $client = null;

    public function startSession()
    {
        try {
            $opentok = new OpenTok(env('TOKBOX_KEY'), env('TOKBOX_SECRET'));

            $session = $opentok->createSession(['mediaMode' => MediaMode::ROUTED]);
            $session_id = $session->getSessionId();

            Log::info('Create OpenTok session', ['session_id' => $session_id]);

            return [
                'session_id' => $session_id,
                'token' => $opentok->generateToken($session_id),
                'api_key' => env('TOKBOX_KEY'),
            ];
        } catch (Exception $e) {
            throw new OpenTokException();
        }
    }

    protected function getConnection()
    {
        if (!$this->client) {
            try {
                $this->client = new OpenTok(env('TOKBOX_KEY'), env('TOKBOX_SECRET'));
            } catch (Exception $e) {
                Log::error('Unable to connect to Tokbox', ['error' => $e->getMessage()]);
                throw new InternalErrorException();
            }
        }

        return $this->client;
    }

    public function startRecording($session_id)
    {
        try {
            $opentok = $this->getConnection();

            $archive_options = $this->getArchiveOptions();

            $archive = $opentok->startArchive($session_id, $archive_options);

            [
                'archive_id' => $archive_id,
                'resolution' => $resolution,
            ] = $this->getArchiveProperties($archive);

            Log::info('OpenTok archiving started', ['session_id' => $session_id, 'archive_id' => $archive_id]);

            $this->storeVideoArchive($archive_id, [
                'status' => VideoArchive::STARTED,
                'resolution' => $resolution,
                'path' => $this->composeVideoPath($archive),
            ]);
        } catch (Exception $e) {
            throw new OpenTokException();
        }
    }

    public function stopRecording($archive_id)
    {
        try {
            $archive = $this->getArchive($archive_id);

            if ($archive->status === constants('TOKBOX_VIDEO_STATUS.STARTED')) {
                $opentok = $this->getConnection();
                $opentok->stopArchive($archive_id);
            }

            Log::info('OpenTok archiving stoped', ['archive_id' => $archive_id]);
        } catch (Exception $e) {
            throw new OpenTokException();
        }
    }

    public function getArchive($archive_id)
    {
        $opentok = $this->getConnection();

        return $opentok->getArchive($archive_id);
    }
}
