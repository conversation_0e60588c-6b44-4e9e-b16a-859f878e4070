<?php

namespace App\Services;

use App\Abstracts\AbstractDocumentService;
use App\Helpers\NumberHelper;
use App\Jobs\GenerateLoanDocuments;
use App\Models\LoanDocument;

class LoanDocumentServiceOIDL extends AbstractDocumentService
{
    public function composePdfData($loan)
    {
        $pdf_data = parent::composePdfData($loan);

        $pdf_data_oidl = [
            'contract_number' => $loan->contract_number,
            'idram_id' => $loan->wallet_details->account_id,
            'sign_date' => $loan->sign_date->format('Y-m-d'),
            'rate_in_words' => NumberHelper::floatToText($loan->interest_rate),
        ];

        return array_merge($pdf_data, $pdf_data_oidl);
    }

    public function getDocumentTypes($obfuscated = null, $loan = null)
    {
        return array_merge(
            [
                LoanDocument::CONTRACT_OIDL,
                LoanDocument::WHAT_TO_DO,
                LoanDocument::PERSONAL_SHEET_OIDL,
                LoanDocument::ARBITRATION,
            ],
            $this->getDisputeSolution($loan->dispute_solution_method)
        );
    }

    public function getLoanDocumentsJob($loan)
    {
        return new GenerateLoanDocuments($loan);
    }
}
