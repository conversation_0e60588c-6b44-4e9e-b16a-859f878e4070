<?php

namespace App\Services;

use App\Exceptions\ClientApplicationNotificationOrderException;
use App\Exceptions\ExpiredPassportException;
use App\Exceptions\GCClientNotFoundException;
use App\Exceptions\InternalErrorException;
use App\Exceptions\UnmatchedEmailException;
use App\Exceptions\UnmatchedPassportException;
use App\Exceptions\UnmatchedPhoneNumberException;
use App\Helpers\NumberHelper;
use App\Helpers\PassportHelper;
use App\Jobs\SendClientApplicationEmail;
use App\Jobs\SendClientApplicationSms;
use App\Models\ClientApplication;
use App\Models\EkengRequest;
use Auth;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Support\Facades\Log;
use Throwable;

class ClientApplicationService
{
    protected $client;

    public function getConnection()
    {
        if (!$this->client) {
            $this->client = new Client(['verify' => false]);
        }

        return $this->client;
    }

    private function getHeaders($access_token): array
    {
        return [
            'Authorization' => "Bearer $access_token",
        ];
    }

    public function getAccessToken()
    {
        $redis_service = resolve('App\Interfaces\IRedisService');

        try {
            $cached_access_token = $redis_service->get('gc_application_access_token');

            if ($cached_access_token) {
                Log::info('GC Application access token retrieved from cache');

                return $cached_access_token;
            }

            $client = $this->getConnection();
            $response = $client->post(config('gc_application.url').'/login', [
                'form_params' => [
                    'username' => config('gc_application.username'),
                    'password' => config('gc_application.password'),
                ],
            ]);

            $result = json_decode($response->getBody()->getContents());

            if ($result && isset($result->access_token)) {
                $redis_service->set('gc_application_access_token', $result->access_token, constants('ONE_MONTH_IN_MINUTES'));

                Log::info('GC Application access token fetched');

                return $result->access_token;
            }

            Log::error('Unable to retrieve to GC Application access token', ['result' => $result]);

            throw new InternalErrorException();
        } catch (Throwable $e) {
            Log::critical('GC Application get access token exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            return null;
        }
    }

    public function getFormList()
    {
        $access_token = $this->getAccessToken();
        $redis_service = resolve('App\Interfaces\IRedisService');

        if (!$access_token) {
            Log::critical('GC Application access token is empty');

            return [];
        }

        try {
            $cached_form_list = $redis_service->get('gc_application_form_list');

            if ($cached_form_list) {
                Log::info('GC Application form list retrieved from cache', ['form_list' => $cached_form_list]);

                return $cached_form_list;
            }

            $client = $this->getConnection();
            $response = $client->get(config('gc_application.url').'/form-list', [
                'headers' => $this->getHeaders($access_token),
            ]);

            $form_list = json_decode($response->getBody()->getContents());

            $redis_service->set('gc_application_form_list', $form_list, constants('ONE_MONTH_IN_MINUTES'));

            Log::info('GC Application service form list fetched', ['form_list' => $form_list]);

            return $form_list;
        } catch (Throwable $e) {
            Log::critical('GC Application get form list exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw $e;
        }
    }

    public function register($data)
    {
        $access_token = $this->getAccessToken();

        $user = Auth::user();

        $warehouse_service = resolve('App\Interfaces\IWarehouseService');
        $client_info = $warehouse_service->getClientInfo($user->profile->ssn);
        $profile_info = $user->profile;

        $payload = [
            'formtype' => $data['formtype'],
            'data' => [
                'ssn' => $profile_info->ssn,
                'phone' => $profile_info->phone_number,
                'email' => $profile_info->email,
                'passport' => $profile_info->document_number,
                'first_name' => $profile_info->first_name,
                'last_name' => $profile_info->last_name,
                'middle_name' => $profile_info->middle_name,
            ],
        ];

        if (!empty($client_info) && !empty($client_info['current']['email'])) {
            $payload['data']['email'] = $client_info['current']['email'];
        }

        Log::info('GC Application register', ['access_token' => $access_token, 'payload' => $payload]);

        if (!$access_token) {
            return [
                'registered' => false,
            ];
        }

        if (is_dev()) {
            // GC Application give us this user for testing
            $payload['data'] = FakerService::getMockedApplicationPayload();
        }

        $client = $this->getConnection();

        try {
            $response = $client->post(config('gc_application.url').'/register', [
                'headers' => $this->getHeaders($access_token),
                'form_params' => $payload,
            ]);

            $result = json_decode($response->getBody()->getContents());

            Log::info('GC Application register response', ['result' => $result]);

            return $result;
        } catch (Throwable $e) {
            Log::critical('GC Application register exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw $e;
        }
    }

    public function check(array $payload, $client): string
    {
        try {
            $this->checkPhoneNumber($payload['phone_number'], $client['current']['phone']);

            $this->checkEmail($payload['email'], $client['current']['email']);

            $this->checkPassports($client['ekeng'], $client['current']['passport']);
        } catch (UnmatchedPhoneNumberException $e) {
            return ClientApplication::UNMATCHED_PHONE_NUMBER;
        } catch (UnmatchedEmailException $e) {
            return ClientApplication::UNMATCHED_EMAIL;
        } catch (UnmatchedPassportException $e) {
            return ClientApplication::UNMATCHED_PASSPORT;
        } catch (ExpiredPassportException $e) {
            return ClientApplication::EXPIRED_PASSPORT;
        }

        return ClientApplication::SUCCESS;
    }

    public function getGCClient($ssn, $client_application)
    {
        $warehouse_service = resolve('App\Interfaces\IWarehouseService');
        $client = $warehouse_service->getClientInfo($ssn);

        Log::info('Client GC Application DWH client', ['client' => $client]);

        if (empty($client)) {
            throw new GCClientNotFoundException();
        }

        $citizen = $this->getEkengData($ssn);
        $ekeng_request_pkey = EkengRequest::whereIdentifier($ssn)
            ->where('identifier_type', constants('SOC_CARD'))
            ->latest('id')
            ->value('id');
        $client_application->ekeng_request_id = $ekeng_request_pkey;
        $client_application->save();

        $client['current'] = array_merge($client['current'], PassportHelper::extractCitizenFullName($citizen['ekeng']));

        return array_merge($client, $citizen);
    }

    public function create($payload)
    {
        return ClientApplication::create($payload);
    }

    public function findByUuid($uuid)
    {
        return ClientApplication::whereUuid($uuid)->firstOrFail();
    }

    public function updateStatus($client_application, $status)
    {
        return $client_application->update([
            'status' => $status,
        ]);
    }

    public function canSendSms($application)
    {
        return $application->sent_email_count > 0;
    }

    public function sendSmsNotification($application, $content)
    {
        if ($this->canSendSms($application)) {
            SendClientApplicationSms::dispatch($application, $content);

            $application->update([
                'sent_sms_count' => ++$application->sent_sms_count,
            ]);

            return;
        }

        throw new ClientApplicationNotificationOrderException();
    }

    public function sendEmailNotification($application, $payload)
    {
        SendClientApplicationEmail::dispatch($application, $payload);

        $application->update([
            'sent_email_count' => ++$application->sent_email_count,
        ]);
    }

    protected function checkPassports($citizen, $passport)
    {
        $ekeng_passports = PassportHelper::getPassports($citizen);

        $result = PassportHelper::passportsComparison($passport, $ekeng_passports);
        // Updated ekeng passports does not match none of our system records
        if (empty($result) || empty($result['matched'])) {
            Log::info('There Are Unmatched Passport(s)');

            throw new UnmatchedPassportException();
        }
        // Updated ekeng passports matched our system records but there are expired passport(s)
        elseif (empty($result['valid'])) {
            Log::info('There Are Expired Passport(s)');

            throw new ExpiredPassportException();
        }
    }

    protected function checkPhoneNumber($phone_number, $client_phone_number)
    {
        // Submitted phone number does not match none of our system records
        if (NumberHelper::phoneMask($phone_number) !== $client_phone_number) {
            Log::info("There aren't matched phone number");

            throw new UnmatchedPhoneNumberException();
        }
    }

    protected function checkEmail($email, $client_email)
    {
        // Submitted email does not match none of our system records
        if (strtolower($email) !== strtolower($client_email)) {
            Log::info("There aren't matched email");

            throw new UnmatchedEmailException();
        }
    }

    protected function getEkengData($ssn)
    {
        $ekeng_service = resolve('App\Interfaces\IEkengService');

        $citizen['ekeng'] = $ekeng_service->getCitizen(constants('SOC_CARD'), $ssn);

        return $ekeng_service->cutEkengPhoto($citizen);
    }

    public function getApplicationStatus($application_number)
    {
        try {
            $client = $this->getConnection();
            $response = $client->get(config('gc_application.url').'/application-status/'.$application_number);

            $body = $response->getBody();
            $status = json_decode($body->getContents());

            Log::info('GC Application service application status fetched', ['status' => $status]);

            return $status;
        } catch (ClientException $e) {
            if ($e->getResponse()->getStatusCode() == 404) {
                Log::warning('GC Application status not found', ['application_number' => $application_number, 'message' => $e->getMessage()]);

                return json_decode($e->getResponse()->getBody()->getContents());
            }

            Log::critical('GC Application get form list exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        } catch (Throwable $e) {
            Log::critical('GC Application get form list exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        }
    }
}
