<?php

namespace App\Services;

use App\Interfaces\IIqosFtpService;
use Carbon\Carbon;
use function Functional\first;
use Illuminate\Support\Facades\Storage;

class IqosFtpService implements IIqosFtpService
{
    public function upload($is_success, $citizen, $contract_number = null, $amount = null)
    {
        $name = $this->composeName();
        $is_success = $is_success ? 'true' : 'false';
        $passport = first($citizen['passports'], function ($p) {
            return $p['primary'] == true;
        });

        $citizen_str = implode(' ', [
            $citizen['first_name'],
            $citizen['last_name'],
            $citizen['middle_name'],
            $passport['given_date'],
            $passport['from'],
        ]);

        $content = implode(';', array_filter([$is_success, $passport['passport_number'], $contract_number, $amount, $citizen_str]));

        Storage::disk('ftp_iqos')->put($name, $content, 'r+');
    }

    public function composeName()
    {
        $date = Carbon::now(constants('ARM_TIMEZONE'));

        return $date->format('Y_m_d_H_i_s_u').'.txt';
    }
}
