<?php

namespace App\Services;

use App\Exceptions\ExpiredPassportException;
use App\Exceptions\InvalidDocumentException;
use App\Exceptions\InvalidSSNException;
use App\Exceptions\UnknownDocumentTypeException;
use App\Factory\AcraServiceFactory;
use App\Helpers\ObjectMerger;
use App\Helpers\PassportHelper;
use App\Interfaces\IAggregatorService;
use App\Jobs\SendEmailAboutAcraRequest;
use App\Schemas\PersonalInfoSchema;
use Carbon\Carbon;
use Exception;
use Log;

class AggregatorService implements IAggregatorService
{
    protected $ekeng_service;
    protected $nork_service;

    public function __construct()
    {
        $this->ekeng_service = resolve('App\Interfaces\IEkengService');
        $this->nork_service = resolve('App\Interfaces\INorkService');
    }

    private function collectAggregateDataWithoutAcra($document_number)
    {
        $citizen['ekeng'] = $this->getEkengData($document_number);

        $citizen['nork'] = $this->nork_service->getCitizen($citizen['ekeng']['passport_data']);

        return $citizen;
    }

    public function getEkengData($document_number)
    {
        Log::debug('Determine document type', ['document_number' => $document_number]);
        $document_type = PassportHelper::determineDocumentType($document_number);
        Log::debug('Document type is', ['document_type' => $document_type]);

        try {
            $citizen = $this->ekeng_service->getCitizen($document_type, $document_number);

            if (in_array($document_type, [constants('PASSPORT'), constants('ID_CARD')])) {
                $avv_document = $citizen['passport_data']->AVVDocuments->AVVDocument ?? [];

                $docs = PassportHelper::getPassportFromList($avv_document, $document_number);
                if (!$docs || empty($docs)) {
                    Log::warning('Get ekeng data Expired Passport Exception', ['document_number' => $document_number]);

                    throw new ExpiredPassportException();
                }
            }

            return $citizen;
        } catch (UnknownDocumentTypeException $e) {
            $log_data = ['error' => $e->getMessage()];
            if (isset($citizen['passport_data']->AVVDocuments->AVVDocument)) {
                $log_data['AVVDocument'] = $citizen['passport_data']->AVVDocuments->AVVDocument;
            }
            Log::info('The passports data is not equals', $log_data);

            throw new InvalidDocumentException();
        } catch (ExpiredPassportException $e) {
            Log::info("Citizen's passport is expired", ['message' => $e->getMessage()]);

            throw $e;
        } catch (InvalidSSNException $e) {
            Log::info("Citizen's ssn is invalid", ['message' => $e->getMessage()]);

            throw $e;
        } catch (Exception $e) {
            Log::info("Can't get ekeng data", ['message' => $e->getMessage()]);

            throw $e;
        }
    }

    public function getNorkData($passport_data)
    {
        return $this->nork_service->getCitizen($passport_data);
    }

    public function getAcraData($ekeng_data, $report_type = null)
    {
        $acra_service = AcraServiceFactory::build(constants('ACRA.TYPE.PHYSICAL'), $report_type);

        $citizen = $acra_service->getCitizen(
            $ekeng_data['passport_data']->PNum,
            $ekeng_data['first_name'],
            $ekeng_data['last_name'],
            $ekeng_data['passport_data']
        );

        if (isset($citizen['cached']) && !$citizen['cached']) {
            $this->notifyAboutAcraRequest();
        }

        return $citizen;
    }

    public function getAcraBusinessMonitoringData($ekeng_data, $report_type = null)
    {
        $acra_service = AcraServiceFactory::build(constants('ACRA.TYPE.MONITORING'), $report_type);

        return $acra_service->getCitizen(
            $ekeng_data['passport_data']->PNum,
            $ekeng_data['first_name'],
            $ekeng_data['last_name'],
            $ekeng_data['passport_data']
        );
    }

    public function getAcraMonitoringData($ekeng_data, $report_type = null)
    {
        $acra_service = AcraServiceFactory::build(constants('ACRA.TYPE.MONITORING'));

        return $acra_service->getCitizen(
            $ekeng_data['passport_data']->PNum,
            $ekeng_data['first_name'],
            $ekeng_data['last_name'],
            $ekeng_data['passport_data']
        );
    }

    public function getEkengPhoto($document_number)
    {
        $citizen['ekeng'] = $this->getEkengData($document_number);

        return $citizen['ekeng']['passport_data']->Photo ?? null;
    }

    public function getSSN($document_number)
    {
        $citizen['ekeng'] = $this->getEkengData($document_number);

        return $citizen['ekeng']['SSN'] ?? null;
    }

    public function getBirthDate($document_number)
    {
        $citizen['ekeng'] = $this->getEkengData($document_number);
        $passport_data = json_decode(json_encode($citizen['ekeng']['passport_data']), true);

        if (isset($passport_data['AVVDocuments']['AVVDocument']['BirthDate'])) {
            $birth_date = $passport_data['AVVDocuments']['AVVDocument']['BirthDate'];

            return Carbon::createFromFormat(constants('EKENG_DATE_FORMAT'), $birth_date)->toDateTimeString();
        }

        if (isset($passport_data['AVVDocuments']['AVVDocument'][0])) {
            $birth_date = $passport_data['AVVDocuments']['AVVDocument'][0]['BirthDate'];

            return Carbon::createFromFormat(constants('EKENG_DATE_FORMAT'), $birth_date)->toDateTimeString();
        }

        return null;
    }

    public function composeAddress($document_number)
    {
        $citizen = $this->collectAggregateDataWithoutAcra($document_number);
        $schema = new PersonalInfoSchema();

        $personal_info_schema = $schema->get();
        $objectMerger = new ObjectMerger($personal_info_schema);
        $personal_info = $objectMerger->merge($citizen);

        if ($personal_info['region'] == 'ԵՐԵՎԱՆ') {
            $personal_info['city'] = 'ԵՐԵՎԱՆ';
        }

        return "ՀՀ, {$personal_info['region']}, {$personal_info['city']}, {$personal_info['street']}, {$personal_info['building']} {$personal_info['apartment']}";
    }

    public function notifyAboutAcraRequest()
    {
        $security_service = resolve('App\Interfaces\ISecurityService');
        $loan_security_email = optional($security_service->tryResolveLoanSecurity())->email;

        $email = $loan_security_email ?? auth()->user()->email ?? null;

        Log::info('Sending email about ACRA request', ['email' => $email]);

        if ($email) {
            $request_date = now(constants('ARM_TIMEZONE'));
            SendEmailAboutAcraRequest::dispatch($email, $request_date);
        }
    }
}
