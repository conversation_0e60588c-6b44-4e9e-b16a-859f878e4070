<?php

namespace App\Services\TopUp;

use App\Factory\SecurityServiceFactory;
use App\Jobs\SendTopUpPeriodicCreditOfferSMS;
use App\Jobs\SendTopUpPeriodicOfferNotification;
use App\Jobs\SendTopUpPeriodicOfferReminderNotification;
use App\Models\LoanSecurity;
use App\Models\Pallaton\Profile;
use App\Models\PreapprovedCreditOffer;
use App\Models\PreapprovedCreditOfferType;
use App\Models\TopUpSelection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Throwable;

class TopUpPeriodicCreditOfferService
{
    const CHUNK_SIZE = 100;

    public function processTopUpSelection()
    {
        TopUpSelection::with(['loan'])
            ->whereDate('selection_date', today())
            ->where('status', TopUpSelection::ACTIVE)
            ->whereHas('loan', function ($query) {
                $query->where('loan_type_id', constants('LOAN_TYPES.OCL'))
                    // Filter out converted BNPL loans
                    ->whereNull('prev_loan_type_id');
            })
            ->chunkById(self::CHUNK_SIZE, function ($records) {
                foreach ($records as $record) {
                    try {
                        Log::debug('ProcessTopUpSelection, started', ['contract_number' => $record->contract_number]);

                        $profile = Profile::getProfileBySsn($record->loan->loan_security->ssn);

                        if ($this->shouldPostponeLoan($record->loan, $profile, $record)) {
                            continue;
                        }

                        $this->handleOfferCreation($profile, $record->loan);

                        Log::debug('ProcessTopUpSelection, offer created', ['contract_number' => $record->contract_number]);
                    } catch (Throwable $e) {
                        Log::critical('ProcessTopUpSelection, calculation skipped',
                            [
                                'contract_number' => $record->contract_number,
                                'message' => $e->getMessage(),
                                'error' => $e->getTraceAsString(),
                            ]
                        );

                        continue;
                    }
                }
            });
    }

    private function shouldPostponeLoan($loan, $profile, $top_up_selection): bool
    {
        if (is_dev() && $loan->contract_number == 'M19-000004') {
            return true;
        }

        $checker = new TopUpEligibilityInternalChecker($loan, $profile, $top_up_selection);

        return $checker->run();
    }

    private function handleOfferCreation($profile, $loan)
    {
        $payload = $this->prepareLoanSecurityPayload($profile);

        $this->createOfferAndNotify($payload, $profile, $loan);
    }

    private function prepareLoanSecurityPayload($profile): array
    {
        return [
            // Mobile APP isn't supported when creating directly loan type as OCL
            'loan_type_id' => constants('LOAN_TYPES.COMMON'),
            'suuid' => generate_uuid(),
            'suuid_exp' => now()->addDays(constants('TEN_DAY')),
            'document_number' => $profile->document_number,
            'ssn' => $profile->ssn,
            'email' => $profile->email,
            'phone_number' => $profile->phone_number,
            'type' => constants('SECURITY_ENTRY.LOAN_APPLICATION_SYSTEM'),
            'user_id' => $profile->user_id,
        ];
    }

    private function createOfferAndNotify($payload, $profile, $loan)
    {
        $citizen_service_ocl = resolve('App\Services\CitizenServiceOCL');
        $top_up_service_ocl = resolve('App\Services\TopUpServiceOCL');

        $loan_security = LoanSecurity::create($payload);
        App::instance('App\Interfaces\ISecurityService', SecurityServiceFactory::build($payload['suuid']));

        $citizen = $citizen_service_ocl->collectCitizenData(['document_number' => $profile->ssn]);

        $security_service = resolve('App\Interfaces\ISecurityService');
        $security_service->recordMetaOnce($citizen, constants('META_STEPS.CITIZEN_INFO'));

        $citizen = $citizen_service_ocl->collectDataRobotScore($citizen);

        ['created_offer' => $created_offer] = $top_up_service_ocl->storeTopUpCreditOffer($citizen, PreapprovedCreditOfferType::TOP_UP_PERIODIC_OFFER);
        // Update next selection date
        $loan->top_up_selection()->update([
            'selection_date' => now()->addMonths(constants('TOP_UP_OFFER_PERIOD_IN_MONTHS')),
        ]);

        if (isset($created_offer)) {
            $created_offer->loan_security()->associate($loan_security);
            $created_offer->save();
        }

        if ($this->isCreditAvailable($created_offer)) {
            SendTopUpPeriodicOfferNotification::dispatch($profile, $created_offer);
            SendTopUpPeriodicCreditOfferSMS::dispatch($profile, $created_offer);
        } else {
            // expire created loan security to avoid unexpected cases
            $loan_security->update([
                'suuid_exp' => now(),
            ]);
        }
    }

    protected function isCreditAvailable($credit)
    {
        return $credit['amount'] != 0.0 && $credit['duration'] != 0.0;
    }

    public function topUpPeriodicOffersReminder()
    {
        PreapprovedCreditOffer::with(['loan_security'])
            ->validTopUpOffer(constants('LOAN_TYPES.OCL'), PreapprovedCreditOfferType::TOP_UP_PERIODIC_OFFER)
            ->whereDate('expiration_date', '=', now()->addDays(2)) // Checks if expiration is in exactly 2 days
            ->where('rejected', '=', false)
            ->chunkById(self::CHUNK_SIZE, function ($offers) {
                foreach ($offers as $offer) {
                    try {
                        $profile = Profile::getProfileBySsn($offer->loan_security->ssn);
                        if (is_null($profile)) {
                            Log::warning('Top Up Periodic Offer Reminder skipped, profile does not exist', ['offer_id' => $offer->id, 'ssn' => $offer->loan_security->ssn]);
                            continue;
                        }

                        SendTopUpPeriodicOfferReminderNotification::dispatch($profile, $offer);
                    } catch (Throwable $e) {
                        Log::error('topUpPeriodicOffersReminder, failed',
                            [
                                'offer_id' => $offer->id,
                                'message' => $e->getMessage(),
                                'error' => $e->getTraceAsString(),
                            ]
                        );

                        continue;
                    }
                }
            });
    }
}
