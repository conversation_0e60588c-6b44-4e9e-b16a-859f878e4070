<?php

namespace App\Services;

use App\Abstracts\AbstractDocumentService;
use App\Jobs\GenerateLoanDocuments;
use App\Models\LoanDocument;
use function Functional\pluck;

class LoanDocumentServiceOIQL extends AbstractDocumentService
{
    public function composePdfData($loan)
    {
        $pdf_data = parent::composePdfData($loan);

        // TODO: Provide OIQL specific data
        $pdf_data_oiql = [
            'application_name' => lang('application.name_OIQL'),
            'mortgage_exist' => lang('personal_sheet.mortgage_exist_OIQL'),
            'provision_fee' => '',
            'encashment_fee' => '',
            'other_fee' => '',
            'pledge_valuation' => '',
            'iqos_name' => $loan->pipe->pipe_type->contract_name,
        ];

        return array_merge($pdf_data, $pdf_data_oiql);
    }

    public function getDocumentTypes($obfuscated = null, $loan = null)
    {
        return array_merge([
                LoanDocument::CONTRACT_OIQL,
                LoanDocument::PERSONAL_SHEET_OIQL,
                LoanDocument::APPLICATION_OIQL,
                LoanDocument::ARBITRATION,
            ],
            $this->getDisputeSolution($loan->dispute_solution_method),
            parent::getDocumentTypes($obfuscated, $loan)
        );
    }

    public function getDocumentsForRemove($obfuscated = null, $loan = null)
    {
        // Sine we can't get previously selected arbitration document name,
        // we add all types in deleting list
        // In case of OIQL we generate documents ones, so we don't need check $obfuscated
        $documents = array_merge(
            $this->getDocumentTypes($obfuscated, $loan),
            [
                LoanDocument::GNM,
                LoanDocument::UBA,
                LoanDocument::OPTIMUS_LEX,
            ]
        );

        return pluck($documents, 'name');
    }

    public function getLoanDocumentsJob($loan)
    {
        return new GenerateLoanDocuments($loan);
    }
}
