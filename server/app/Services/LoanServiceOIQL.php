<?php

namespace App\Services;

use App\Abstracts\AbstractLoanService;
use App\Jobs\GenerateLawyerDocument;
use App\Jobs\SendConfirmationSMS;
use App\Models\PipeType;
use App\Traits\Transaction;
use Log;

class LoanServiceOIQL extends AbstractLoanService
{
    use Transaction;

    public function __construct()
    {
        parent::__construct(constants('LOAN_TYPES.OIQL'));
    }

    protected function insertLoan($loan_security, $details, $schedule)
    {
        return $this->startTransaction(function () use ($loan_security, $details, $schedule) {
            $loan = parent::insertLoan($loan_security, $details, $schedule);

            $loan->pipe()->create([
                'seller_id' => auth('api')->user()->id,
                'pipe_type_id' => $details['pipe_type_id'],
            ]);

            return $loan;
        });
    }

    public function processLoanConfirmation($loan)
    {
        $this->validateConfirmation($loan);

        $this->expireLoan($loan);

        $this->processLoanPayment($loan);

        $this->saveLoanToHC($loan);

        Log::info('Send confirmation SMS');
        SendConfirmationSMS::dispatch($loan);

        Log::info('Generating lawyer document');

        GenerateLawyerDocument::dispatch($loan);

        // Upload loan info to remote ftp server
        Log::info('Uploading to IQOS FTP');
        $ftpService = resolve('App\Interfaces\IIqosFtpService');
        $ftpService->upload(true, $loan->citizen->toArray(), $loan->contract_number, $loan->amount);
        Log::info('Uploading to IQOS FTP success');
    }

    public function getPipeTypes()
    {
        return PipeType::where('disabled', false)->get();
    }
}
