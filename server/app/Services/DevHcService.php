<?php

namespace App\Services;

use App\Interfaces\IHcService;
use Config;
use function Functional\first;

/**
 * Armsoft requires vpn connection, hence we use fake service in dev.
 */
class DevHcService implements IHcService
{
    private $loan_type = null;

    public function __construct($loan_type)
    {
        $this->loan_type = $loan_type;
    }

    public function hasNonSyncedCredits($soc_card)
    {
        return false;
    }

    public function save($loan)
    {
        return;
    }

    public function storeLoanSupportSchedule($loan, $schedule)
    {
        return;
    }

    public function storeTransaction($transaction)
    {
        return;
    }

    public function updateTransaction($transaction, $amount)
    {
        return;
    }

    public function getHcGCClientBySocCard($soc_card)
    {
        return null;
    }

    public function hasCredit($soc_card)
    {
        return false;
    }

    public function hasCreditByLoan($loan)
    {
        return false;
    }

    public function getWalletLoansCount($soc_card)
    {
        return 0;
    }

    public function hasNonSyncedDisbursement($contract_number)
    {
        return false;
    }

    public function hasNonSyncedPayment($contract_number)
    {
        return false;
    }

    public function generateContractNumber($loan_type_id, $date)
    {
        if ($this->loan_type === constants('LOAN_TYPES.OVL')) {
            return 'V19-000004';
        }

        if ($this->loan_type === constants('LOAN_TYPES.PL')) {
            return 'P19-000004';
        }

        if ($this->loan_type === constants('LOAN_TYPES.BNPL')) {
            return 'H19-000004';
        }

        return 'M19-000004';
    }

    public function generateMortgageOuterCode($loan_contract_number, $date)
    {
        if ($this->loan_type === constants('LOAN_TYPES.OVL')) {
            return 'G19-V00004';
        }

        return 'G19-S00004';
    }

    public function getCreditCodeAndOuterId($ssn, $loan_type_id)
    {
        return [
            'credit_code' => '635005000010L001',
            'outer_id' => '500001',
        ];
    }

    public function updateHCEmail($soc_card, $email)
    {
    }

    public function updateLoanDisbursement($loan)
    {
        return;
    }

    public function getBalance($contract_number)
    {
        return 1083620.00;
    }

    public function getCreditRegisterCodeNew($contract_number)
    {
        return '63500-20200818-006483';
    }

    public function getCreditBalance($loan)
    {
        $debt = $loan->transactions->sum('amount') ?? 0;
        $amount = $loan->amount - $debt;

        return [
            'debt' => $debt,
            'amount' => max(0, $amount),
            'overdue_fee' => 0,
        ];
    }

    public function oclClosedCredits($soc_card)
    {
        $fake_citizens = Config::get('fake_citizens');

        $citizen = first($fake_citizens, function ($citizen) use ($soc_card) {
            if ($citizen['SSN'] === $soc_card) {
                return $citizen;
            }
        });

        // This is a fake data for checking customer is new or old
        if (!isset($citizen['isNewOclCustomer'])) {
            $fake_loans = FakerService::getMockedHcLoans();

            return collect([$fake_loans]);
        }

        return null;
    }

    public function ovlClosedCredits($soc_card)
    {
        return null;
    }

    public function veloxClosedCredits($soc_card)
    {
        return null;
    }

    public function storeLoansCredPayment($contract_number, $amount, $vendor_acc_number)
    {
    }

    public function clientHasCreditLine($ssn): bool
    {
        return true;
    }

    public function storeLoanDisbursementFee($loan)
    {
        return;
    }

    public function storeLoanSchedule($loan_outer_id, $loan)
    {
    }

    public function storeLoanConversionToHC($loan_outer_id, $loan)
    {
    }

    public function storeLoansCredLimit($loan_outer_id, $debt, $date)
    {
        return null;
    }

    public function saveTopUp($loan)
    {
    }

    public function getOrCreateClientId($ssn, $loan_type_id)
    {
        return '500001';
    }

    public function storeMortgageDetails($loan, $loan_outer_id, $client_outer_id)
    {
        return;
    }

    public function hasActiveOclOrWallet($soc_card): bool
    {
        return false;
    }

    public function extractCitizenFromEkengData($soc_card): array
    {
        return [];
    }
}
