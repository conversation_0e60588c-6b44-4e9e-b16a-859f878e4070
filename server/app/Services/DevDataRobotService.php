<?php

namespace App\Services;

use App\Abstracts\AbstractDataRobotService;
use App\Models\LoanSecurity;

class DevDataRobotService extends AbstractDataRobotService
{
    protected function predict(int $application_id, string $model, array $details = null): array
    {
        $loan_security = LoanSecurity::find($application_id);
        $fake_citizen = FakerService::getMockedCitizen($loan_security->document_number);
        $dr_score = $fake_citizen['drScore'];
        $dr_data = ['data' => [[
            'predictionValues' => [
                ['value' => $dr_score, 'label' => 1],
                ['value' => $dr_score, 'label' => 0]
            ],
            'deploymentApprovalStatus' => 'APPROVED',
            'predictionThreshold' => 0.5,
            'prediction' => 0,
            'rowId' => 0
        ]]];

        return ['prediction'  => json_encode($dr_data)];
    }
}
