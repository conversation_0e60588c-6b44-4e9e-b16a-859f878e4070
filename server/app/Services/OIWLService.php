<?php

namespace App\Services;

use App\Abstracts\AbstractOWLService;
use Log;

class OIWLService extends AbstractOWLService
{
    public function getWalletDuration(): int
    {
        $loan_config_service = resolve('App\Services\LoanConfigService');
        $loan_configs = $loan_config_service->getConfigs(constants('LOAN_TYPES.OIWL'));

        return $loan_configs['max_months'];
    }

    protected function requestWalletInfo($token, $suuid)
    {
        return [];
    }

    public function transfer($token, $amount, $agreement_id, $suuid)
    {

    }

    protected function getLoanConfigs($loan)
    {
        $loan_config_service = resolve('App\Services\LoanConfigService');

        return $loan_config_service->getConfigs(constants('LOAN_TYPES.OIWL'));
    }

    protected function getWalletKey()
    {
        return 'oiwl';
    }
}
