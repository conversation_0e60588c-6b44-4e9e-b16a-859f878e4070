<?php

namespace App\Services;

use App\Helpers\DateHelper;
use App\Models\Loan;
use App\Models\LoanConfig;
use App\Models\LoanType;
use Carbon\Carbon;
use function Functional\reduce_left;

class LoanConfigService
{
    public function getConfigs($type_id)
    {
        switch ($type_id) {
            case constants('LOAN_TYPES.COMMON'):
                return $this->getCommonConfigs();
            case constants('LOAN_TYPES.OBL'):
                return $this->getOblConfigs();
            default:
                return $this->getConfigsByType($type_id);
        }
    }

    protected function getCommonConfigs()
    {
        return array_merge($this->getConfigsByType(constants('LOAN_TYPES.COMMON')), [
            'ocl' => $this->getConfigsByType(constants('LOAN_TYPES.OCL')),
            'ovl' => $this->getConfigsByType(constants('LOAN_TYPES.OVL')),
            'oiwl' => $this->getConfigsByType(constants('LOAN_TYPES.OIWL')),
        ]);
    }

    protected function getOblConfigs()
    {
        return ['obl' => $this->getConfigsByType(constants('LOAN_TYPES.OBL'))];
    }

    public function getConfigsByType($type_id)
    {
        $loan_type = LoanType::withLoanConfigs($type_id);

        $adjusted_configs = $this->adjustConfigs($loan_type->loan_configs);
        $extra_configs = [];
        if ($type_id == constants('LOAN_TYPES.OIWL')) {
            $extra_configs['wallet_available_amounts'] = constants('WALLET_AVAILABLE_AMOUNTS');
        }

        return array_merge($adjusted_configs, $extra_configs);
    }

    public function getConfigsByLoan($loan)
    {
        $configs = $loan->isConverted() ? $loan->prev_loan_type->loan_configs : $loan->loan_type->loan_configs;

        return $this->adjustConfigs($configs);
    }

    public function adjustConfigs($loan_configs)
    {
        return reduce_left($loan_configs, function ($row, $index, $collection, $reduction) {
            return array_merge($reduction, [$row->key => $row->value]);
        }, []);
    }

    public function getLoanSummary($type_id)
    {
        [
            'night_start' => $night_start,
            'night_end' => $night_end,
            'min_amount' => $min_amount,
            'max_amount' => $max_amount,
        ] = LoanConfig::getConfigs($type_id);

        $total = Loan::getDailyTotal($type_id, $night_start, $night_end);
        $total_night = Loan::getDailyNightTotal($type_id, $night_start, $night_end);

        return [
            'totalAmount' => $total->amount,
            'totalCount' => $total->count,
            'totalNightAmount' => $total_night->amount,
            'totalNightCount' => $total_night->count,
            'isWeekend' => DateHelper::isWeekend(Carbon::now(constants('ARM_TIMEZONE'))),
            'isNight' => DateHelper::isNight($night_start, $night_end, constants('ARM_TIMEZONE')),
            'minAmount' => $min_amount,
            'maxAmount' => $max_amount,
        ];
    }
}
