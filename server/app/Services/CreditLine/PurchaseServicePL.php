<?php

namespace App\Services\CreditLine;

use App\Abstracts\AbstractPurchaseService;
use App\Exceptions\CreditLine\DailyPurchaseLimitExceededException;
use App\Exceptions\CreditLine\MinimumAmountException;
use App\Exceptions\CreditLine\NonSyncPaymentException;
use App\Exceptions\CreditLine\NonSyncTransactionException;
use App\Exceptions\CreditLine\TransactionCancelationAmountException;
use App\Exceptions\CreditLine\TransactionCancelationDateException;
use App\Models\CreditLine\Transaction as TransactionModel;
use Carbon\Carbon;
use Log;

class PurchaseServicePL extends AbstractPurchaseService
{
    public function __construct()
    {
        parent::__construct(constants('CREDIT_LINE.PL_PROJECT_START_YEAR'));
    }

    public function createSession($payload)
    {
        $purchase_session = parent::createSession($payload);

        $this->associatePurchaseWithVendor($purchase_session);

        return $purchase_session;
    }

    public function makePurchase($payment_id)
    {
        $transaction = parent::makePurchase($payment_id);

        $this->storeCreditToHC($transaction);
        $this->storeTransactionToHC($transaction);

        $purchase_session = $this->getPurchaseSession($payment_id);
        $this->validateSession($purchase_session);

        return $this->composeSuccessCallback($purchase_session);
    }

    private function todaysTransactionsSum($vendor_id)
    {
        return TransactionModel::where('vendor_id', $vendor_id)
            ->whereDate('paid', Carbon::today())
            ->sum('amount');
    }

    public function validatePurchase($purchase_session, $balance)
    {
        $vendor = $purchase_session->vendor;

        if ($purchase_session->amount < $vendor->transaction_min_amount) {
            throw new MinimumAmountException();
        }

        if ($this->todaysTransactionsSum($vendor->id) >= $vendor->limit) {
            throw new DailyPurchaseLimitExceededException();
        }

        parent::validatePurchase($purchase_session, $balance);
    }

    public function composeFailureCallback($purchase_session, $status_code)
    {
        $status_code = $status_code ?: config('error_codes.CREDIT_LINE.PUBLIC.SYSTEM_ERROR');

        return $this->composeCallback($purchase_session, $status_code);
    }

    protected function createTransaction($purchase_session, $loan, $schedule, $current_schedule, $debt)
    {
        $transaction = parent::createTransaction($purchase_session, $loan, $schedule, $current_schedule, $debt);

        // Attach vendor
        $vendor = $purchase_session->vendor;
        $transaction->vendor()->associate($vendor);
        $transaction->update();

        return $transaction;
    }

    public function getTransactions($from, $to)
    {
        $transactions = parent::getTransactions($from, $to);

        return $transactions->with('vendor', 'canceled_transactions')->get();
    }

    public function cancelPayment($order_id, $amount, $reason)
    {
        Log::info('Canceling transaction', [
            'order_id' => $order_id,
            'amount' => $amount,
        ]);

        $transaction = TransactionModel::where('order_id', $order_id)->first();
        $existing_schedule = $this->getExistingSchedule($transaction->loan);
        Log::info('Cancel payment', ['existing schedule' => $existing_schedule]);

        $debt = $this->calculateCurrentDebt($existing_schedule);
        Log::info('Cancel payment', ['calculated debt' => $debt]);

        $this->validateCanceledTransaction($transaction, $amount, $debt);

        $this->startTransaction(function () use ($transaction, $existing_schedule, $amount, $reason, $debt) {
            $transaction->canceled_transactions()->create([
                'order_id' => $transaction->order_id,
                'amount' => $amount,
                'reason' => $reason,
                'date' => now(),
                'debt' => $debt - $amount,
            ]);

            $last_transaction = TransactionModel::getLastTransaction($transaction->loan_id);

            $this->regenerateSchedule($transaction, $last_transaction, $existing_schedule, $amount, $debt);

            $this->hc_service->updateTransaction($transaction, $last_transaction, $amount);
        });

        Log::info('Credit Line transaction canceled successfully', [
            'transaction_id' => $transaction->id,
            'amount' => $amount,
            'reason' => $reason,
        ]);
    }

    protected function validateCanceledTransaction($transaction, $amount, $debt)
    {
        if ($amount > $debt) {
            Log::info('Canceled transaction amount exceeds the balance usage');
            throw new TransactionCancelationAmountException('Canceled transaction amount exceeds the balance usage');
        }

        $total_canceled_amount = $transaction->canceled_amount;

        if ($total_canceled_amount + $amount > $transaction->amount) {
            Log::info('Canceled transaction amount exceeds the transaction amount');
            throw new TransactionCancelationAmountException();
        }

        $vendor = $transaction->vendor;

        if (Carbon::now()->subDays($vendor->cancelation_day_limit)->isAfter($transaction->paid)) {
            Log::info('Transaction cancelation date expired');
            throw new TransactionCancelationDateException();
        }

        $has_non_synced_transfer = $this->hc_service->hasNonSyncedDisbursement($transaction->loan->contract_number);

        if ($has_non_synced_transfer) {
            Log::info('There are unsynced transactions in HC');
            throw new NonSyncTransactionException();
        }

        $has_non_synced_payment = $this->hc_service->hasNonSyncedPayment($transaction->loan->contract_number);

        if ($has_non_synced_payment) {
            Log::info('There are unsynced canceled transactions in HC');
            throw new NonSyncPaymentException();
        }
    }

    public function composeRegenerateData($transaction)
    {
        $vendor = $transaction->vendor;

        $data = parent::composeRegenerateData($transaction);

        return array_merge($data, ['duration' => $vendor->duration]);
    }

    public function composePaymentStatusData($purchase_session)
    {
        $data = parent::composePaymentStatusData($purchase_session);

        return array_merge($data, [
            'vendor' => $purchase_session->vendor->name,
        ]);
    }

    protected function composeCallbackBody($order_id, $payment_id, $error_code)
    {
        $body = [
            'success' => 1,
            'error_code' => 0,
            'order_id' => $order_id,
            'payment_id' => $payment_id,
        ];

        if ($error_code !== null) {
            $body['success'] = 0;
            $body['error_code'] = $error_code;
        }

        return $body;
    }

    public function composeSuccessCallback($purchase_session)
    {
        return $this->composeCallback($purchase_session);
    }

    protected function composeCallback($purchase_session, $status_code = null)
    {
        $this->setPaymentStatus($purchase_session, $status_code);

        $this->expireSession($purchase_session);

        $body = $this->composeCallbackBody($purchase_session->order_id, $purchase_session->payment_id, $status_code);

        $callback = array_merge($body, [
            'url' => $purchase_session->callback_url,
        ]);

        Log::info('Callback', ['callback' => $callback]);

        return $callback;
    }
}
