<?php

namespace App\Services\CreditLine;

use App\Abstracts\AbstractCreditLineHcService;
use App\Interfaces\IHcService;
use App\Models\CreditLine\PurchaseOrder;
use App\Models\LoanTypeHcNote;
use App\Services\SecurityUtilityService;

class HcServiceBNPL extends AbstractCreditLineHcService implements IHcService
{
    const LOAN_NOTE2 = '1C2';
    const LOAN_CONVERSION_NOTE2 = '1C3';

    protected function resolveNote2($loan)
    {
        return self::LOAN_NOTE2;
    }

    protected function getOuterCodeType()
    {
        return constants('HC.LOAN_OUTER_CODE_TYPE_BNPL');
    }

    protected function clientHasCredit($hc_gc_client, $config)
    {
        return false;
    }

    protected function getAccountNumber($transaction)
    {
        if (SecurityUtilityService::isInternalBNPLVendor($transaction->vendor->type) || SecurityUtilityService::isArmedVendor($transaction->vendor->type)) {
            return $this->resolveAccountNumberViaMerchant($transaction);
        }

        return $transaction->vendor->account_number;
    }

    private function resolveAccountNumberViaMerchant($transaction)
    {
        $purchase_order = PurchaseOrder::getApprovedByOrderId($transaction->order_id)->first();

        return $purchase_order->merchant->account_number;
    }

    public function storeLoanUpdate($loan, $updatable_attrs = [])
    {
        parent::storeLoanUpdate($loan, [
            'NOTE2' => LoanTypeHcNote::getExactNote2(constants('LOAN_TYPES.OCL'), self::LOAN_CONVERSION_NOTE2),
        ]);
    }
}
