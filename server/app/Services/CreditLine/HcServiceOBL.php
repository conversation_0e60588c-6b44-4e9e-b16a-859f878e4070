<?php

namespace App\Services\CreditLine;

use App\Abstracts\AbstractCreditLineHcService;
use App\Interfaces\IHcService;
use App\Models\HC\HcGCCLIENTS;
use App\Models\HC\HcLoansCred2;
use App\Models\HC\HcLoansCred2Schedules;
use function Functional\reduce_left;
use Log;

class HcServiceOBL extends AbstractCreditLineHcService implements IHcService
{
    const LOAN_NOTE2 = '205';
    const DURATION_IN_MONTHS = 3;

    protected function resolveNote2($loan)
    {
        return self::LOAN_NOTE2;
    }

    protected function getOuterCodeType()
    {
        return constants('HC.LOAN_OUTER_CODE_TYPE_OBL');
    }

    protected function clientHasCredit($hc_gc_client, $config)
    {
        return false;
    }

    public function getHcGCClientBySocCard($soc_card)
    {
        return HcGCCLIENTS::getLegalEntityBySocCard($soc_card);
    }

    protected function composeLoan($loan, $client_outer_id)
    {
        $end_date = carbon_parse($loan->sign_date)->addMonths(self::DURATION_IN_MONTHS)->format(constants('OBL_END_DATE_FORMAT'));
        $this->credit_line_end_date = carbon_parse($loan->sign_date)->addMonths(self::DURATION_IN_MONTHS);
        $business_info = $loan->business;

        return array_merge(
            parent::composeLoan($loan, $client_outer_id),
            $this->composeLoanRegion($business_info),
            [
                'SUMMA' => $loan->amount,
                'PCAGR' => $loan->interest_rate,
                'DATELNGEND' => $end_date,
                'DATEAGR' => $end_date,
                'PCLOSS' => $loan->interest_rate,
                'SDATE' => $loan->sign_date,
            ]);
    }

    protected function composeClient($citizen, $defaults, $hcGCClient = null)
    {
        $business_info = $citizen->loan->business;

        return array_merge(
            parent::composeClient($citizen, $defaults, $hcGCClient),
            $this->composeOrgName($citizen),
            $this->composeOrgAddress($business_info),
            [
                'TAXCODE' => $business_info->tax_identifier,
                'DATEREGN' => $business_info->reg_date,
                'FIRSTNAME' => $citizen->first_name,
                'EFIRSTNAME' => $citizen->first_name_en,
                'LASTNAME' => $citizen->last_name,
                'ELASTNAME' => $citizen->last_name_en,
                'PATRNAME' => $citizen->middle_name,
                'EPATRNAME' => $citizen->middle_name_en,
        ]);
    }

    protected function getAccountNumber($transaction)
    {
    }

    protected function composeLoanRegion($business_info)
    {
        $regions = $this->getRegionCodes($business_info);

        if (empty($regions)) {
            return [];
        }

        return [
            'LRDISTR' => $regions['region_codes']['DISTRICT'],
            'REGION' => $regions['region_codes']['REGION'],
        ];
    }

    private function composeOrgName($citizen)
    {
        return [
            'NAME' => $citizen->last_name.' '.$citizen->first_name.' '.$citizen->middle_name.' ԱՁ',
            'ENAME' => $citizen->last_name_en.' '.$citizen->first_name_en.' '.$citizen->middle_name_en.' IE',
        ];
    }

    public function getRegionCodes($business_info)
    {
        $location_cdoe = array_collapse(constants('HC.HC_REGIONS'))[$business_info['region']] ?? null;

        if (
            is_null($location_cdoe) ||
            empty($business_info['region']) ||
            empty($business_info['city']) ||
            empty($business_info['community']) ||
            empty($business_info['address'])) {
            Log::info('Can not resolve address', ['business_info' => $business_info]);

            return [];
        }

        $region_codes = $this->resolveRegion($location_cdoe['REGION'], $business_info['region'], $business_info['community']);
        $hc_code = $this->resolveLocation($location_cdoe['REGION'], $business_info['region'], $business_info['community']);

        return [
            'region_codes' => $region_codes,
            'hc_code' => $hc_code,
        ];
    }

    public function composeOrgAddress($business_info)
    {
        $regions = $this->getRegionCodes($business_info);

        if (empty($regions)) {
            return [];
        }

        return [
            'COMMUNITY2' => $regions['hc_code'],
            'DISTRICT2' => $regions['region_codes']['DISTRICT'],
            'APARTMENT2' => $business_info['apartment'],
            'CITY2' => $business_info->city,
            'ADDRESS2' => $business_info->address,
            'COUNTRY2' => 'AM',
        ];
    }

    protected function insertLoansSchedule($generated_schedule)
    {
        HcLoansCred2Schedules::insert($generated_schedule);
    }

    protected function insertLoansCred($hc_loan)
    {
        HcLoansCred2::create($hc_loan);
    }

    protected function prepareLoansScheduleProperties($custom_schedule, $loan_outer_id)
    {
        return reduce_left($custom_schedule, function ($s, $i, $schedule, $acc) use ($loan_outer_id) {
            // Bulk insert doesn't use Eloquent features,
            // hence we need to format the date here
            $p = [
                'OUTER_CODE' => $loan_outer_id,
                'CODE' => $loan_outer_id,
                'DATE' => $s['date']->format(constants('CREDIT_LINE.SCHEDULE_DATE_FORMAT')),
            ];

            array_push($acc, array_merge(
                [
                    'SUMMA' => 0.0,
                    'TYPE' => 1,
                ], $p
            ), array_merge(
                [
                    'SUMMA' => 0.0,
                    'TYPE' => 2,
                ], $p
            ));

            return $acc;
        }, []);
    }

    protected function composeMeta($loan)
    {
        return [
            'SDATE' => $loan->sign_date,
            'PERIODICITY' => '1/0',
        ];
    }
}
