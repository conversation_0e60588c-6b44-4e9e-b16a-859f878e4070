<?php

namespace App\Services\CreditLine;

use App\Abstracts\AbstractCreditLineDocumentService;
use App\Helpers\ArrayHelper;
use App\Helpers\DateHelper;
use App\Helpers\NumberHelper;
use App\Models\LoanDocument;
use function Functional\pluck;

class DocumentServiceOBL extends AbstractCreditLineDocumentService
{
    const CONTRACT_END_DATE = 10;

    public function composePdfData($loan)
    {
        $citizen_data = $this->getCitizenData($loan);
        $pdf_data = parent::composePdfData($loan);
        $tax_identifier = $loan->business->tax_identifier ?? null;

        $citizen_data = array_merge(
            $citizen_data,
            ArrayHelper::pick($pdf_data, ['contract_number', 'credit_code']),
            ['tax_identifier' => $tax_identifier]
        );

        $citizen_private = $this->makePrivate($citizen_data);

        $loan_config_service = resolve('App\Services\LoanConfigService');
        $loan_configs = $loan_config_service->getConfigsByType(constants('LOAN_TYPES.OBL'));

        $provision_fee = $loan_configs["transaction_fee_" . $loan->amount];
        $service_fee = $loan_configs['service_fee_rate'];
        $total = $loan->amount + $service_fee + $provision_fee;
        $other_fee_total = $provision_fee + $service_fee;
        $end_date = now()->addMonths($loan_configs['duration']);
        $last_payment_date = now()->addYears(self::CONTRACT_END_DATE);

        $pdf_data_credit_line = [
            'application_name' => trans('lang.application.name_OBL', [], 'hy'),
            'months' => $loan_configs['duration'],
            'max_amount' => $loan_configs['max_amount'],
            'max_amount_in_words' => NumberHelper::numberInWords($loan_configs['max_amount']),
            'interest_rate_in_words' => NumberHelper::numberInWords($loan_configs['interest_rate']),
            'interest_rate' => $loan_configs['interest_rate'],
            'provision_fee' => lang('personal_sheet.provision_fee_OBL', ['fee' => NumberHelper::numberToStringDram($provision_fee)]),
            'mortgage_exist' => lang('personal_sheet.mortgage_exist_OBL', []),
            'service_fee' => lang('personal_sheet.service_fee_OBL', ['fee' => NumberHelper::numberToStringDram($loan_configs['service_fee_rate'])]),
            'other_fee_total' => $other_fee_total,
            'total' => $total,
            'link' => $loan_configs['link'],
            'tax_identifier' => $tax_identifier,
            'last_payment_date' => DateHelper::parseDate($last_payment_date),
            'end_date' => DateHelper::parseDate($end_date),
        ];

        return array_merge($citizen_private, $pdf_data, $pdf_data_credit_line);
    }

    public function getDocumentTypes($obfuscated = null, $loan = null)
    {
        $documents = [
            LoanDocument::CONTRACT_OBL,
            LoanDocument::PERSONAL_SHEET_OBL,
            LoanDocument::APPLICATION,
            LoanDocument::ARBITRATION_OBL,
            LoanDocument::CONTRACT_OBL_PRIVATE,
            LoanDocument::PERSONAL_SHEET_OBL_PRIVATE,
            LoanDocument::APPLICATION_PRIVATE,
        ];

        return array_merge(
            $documents,
            parent::getDocumentTypes(),
            $this->getDisputeSolution($loan->dispute_solution_method)
        );
    }

    public function getDisputeSolution($dispute_solution_method)
    {
        switch ($dispute_solution_method) {
            case constants('ARBITRATION.OPTIMUS'):
                return [
                    LoanDocument::OBL_OPTIMUS_LEX,
                ];
            case constants('ARBITRATION.GNM'):
                return [
                    LoanDocument::OBL_GNM,
                ];
            case constants('ARBITRATION.UBA'):
                return [
                    LoanDocument::OBL_UBA,
                ];
            default:
                return [];
        }
    }

    public function getDocumentsForRemove($obfuscated = true, $loan = null)
    {
        // Sine we can't get previously selected arbitration document name,
        // we add all types in deleting list
        $documents = [
            LoanDocument::OBL_GNM,
            LoanDocument::OBL_UBA,
            LoanDocument::OBL_OPTIMUS_LEX,
        ];

        $documents = array_merge(
            $documents,
            $this->getDocumentTypes($obfuscated, $loan)
        );

        return pluck($documents, 'name');
    }

    public function getTempPrivateDocs($loan = null)
    {
        return array_merge(
            [
                LoanDocument::CONTRACT_OBL_PRIVATE['name'],
                LoanDocument::PERSONAL_SHEET_OBL_PRIVATE['name'],
            ],
            parent::getTempPrivateDocs()
        );
    }

    public function getTempPublicDocs($loan)
    {
        return array_merge(
            [
                LoanDocument::CONTRACT_OBL['name'],
                LoanDocument::PERSONAL_SHEET_OBL['name'],
                LoanDocument::ARBITRATION_OBL['name'],
            ],
            parent::getTempPublicDocs($loan)
        );
    }
}
