<?php

namespace App\Services;

use App\Calculator\LoanCalculatorVLX;
use App\Exceptions\LoanNotFoundException;
use App\Exceptions\NotAllowedLoanUpdateException;
use App\Models\Loan;
use Illuminate\Support\Facades\Log;

class LoanConversionService
{
    private function getConfigs()
    {
        $loan_type = constants('LOAN_TYPES.BNPL');

        $loan_config_service = resolve('App\Services\LoanConfigService');

        return $loan_config_service->getConfigs($loan_type);
    }

    public function convertToAmortizedLoan($amount, $contract_number)
    {
        $loan = Loan::getLoanByContractNumber($contract_number);

        if (!$loan) {
            throw new LoanNotFoundException();
        }

        $configs = $this->getConfigs();

        //We are using velox calculator, because we need to create schedule fixed months
        $calculator = new LoanCalculatorVLX(
            $amount,
            $configs['amortized_interest_rate'],
            $configs['amortized_interest_rate'] - $configs['amortized_service_fee_rate'],
            [
                'max_duration' => $configs['amortized_duration'],
                'duration' => $configs['amortized_duration'],
            ]
        );
        $schedule = $calculator->generateSchedule();

        Log::info('Loan conversion new schedule', [
            'contract_number' => $contract_number,
            'schedule' => $schedule,
        ]);

        $monthly_payment = $schedule[0]['payment'];
        $sign_date = now();
        $duration = $configs['amortized_duration'];

        $total_amount = $calculator->scheduleSummary($monthly_payment)['total'];

        $apr = $calculator->calculateAPR($monthly_payment, $sign_date, $loan->withdrawal_fee, $duration);

        if ($loan->status != Loan::CONFIRMED) {
            throw new NotAllowedLoanUpdateException();
        }

        $loan->processLoanUpdates([
            'amount' => $amount,
            'months' => $duration,
            'total' => $total_amount,
            'prev_loan_type_id' => constants('LOAN_TYPES.BNPL'),
            'loan_type_id' => constants('LOAN_TYPES.OCL'),
            'monthly_payment' => $monthly_payment,
            'last_month_payment' => end($schedule)['payment'],
            'next_payment_date' => $schedule[0]['date'],
            'sign_date' => $sign_date,
            'interest_rate' => $configs['amortized_interest_rate'],
            'nominal_rate' => $configs['amortized_service_fee_rate'],
            'service_fee_rate' => $configs['amortized_interest_rate'] - $configs['amortized_service_fee_rate'],
            'apr' => $apr,
        ]);

        // Update loan schedule
        $loan->storeLoanSchedule($schedule);

        return $loan;
    }
}
