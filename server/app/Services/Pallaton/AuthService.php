<?php

namespace App\Services\Pallaton;

use App\Exceptions\Pallaton\DuplicateUserException;
use App\Exceptions\ResourceLockedException;
use App\Exceptions\UserNotFoundException;
use App\Factory\HcServiceFactory;
use App\Helpers\NumberHelper;
use App\Helpers\PassportHelper;
use App\Helpers\StringHelper;
use App\Interfaces\Pallaton\IAuthService;
use App\Models\Pallaton\Profile;
use App\Models\User;
use App\Traits\Transaction;
use Auth;
use Exception;
use Log;
use Spatie\Permission\Models\Role;

class AuthService implements IAuthService
{
    use Transaction;

    private $ssn_lock;
    private $email_lock;
    private $phone_lock;

    public function registerUser($payload, $loan_security)
    {
        // TODO: This is temporary solution, find proper solution
        $log_payload = array_filter($payload, function ($key) {
            return $key !== 'password';
        }, ARRAY_FILTER_USE_KEY);

        Log::info('Mobile user registration started', ['payload' => $log_payload]);

        $this->checkProfileExistence($loan_security);

        $this->acquireLocks($loan_security);

        return $this->startTransaction(function () use ($payload, $loan_security) {
            try {
                $aggregator_service = resolve('App\Interfaces\IAggregatorService');
                $ekeng_data = $aggregator_service->getEkengData($loan_security->document_number);

                $user = $this->createUser($payload, $loan_security, $ekeng_data);

                $this->createUserProfile($user, $loan_security, $aggregator_service, $ekeng_data);

                Log::info('Mobile user registration finished', ['user_id' => $user->id, 'profile_id' => $user->profile->id]);
            } catch (Exception $e) {
                Log::error('Mobile user registration, Exception', ['error' => $e->getMessage()]);
                throw $e;
            } finally {
                Log::debug('Releasing locked Mobile user registration resource');
                $this->releaseLocks();
                Log::debug('Mobile user registration locked resource released');
            }
        });
    }

    private function acquireLocks($loan_security)
    {
        $redis_service = resolve('App\Interfaces\IRedisService');

        $this->ssn_lock = $redis_service->lock('registration_ssn_lock_'.$loan_security->ssn, constants('REGISTRATION_USER_INFO_LOCK'));
        $this->email_lock = $redis_service->lock('registration_email_lock_'.$loan_security->email, constants('REGISTRATION_USER_INFO_LOCK'));
        $this->phone_lock = $redis_service->lock('registration_phone_number_lock_'.$loan_security->phone_number, constants('REGISTRATION_USER_INFO_LOCK'));

        if (!$this->ssn_lock->acquire() || !$this->email_lock->acquire() || !$this->phone_lock->acquire()) {
            Log::warning('Trying to access User Registration locked resource, ResourceLockedException',
                [
                    'ssn_locked' => !$this->ssn_lock->acquire(),
                    'email_locked' => !$this->email_lock->acquire(),
                    'phone_locked' => !$this->phone_lock->acquire(),
                ]);
            throw new ResourceLockedException();
        }
    }

    private function releaseLocks()
    {
        optional($this->ssn_lock)->release();
        optional($this->email_lock)->release();
        optional($this->phone_lock)->release();
    }

    private function checkProfileExistence($loan_security)
    {
        $existing_profile = Profile::where('ssn', $loan_security->ssn)
            ->orWhere('email', $loan_security->email)
            ->orWhere('phone_number', $loan_security->phone_number)
            ->first();

        if ($existing_profile) {
            Log::error('User already exists', [
                'ssn' => $loan_security->ssn,
                'email' => $loan_security->email,
                'phone' => $loan_security->phone_number,
            ]);
            throw new DuplicateUserException();
        }
    }

    private function createUser($payload, $loan_security, $ekeng_data)
    {
        $user_full_name = PassportHelper::extractCitizenFullName($ekeng_data);
        $formatted_full_name = array_map([StringHelper::class, 'makeFirstLetterUppercase'], $user_full_name);

        $user = User::create(array_merge($payload, $formatted_full_name));
        $user->assignRole(app(Role::class)->findByName('mobile-user', 'api'));
        $user->loan_security()->save($loan_security);
        $user->save();

        return $user;
    }

    private function createUserProfile($user, $loan_security, $aggregator_service, $ekeng_data)
    {
        $profile_data = [
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'middle_name' => $user->middle_name,
            'birth_date' => PassportHelper::getBirthDate($ekeng_data['passport_data'] ?? []),
            'address' => $aggregator_service->composeAddress($loan_security->document_number),
            'email' => $loan_security->email,
            'phone_number' => $loan_security->phone_number,
            'document_number' => $loan_security->document_number,
            'ssn' => $loan_security->ssn,
        ];

        $profile = $user->profile()->create($profile_data);
        $profile->save();
    }

    public function getUserByEmailOrPhone(string $username): ?User
    {
        return User::where('email', '=', strtolower($username))->orWhere('phone_number', '=', NumberHelper::phoneMask($username))->first();
    }

    public function resetPassword(string $username, string $password): void
    {
        $user = $this->getUserByEmailOrPhone($username);

        if (!$user) {
            throw new UserNotFoundException();
        }

        $user->password = $password;
        $user->save();
    }

    public function getUserProfile(): ?Profile
    {
        return Auth::user()->profile;
    }

    public function updateEmail($email)
    {
        $user = Auth::user();
        $email = strtolower($email);

        Log::info('Update mobile user email request', ['new_email' => $email, 'user' => $user]);

        $email_exists = User::where('email', 'ILIKE', $email)->exists();

        if ($email_exists) {
            throw new DuplicateUserException();
        }

        $user->update($payload);
        $user->profile()->update($payload);

        $hc_service = HcServiceFactory::build();
        $hc_service->updateHCEmail($user->profile->ssn, $email);

        Log::info('Mobile user email updated', ['email' => $email, 'user' => $user]);
    }
}
