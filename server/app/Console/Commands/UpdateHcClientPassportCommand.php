<?php

namespace App\Console\Commands;

use App\Exceptions\GCClientNotFoundException;
use App\Exceptions\HCBlacklistedException;
use App\Exceptions\InvalidDocumentException;
use App\Factory\HcServiceFactory;
use App\Helpers\PassportHelper;
use App\Traits\MeasureExecutionTime;
use App\Utils\HCPassportManager;
use Carbon\Carbon;
use Exception;
use function Functional\map;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Throwable;

class UpdateHcClientPassportCommand extends Command
{
    use MeasureExecutionTime;

    const CHUNK_SIZE = 100;

    protected $aggregator_service;
    protected $hc_service;
    protected $warehouse_service;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:hc-client-passport';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Via this command we are able to update hc client(s) passport(s)';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        $this->aggregator_service = resolve('App\Interfaces\IAggregatorService');
        $this->hc_service = HcServiceFactory::build(1);
        $this->warehouse_service = resolve('App\Interfaces\IWarehouseService');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            $this->info('Start Updating HC Client Passport...');
            Log::debug('Start Updating HC Client Passports');

            $ssn_chunks = array_chunk($this->getSsnList(), self::CHUNK_SIZE);

            foreach ($ssn_chunks as $chunk) {
                $this->processChunk($chunk);
            }

            Log::debug('Updating HC Client Passports Completed');
            $this->info('Success!. Update completed');
        } catch (Exception $e) {
            Log::error('Client HC Passport update, Exception', [
                'message' => $e->getMessage(),
                'error' => $e->getTraceAsString(),
            ]);

            $this->error($e->getMessage());
        }
    }

    private function processChunk(array $chunk)
    {
        foreach ($chunk as $ssn) {
            try {
                $this->measureExecutionTime(function () use ($ssn) {
                    $this->updateClientPassport($ssn);
                },
                    'UpdateHcClientPassport',
                    ['ssn' => $ssn]
                );
            } catch (Throwable $e) {
                Log::error('Client HC Passport update failed and update skipped', [
                    'ssn' => $ssn,
                    'message' => $e->getMessage(),
                    'error' => $e->getTraceAsString(),
                ]);

                continue;
            }
        }
    }

    private function updateClientPassport($ssn)
    {
        $hc_gc_client = $this->hc_service->getHcGCClientBySocCard($ssn);

        if (is_null($hc_gc_client)) {
            throw new GCClientNotFoundException();
        }

        $citizen_ekeng = $this->aggregator_service->getEkengData($ssn);
        $adjusted_documents = $this->adjustDocuments($this->getValidDocuments($citizen_ekeng));

        $passport_numbers_as_array = PassportHelper::getEkengPassportNumbersAsArray($ekeng_data['passport_data'] ?? []);
        $is_hc_blacklisted = $this->warehouse_service->checkIsHcBlacklisted($ssn, $passport_numbers_as_array);
        if ($is_hc_blacklisted) {
            Log::warning('Client HC passport update, client is HC blacklisted', ['ssn' => $ssn]);

            throw new HCBlacklistedException();
        }

        $client_passports = $this->composeClientPassport($adjusted_documents, $hc_gc_client);

        $client_passports = array_merge($client_passports, [
            'REGNUM' => $ssn,
        ]);

        $outer_id = $this->hc_service->getOrCreateClientId($ssn, constants('LOAN_TYPES.OCL'));

        $this->hc_service->updateExistingClient($hc_gc_client, $client_passports, $outer_id);

        Log::info('Client HC passport updated', [
            'ssn' => $ssn,
            'outer_id' => $outer_id,
            'client_passports' => $client_passports,
        ]);
    }

    private function getValidDocuments($citizen_ekeng)
    {
        $passport_data = $citizen_ekeng['passport_data']->AVVDocuments->AVVDocument ?? [];
        $valid_documents = PassportHelper::getPassportNumbers($passport_data);

        if (empty($valid_documents)) {
            Log::warning('Valid document not found in Ekeng passport list');

            throw new InvalidDocumentException();
        }

        return $valid_documents;
    }

    private function adjustDocuments($documents)
    {
        return map($documents, function ($d) {
            $given_date = Carbon::createFromFormat(constants('EKENG_DATE_FORMAT'), $d->IssuanceDate)->setTime(0, 0);
            $expire_date = Carbon::createFromFormat(constants('EKENG_DATE_FORMAT'), $d->ValidityDate)->setTime(0, 0);

            return [
                'type' => $d->DocumentIdentifier->DocumentType,
                'passport_number' => $d->DocumentIdentifier->DocumentNumber,
                'given_date' => $given_date,
                'expire_date' => $expire_date,
                'from' => $d->DocumentDepartment,
                'primary' => false,
            ];
        });
    }

    public function composeClientPassport($passports, $hc_gc_client): array
    {
        $hc_initial_passports = is_null($hc_gc_client) ? [] : [
            'PASSCODE' => HCPassportManager::composeHCPassportData($hc_gc_client->fPASCODE, $hc_gc_client->fPASTYPE),
            'PASSCODE2' => HCPassportManager::composeHCPassportData($hc_gc_client->fPASCODE2, $hc_gc_client->fPASTYPE2, HCPassportManager::PASSCODE2_SUFFIX),
        ];

        $hc_passport_manager = new HCPassportManager($passports, $hc_initial_passports);

        return $hc_passport_manager->composeHCPassports();
    }

    private function getSsnList()
    {
        return [
          '6514640451',
        ];
    }
}
