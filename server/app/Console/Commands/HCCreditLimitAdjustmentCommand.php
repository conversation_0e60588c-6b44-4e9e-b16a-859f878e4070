<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class HCCreditLimitAdjustmentCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'hc:credit-limit-adjustment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'HC Credit Limit Adjustment';

    protected $credit_limit_calculation_service;

    public function __construct()
    {
        parent::__construct();

        $this->credit_limit_calculation_service = resolve('App\Services\CreditLine\CreditLimitCalculationService');
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        Log::debug('HCCreditLimitAdjustmentCommand, started');

        $this->credit_limit_calculation_service->processHCCreditLimitAdjustment();

        Log::debug('HCCreditLimitAdjustmentCommand, finished');
    }
}
