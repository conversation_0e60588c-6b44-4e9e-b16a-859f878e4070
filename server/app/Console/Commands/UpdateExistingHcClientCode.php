<?php

namespace App\Console\Commands;

use App\Models\HC\HcClient;
use App\Models\HC\HcGCCLIENTS;
use App\Models\InternalClient;
use Illuminate\Console\Command;

class UpdateExistingHcClientCode extends Command
{
    const LIMIT = 100;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:hc-client-codes {limit?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update HC existing client codes that are not the same as our db';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $internal_client_service = resolve('App\Interfaces\IInternalClientService');

        $clients = InternalClient::where('is_hc_inserted', false)
            ->select(['ssn', 'client_type', 'client_id'])
            ->limit($this->argument('limit') ?? self::LIMIT)
            ->get();

        foreach ($clients as $client) {
            $hc_gc_client = HcGCCLIENTS::getBySocCard($client->ssn, $client->client_type);

            if ($hc_gc_client) {
                $ids = ['CLICOD' => $hc_gc_client->fCODE, 'NEW_OUTERID' => $client->client_id];

                HcClient::updateExisting($ids, ['REGNUM' => $client->ssn]);
                $internal_client_service->markAsHcInserted($client->ssn, $client->client_type);
            }
        }
    }
}
