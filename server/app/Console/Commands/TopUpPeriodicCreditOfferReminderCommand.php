<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TopUpPeriodicCreditOfferReminderCommand extends Command
{
    protected $signature = 'top-up:periodic-credit-offer-reminder';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Top-up periodic credit offers reminder';

    protected $top_up_periodic_offer_service;

    public function __construct()
    {
        parent::__construct();

        $this->top_up_periodic_offer_service = resolve('App\Services\TopUp\TopUpPeriodicCreditOfferService');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::debug('TopUpPeriodicCreditOfferReminderCommand, started');

        $this->top_up_periodic_offer_service->topUpPeriodicOffersReminder();

        Log::debug('TopUpPeriodicCreditOfferReminderCommand, finished');
    }
}
