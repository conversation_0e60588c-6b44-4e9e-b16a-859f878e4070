<?php

namespace App\Api\V2\Transformers;

use League\Fractal\TransformerAbstract;

class DWScheduleTransformer extends TransformerAbstract
{
    public function transform($schedule)
    {
        return [
            'date' => $schedule['fdate'],
            'principal' => floatval($schedule['princ']),
            'interest' => floatval($schedule['Intr']),
            'service_fee' => floatval($schedule['sfvan']),
            'total' => floatval($schedule['mtot']),
        ];
    }
}
