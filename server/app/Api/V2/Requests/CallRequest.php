<?php

namespace App\Api\V2\Requests;

use Dingo\Api\Http\FormRequest;

class CallRequest extends FormRequest
{
    public function rules()
    {
        $rules = [
            'name' => 'required|string',
            'phone' => 'required|string',
            'type' => 'required|string',
            'ssn' => 'nullable|string',
        ];

        return $rules;
    }

    public function authorize()
    {
        return true;
    }
}
