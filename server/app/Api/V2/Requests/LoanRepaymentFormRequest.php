<?php

namespace App\Api\V2\Requests;

use Dingo\Api\Http\FormRequest;

class LoanRepaymentFormRequest extends FormRequest
{
    public function rules()
    {
        return [
            'contract_number' => 'required|string',
            'phone_number' => 'required|string',
            'amount' => 'required|numeric',
            'repayment_type' => 'required|numeric',
            'save' => 'required',
            'user_id' => 'required_if:save,true',
        ];
    }

    public function authorize()
    {
        return true;
    }
}
