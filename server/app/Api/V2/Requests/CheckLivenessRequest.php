<?php

namespace App\Api\V2\Requests;

use Dingo\Api\Http\FormRequest;

class CheckLivenessRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $regex = '/^[-A-Za-z0-9+=\/]{1,50}|=[^=]|={3,}$/';

        // Check is valid base64 string and check size with string size
        return [
            'citizen_image' => ['regex:'.$regex, 'max:'. 20 * 1024 * 1024],
        ];
    }
}
