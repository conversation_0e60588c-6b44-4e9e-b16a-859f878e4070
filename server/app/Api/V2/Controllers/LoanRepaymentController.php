<?php

namespace App\Api\V2\Controllers;

use App\Api\V2\Requests\LoanRepaymentFormRequest;
use App\Api\V2\Requests\LoanRepaymentRequest;
use App\Exceptions\AppWarningException;
use App\Exceptions\InternalErrorException;
use App\Exceptions\Pallaton\HcBankContractNotFoundException;
use App\Http\Controllers\Controller;
use App\Interfaces\Pallaton\ILoanRepaymentService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Log;
use Throwable;

class LoanRepaymentController extends Controller
{
    /**
     * @var ILoanRepaymentService
     */
    private $loan_repayment_service;

    /**
     * Create a new LoanRepaymentController instance.
     *
     * @return void
     */
    public function __construct(ILoanRepaymentService $loan_repayment_service)
    {
        $this->loan_repayment_service = $loan_repayment_service;
    }

    /**
     * Get loan repayment details.
     *
     * @return JsonResponse
     */
    public function getLoanRepaymentDetails(LoanRepaymentRequest $request)
    {
        try {
            $payload = $request->only([
                'contract_number',
                'phone_number',
            ]);

            Log::info('Get loan repayment details', ['payload' => $payload]);

            $loan_repayment_details = $this->loan_repayment_service->getLoanRepaymentDetails($payload['contract_number']);

            Log::info('Get loan repayment details response', ['details' => $loan_repayment_details]);

            return response()->json($loan_repayment_details);
        } catch (HcBankContractNotFoundException $e) {
            Log::warning('Get loan repayment details, HcBankContractNotFoundException', ['message' => $e->getMessage()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Get loan repayment details, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    /**
     * Get loan repayment form.
     *
     * @return JsonResponse
     */
    public function getLoanRepaymentForm(LoanRepaymentFormRequest $request)
    {
        try {
            $payload = $request->only([
                'contract_number',
                'phone_number',
                'amount',
                'repayment_type',
                'save',
                'user_id',
            ]);

            Log::info('Get loan repayment form', ['payload' => $payload]);

            $loan_repayment_form = $this->loan_repayment_service->getLoanRepaymentForm($payload);

            Log::info('Get loan repayment form response', ['form' => $loan_repayment_form]);

            return response()->json($loan_repayment_form);
        } catch (AppWarningException $e) {
            Log::warning('Get loan repayment form, AppWarningException', ['message' => $e->getMessage()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Get loan repayment form, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function processLoanRepaymentWebhook(Request $request)
    {
        try {
            $order_id = $request->get('orderId');
            $should_save_credit_card = (bool) $request->get('save');

            Log::info('Starting process loan repayment webhook', [
                'order_id' => $order_id,
                'should_save_credit_card' => $should_save_credit_card,
            ]);

            $redirect_url = $this->loan_repayment_service->processLoanRepayment($order_id, $should_save_credit_card);

            Log::info('Finished process loan repayment webhook', ['redirect_url' => $redirect_url]);

            return redirect($redirect_url);
        } catch (Throwable $e) {
            Log::error('Process loan repayment webhook, Exception', [
                'order_id' => $order_id ?? null,
                'message' => $e->getMessage(),
                'error' => $e->getTraceAsString(),
            ]);
            throw new InternalErrorException();
        }
    }
}
