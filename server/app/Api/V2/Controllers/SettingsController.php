<?php

namespace App\Api\V2\Controllers;

use App\Api\V2\Requests\NotifiableUserRequest;
use App\Api\V2\Requests\QRRequest;
use App\Exceptions\ExpiredQrException;
use App\Exceptions\QrNotFoundException;
use App\Http\Controllers\Controller;
use Dingo\Api\Http\Request;
use Exception;
use Log;

class SettingsController extends Controller
{
    public function getServerTime()
    {
        return response()->json(['now' => now()->format(constants('SERVER_DATE_TIME_FORMAT'))]);
    }

    public function getAppVersion()
    {
        return response()->json(['version' => env('MOBILE_APP_VERSION')]);
    }

    public function getAppStatus(Request $request)
    {
        if (!$request->header('X-Device-ID')) {
            return response()->json(['DISABLE_APP' => true]);
        }

        $setting_service = resolve('App\Interfaces\ISettingsService');

        return response()->json($setting_service->appStatus());
    }

    public function notifiableUsers(NotifiableUserRequest $request)
    {
        $notifiable_service = resolve('App\Interfaces\Pallaton\INotifiableUsersService');

        try {
            $payload = $request->all();

            if (!$request->header('X-Device-ID')) {
                Log::info('Store user device old app', ['payload' => $payload]);

                return response()->json(['success' => false]);
            }

            $notifiable_service->addNotifiableUsers($payload);
        } catch (Exception $e) {
            Log::error('Disabled loan notify error', ['payload' => $payload]);

            return response()->json(['success' => false]);
        }

        return response()->json(['success' => true]);
    }

    public function getQrDiscount(QRRequest $request)
    {
        try {
            $qr_code = $request->get('qr_code');
            $category_service = resolve('App\Services\SettingsService');
            $result = $category_service->getQrDiscount($qr_code);

            return response()->json($result);
        } catch (QrNotFoundException $e) {
            Log::warning('Check QrCode,  not found', ['message' => $e->getMessage()]);
            throw $e;
        } catch (ExpiredQrException $e) {
            Log::warning('Check QrCode, expired', ['message' => $e->getMessage()]);
            throw $e;
        }
    }
}
