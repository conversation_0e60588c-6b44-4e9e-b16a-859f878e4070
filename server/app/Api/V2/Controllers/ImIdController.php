<?php

namespace App\Api\V2\Controllers;

use App\Api\V2\Requests\AuthImIdRequest;
use App\Api\V2\Requests\CheckStatusImIdRequest;
use App\Api\V2\Requests\CreateImIdRegistrationSessionRequest;
use App\Api\V2\Requests\UpdateImIdRegistrationDetailsRequest;
use App\Exceptions\ImIdInternalErrorException;
use App\Http\Controllers\Controller;
use App\Interfaces\IImIdService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ImIdController extends Controller
{
    /**
     * @var IImIdService
     */
    private $im_id_service;

    public function __construct(IImIdService $im_id_service)
    {
        $this->im_id_service = $im_id_service;
    }

    public function authentication(AuthImIdRequest $request)
    {
        try {
            $payload = $request->only([
                'phone_number',
            ]);

            Log::info('Starting process authentication imId', [
                'phone_number' => $payload['phone_number'],
            ]);

            $result = $this->im_id_service->processAuthentication($payload['phone_number']);

            Log::info('Processing authentication imId', [
                'result' => $result,
            ]);

            return response()->json($result);
        } catch (Exception $e) {
            Log::warning('ImId authentication, Exception', ['error' => $e->getMessage()]);
            throw new ImIdInternalErrorException();
        }
    }

    public function handleProviderCallback(Request $request)
    {
        try {
            $auth_token = $request->header('Authorization', '');
            $payload = $request->all();

            Log::info('Processing imId provider callback', [
                'payload' => $payload,
            ]);
            $this->im_id_service->handleCallback($payload, $auth_token);
        } catch (Exception $e) {
            Log::error('ImId handle callBack Exception', ['error' => $e->getMessage()]);
        }
    }

    public function checkStatus(CheckStatusImIdRequest $request)
    {
        try {
            $payload = $request->only([
                'im_id_session_id',
            ]);

            Log::info('ImId Check status payload', ['payload' => $payload]);

            return $this->im_id_service->checkImIdStatus($payload);
        } catch (Exception $e) {
            Log::error('ImId check status Exception', ['error' => $e->getMessage()]);

            throw new ImIdInternalErrorException();
        }
    }

    public function createRegistrationSession(CreateImIdRegistrationSessionRequest $request)
    {
        try {
            $payload = $request->only([
                'im_id_session_id',
            ]);

            Log::info('ImId create registration session payload', ['request' => $payload]);

            $suuid = $this->im_id_service->createRegistrationSession($payload);

            Log::info('ImId registration session create success', ['suuid' => $suuid]);

            return response()->json([
                'suuid' => $suuid,
            ]);
        } catch (Exception $e) {
            Log::error('ImId create registration session Exception', ['error' => $e->getMessage()]);
            throw new ImIdInternalErrorException();
        }
    }

    public function updateRegistrationDetails(UpdateImIdRegistrationDetailsRequest $request)
    {
        try {
            $payload = $request->only([
                'email',
            ]);

            Log::info('ImId update loan security', ['payload' => $payload]);
            $this->im_id_service->updateRegistrationDetails($payload);

            return response()->json(['status' => 'ok']);
        } catch (Exception $e) {
            Log::error('ImId update loan security Exception', ['error' => $e->getMessage()]);
            throw new ImIdInternalErrorException();
        }
    }
}
