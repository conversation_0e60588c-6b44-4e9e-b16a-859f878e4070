<?php

namespace App\Api\V2\Controllers;

use App\Api\V2\Requests\ArmedPurchaseOrderStatusRequest;
use App\Api\V2\Requests\ArmedStorePurchaseOrderRequest;
use App\Exceptions\CreditLine\PurchaseOrderAlreadyExistsException;
use App\Exceptions\CreditLine\PurchaseOrderNotFoundException;
use App\Exceptions\InternalErrorException;
use App\Exceptions\InvalidArmedDataException;
use App\Http\Controllers\Controller;
use App\Traits\Transaction;
use Carbon\Carbon;
use Exception;
use Log;

class ArmedController extends Controller
{
    use Transaction;

    public function storePurchaseOrder(ArmedStorePurchaseOrderRequest $request)
    {
        try {
            $purchase_service = resolve('App\Interfaces\CreditLine\IPurchaseService');
            $armed_service = resolve('App\Interfaces\IArmedService');

            $request_data = $request->all();

            $decrypted_data = $armed_service->decrypt($request_data['data']);
            $merged_data = array_merge($decrypted_data, [
                'order_id' => $decrypted_data['transaction_id'],
                'armed_id' => $request_data['id'],
            ]);

            Log::info('Attempting to store Armed purchase order and session', ['payload' => $merged_data]);

            $this->startTransaction(function () use ($purchase_service, $merged_data) {
                $result = $purchase_service->storePurchaseOrder($merged_data);
                $purchase_service->createSession($merged_data);

                $sms_service = resolve('App\Interfaces\ISmsService');
                $sms_service->send($result->phone_number, __('sms.bnpl_app_redirect_msg', [
                    'app_redirect_link' => env('BNPL_APP_REDIRECT_LINK'),
                    'date' => Carbon::parse($result->created_at)->setTimezone(constants('ARM_TIMEZONE'))->format(constants('SMS_DATE_FORMAT')),
                ]));
            });

            return response()->json(['success' => true]);
        } catch (PurchaseOrderAlreadyExistsException | InvalidArmedDataException $e) {
            Log::warning('Store Purchase Order, '.get_class($e), ['error' => $e->getMessage()]);

            throw $e;
        } catch (Exception $e) {
            Log::error('Store Purchase Order, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw new InternalErrorException();
        }
    }

    public function getPurchaseOrderStatus(ArmedPurchaseOrderStatusRequest $request)
    {
        try {
            $purchase_service = resolve('App\Interfaces\CreditLine\IPurchaseService');

            $status = $purchase_service->getPurchaseOrderStatus($request->input('data'));

            return response()->json([
                'current_status' => $status,
            ]);
        } catch (PurchaseOrderNotFoundException | InvalidArmedDataException $e) {
            Log::warning('Get purchase order status, '.get_class($e), ['error' => $e->getMessage()]);

            throw $e;
        } catch (Exception $e) {
            Log::error('Get purchase order status, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw new InternalErrorException();
        }
    }
}
