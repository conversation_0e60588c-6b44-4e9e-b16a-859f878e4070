<?php

namespace App\Api\V1\Controllers;

use App\Api\V1\Requests\CitizenRequest;
use App\Api\V1\Requests\QRRequest;
use App\Api\V1\Transformers\CitizenTransformer;
use App\Exceptions\BlacklistedException;
use App\Exceptions\CitizenNotFoundException;
use App\Exceptions\ExpiredPassportException;
use App\Exceptions\InternalErrorException;
use App\Exceptions\InvalidOwnerDocumentException;
use App\Exceptions\InvalidSSNException;
use App\Exceptions\InvalidSuuidException;
use App\Exceptions\InvalidTradeAmountException;
use App\Exceptions\ResourceLockedException;
use App\Exceptions\TradeCreditEmptyException;
use App\Exceptions\UnknownDocumentTypeException;
use App\Exceptions\VehicleNotFoundException;
use App\Exceptions\VehicleOwnerIsCitizenException;
use App\Http\Controllers\Controller;
use App\Interfaces\ICitizenService;
use App\Interfaces\ISecurityService;
use App\Models\QrCode;
use Auth;
use Exception;
use Log;
use Throwable;

class CitizenController extends Controller
{
    /**
     * Create a new CitizenController instance.
     *
     * @return void
     */
    public function __construct(ICitizenService $citizenService, ISecurityService $securityService)
    {
        $this->citizenService = $citizenService;
        $this->securityService = $securityService;
    }

    /**
     * Show fiscal details.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(CitizenRequest $request)
    {
        $calculate_allowance = $request->input('gcc', true);

        try {
            $loan_security = $this->securityService->resolveLoanSecurity();

            $payload = [
                'document_number' => $loan_security->document_number,
                'term_id' => $loan_security->term_id,
                'solar_panel_type_id' => $loan_security->solar_panel_type_id,
                'pipe_type_id' => $loan_security->pipe_type_id,
                'vehicle_number' => $request->input('vehicle_number', null),
                'tech_passport' => $request->input('tech_passport', null),
                'trade_amount' => (int) $request->input('trade_amount', null),
            ];

            $citizen = $this->requestCitizen($payload, $calculate_allowance);

            return response()->json($citizen);
        } catch (InvalidSuuidException $e) {
            Log::error('Citizen show, InvalidSuuidException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (BlacklistedException $e) {
            Log::error('Citizen show, BlacklistedException', ['error' => $e->getTraceAsString()]);
            throw $e;
        }
    }

    public function submitDocumentOasl(CitizenRequest $request)
    {
        $document_number = strtoupper($request->get('gcd'));

        $security_service = resolve('App\Interfaces\ISecurityService');

        // The User is logged in Oasl Agent in this case
        $user = Auth::guard('api')->user();

        Log::info('Submit Document oasl', ['oasl_agent_id' => $user->id, 'document_number' => $document_number]);

        $security_service->recordMetaOnce([], constants('META_STEPS.CREDENTIALS_VERIFIED'), true);

        return response()->json([
            'submit_document_success' => true,
        ]);
    }

    public function submitDocumentOiql(CitizenRequest $request)
    {
        $document_number = strtoupper($request->get('gcd'));

        // The User is logged in Oiql Agent in this case
        $user = Auth::guard('api')->user();

        Log::info('Submit Document oiql', ['oiql_agent_id' => $user->id, 'document_number' => $document_number]);

        return response()->json([
            'submit_document_success' => true,
        ]);
    }

    private function requestCitizen($payload, $calculate_allowance)
    {
        try {
            Log::info('Requesting citizen data', ['gcd' => $payload, 'gcc' => $calculate_allowance]);
            $citizen = $this->citizenService->fetchCitizen($payload, $calculate_allowance);
            Log::info('Citizen data response', ['citizen_context' => $citizen]);

            return fractal()->item($citizen)->transformWith(new CitizenTransformer());
        } catch (UnknownDocumentTypeException $e) {
            Log::error('Request citizen data, UnknownDocumentTypeException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (CitizenNotFoundException $e) {
            Log::error('Request citizen data, CitizenNotFoundException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (BlacklistedException $e) {
            Log::error('Request citizen data, BlacklistedException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (ResourceLockedException $e) {
            Log::error('Request citizen data, ResourceLockedException', ['error' => $e->getMessage()]);
            throw new InternalErrorException();
        } catch (ExpiredPassportException $e) {
            Log::error('Request citizen data, ExpiredPassportException', ['error' => $e->getTraceAsString()]);
            throw $e;
        } catch (InvalidSSNException $e) {
            Log::error('Request citizen data, InvalidSSNException', ['error' => $e->getTraceAsString()]);
            throw $e;
        } catch (InternalErrorException $e) {
            Log::error('Request citizen data, InternalErrorException', ['error' => $e->getTraceAsString()]);
            throw $e;
        } catch (Throwable $e) {
            Log::error('Request citizen data, Exception', ['error' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }

    public function getTradeVehicleCredit(CitizenRequest $request)
    {
        $calculate_allowance = $request->input('gcc', true);

        try {
            $loan_security = $this->securityService->resolveLoanSecurity();

            $payload = [
                'document_number' => $loan_security->document_number,
                'vehicle_number' => $request->get('vehicle_number'),
                'tech_passport' => $request->get('tech_passport'),
                'trade_amount' => (int) $request->get('trade_amount'),
            ];

            $citizenServiceOVL = resolve('App\Services\CitizenServiceOVL');
            $credit = $citizenServiceOVL->getTradeCredit($payload, $calculate_allowance);

            return response()->json($credit);
        } catch (InvalidSuuidException | UnknownDocumentTypeException | CitizenNotFoundException |
        VehicleNotFoundException | TradeCreditEmptyException | InvalidTradeAmountException |
        VehicleOwnerIsCitizenException | BlacklistedException | InvalidOwnerDocumentException $e) {
            Log::warning('Trade Vehicle Credit, '.get_class($e), ['error' => $e->getMessage()]);
            throw $e;
        } catch (ResourceLockedException $e) {
            Log::warning('Request Trade Vehicle data, ResourceLockedException', ['error' => $e->getMessage()]);
            throw new InternalErrorException();
        } catch (Throwable $e) {
            Log::error('Request Trade Vehicle data, Exception', ['error' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }

    public function getDiscount(QRRequest $request)
    {
        try {
            $discount = QrCode::getDiscount($request->token, $request->loan_type_id);

            if (!$discount) {
                Log::info('Get Discount', ['discount' => $discount, 'token' => $request->token]);
            }

            return response()->json([
                'discount' => $discount,
            ]);
        } catch (Exception $e) {
            Log::error('Get discount, Exception', ['error' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }
}
