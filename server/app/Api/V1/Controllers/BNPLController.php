<?php

namespace App\Api\V1\Controllers;

use App\Abstracts\AbstractCreditLineController;
use App\Api\V1\Requests\ClientStateBNPLRequest;
use App\Api\V1\Requests\CreateCreditRequest;
use App\Api\V1\Requests\CreditLimitRequest;
use App\Api\V1\Requests\PurchaseRequest;
use App\Api\V1\Requests\SmsRequest;
use App\Exceptions\CreditLine\DeclinedPurchaseException;
use App\Exceptions\CreditLine\DoublePurchaseException;
use App\Exceptions\CreditLine\ExpiredPaymentIdException;
use App\Exceptions\CreditLine\HcBankGetDebtException;
use App\Exceptions\CreditLine\InsufficientBalanceException;
use App\Exceptions\CreditLine\InvalidPaymentIdException;
use App\Exceptions\CreditLine\NonSyncTransactionException;
use App\Exceptions\DoubleBNPLLoanException;
use App\Exceptions\ExpiredPassportException;
use App\Exceptions\HcBankOverdueFeeException;
use App\Exceptions\InternalErrorException;
use App\Exceptions\InvalidCodeException;
use App\Exceptions\InvalidSuuidException;
use App\Interfaces\CreditLine\ICreditLineException;
use App\Services\CreditLine\BNPLService;
use Dingo\Api\Http\Request;
use Exception;
use Log;
use Throwable;

class BNPLController extends AbstractCreditLineController
{
    public function __construct(BNPLService $bnpl_service)
    {
        parent::__construct($bnpl_service);
    }

    public function terms()
    {
        try {
            $terms = $this->credit_line_service->getTerms();

            return response()->json($terms);
        } catch (Exception $e) {
            Log::error('Get BNPL Terms, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw new InternalErrorException();
        }
    }

    public function clientState(ClientStateBNPLRequest $request)
    {
        try {
            $ssn = $request->get('ssn');

            Log::info('Start getting client state BNPL', ['ssn' => $ssn]);

            $client_state = $this->credit_line_service->getClientState($ssn);

            return response()->json($client_state);
        } catch (Exception $e) {
            Log::error('Getting Client State BNPL, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw new InternalErrorException();
        }
    }

    public function creditLimit(CreditLimitRequest $request)
    {
        try {
            $ssn = $request->get('ssn');

            Log::info('Start get credit limit BNPL', ['ssn' => $ssn]);

            $result = $this->credit_line_service->getLimit($ssn);

            Log::info('End get credit limit BNPL', ['ssn' => $ssn, 'result' => $result]);

            return response()->json($result);
        } catch (ExpiredPassportException $e) {
            Log::warning('Get credit limit, ExpiredPassportException', ['error' => $e->getMessage()]);

            throw $e;
        } catch (Exception $e) {
            Log::error('Get credit limit, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw new InternalErrorException();
        }
    }

    public function createCredit(CreateCreditRequest $request)
    {
        try {
            $payload = $request->only([
                'dispute_solution_method',
                'notification_method',
            ]);

            if ($this->credit_line_service->resolveCredit()) {
                throw new DoubleBNPLLoanException();
            }

            $security_service = resolve('App\Interfaces\ISecurityService');
            $loan_security = $security_service->resolveLoanSecurity();

            $amount = 0;
            $loan_documents = [];

            $loan = $this->credit_line_service->createCredit($loan_security, $payload);
            // The loan could be null if the citizen rejected based on rule engine
            if (!is_null($loan)) {
                $security_service->recordMetaOnce([], constants('META_STEPS.UPDATE_PERSONAL_INFO'), true);

                $amount = $loan->amount;
                $loan_documents = $this->credit_line_service->generateDocuments($loan);

                $security_service->recordMetaOnce([], constants('META_STEPS.GET_LOAN_DOCUMENTS'), true);
            }

            return response()->json([
                'amount' => $amount,
                'loan_documents' => $loan_documents,
            ]);
        } catch (DoubleBNPLLoanException | InvalidSuuidException $e) {
            Log::warning('Create credit, '.get_class($e), ['error' => $e->getMessage()]);

            throw $e;
        } catch (Throwable $e) {
            Log::error('Create credit, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw new InternalErrorException();
        }
    }

    public function approve(SmsRequest $request)
    {
        try {
            $security_service = resolve('App\Interfaces\ISecurityService');
            $loan_security = $security_service->resolveLoanSecurity();
            $loan = $loan_security->loan;

            // Lock the resource and process loan confirmation
            return $this->withLoanConfirmationLock($loan, function () use ($request) {
                parent::approve($request);

                return response()->json([
                    'success' => true,
                ]);
            });
        } catch (InvalidCodeException $e) {
            Log::warning('Approve loan, InvalidCodeException', ['error' => $e->getMessage()]);

            throw $e;
        } catch (Throwable $e) {
            Log::error('Approve loan, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw new InternalErrorException();
        }
    }

    public function getPurchase(Request $request)
    {
        try {
            return parent::getPurchase($request);
        } catch (InvalidPaymentIdException | ExpiredPaymentIdException | DoublePurchaseException | HcBankGetDebtException | NonSyncTransactionException $e) {
            // These Exceptions are not to Session Rejection cases
            Log::warning('Get purchase, '.get_class($e), ['error' => $e->getMessage()]);

            throw $e;
        } catch (ICreditLineException $e) {
            Log::warning('Get purchase, '.get_class($e), ['error' => $e->getMessage()]);

            $purchase_service = resolve('App\Interfaces\CreditLine\IPurchaseService');
            $purchase_session = $purchase_service->getPurchaseSession($request->get('payment_id'));
            $purchase_service->rejectSession($purchase_session);

            throw new DeclinedPurchaseException();
        } catch (Throwable $e) {
            Log::error('Get purchase, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw new InternalErrorException();
        }
    }

    public function purchase(PurchaseRequest $request)
    {
        try {
            $payment_id = $request->get('payment_id');

            $purchase_service = resolve('App\Interfaces\CreditLine\IPurchaseService');
            $purchase_service->makePurchase($payment_id);

            $transaction_details = $this->credit_line_service->getTransactionDetails($payment_id);

            Log::info('BNPL Transaction details', [$transaction_details]);

            return response()->json($transaction_details);
        } catch (InvalidPaymentIdException | ExpiredPaymentIdException | DoublePurchaseException $e) {
            Log::warning('Make purchase, '.get_class($e), ['error' => $e->getMessage()]);

            throw $e;
        } catch (InsufficientBalanceException | HcBankOverdueFeeException $e) {
            Log::warning('Make purchase, '.get_class($e), ['error' => $e->getMessage()]);

            throw new DeclinedPurchaseException();
        } catch (Throwable $e) {
            Log::error('Make purchase, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw new InternalErrorException();
        }
    }
}
