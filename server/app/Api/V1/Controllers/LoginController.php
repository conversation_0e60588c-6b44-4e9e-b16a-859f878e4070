<?php

namespace App\Api\V1\Controllers;

use App\Api\V1\Requests\LoginPlRequest;
use App\Api\V1\Requests\LoginRequest;
use App\Api\V1\Requests\SetPasswordRequest;
use App\Exceptions\BlacklistedException;
use App\Exceptions\InternalErrorException;
use App\Exceptions\InvalidSuuidException;
use App\Exceptions\LoginFailureException;
use App\Helpers\NumberHelper;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Log;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Throwable;
use <PERSON><PERSON>\JWTAuth\Exceptions\JWTException;
use <PERSON>mon\JWTAuth\JWTAuth;

class LoginController extends Controller
{
    /**
     * Log the user in.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(LoginRequest $request, JWTAuth $JWTAuth)
    {
        $credentials = $request->only(['email', 'password']);
        $credentials = array_merge($credentials, [
            'email' => strtolower($credentials['email']),
        ]);

        if (!$this->canLogin($request->loan_type_id, $credentials['email'])) {
            throw new LoginFailureException();
        }

        return $this->auth($credentials);
    }

    public function plLogin(LoginPlRequest $request, JWTAuth $JWTAuth)
    {
        $credentials = $request->only(['phone_number', 'password']);

        $credentials = array_merge($credentials, [
            'phone_number' => NumberHelper::phoneMask($credentials['phone_number']),
        ]);

        if (!$this->canPlLogin($request->loan_type_id, $credentials['phone_number'])) {
            throw new LoginFailureException();
        }

        return $this->auth($credentials);
    }

    private function canLogin($loan_type_id, $email)
    {
        $user = User::where('email', 'ILIKE', $email)->first();
        if (!$user) {
            return false;
        }

        $role_mapping = [
            constants('LOAN_TYPES.OASL') => 'oasl-agent',
            constants('LOAN_TYPES.OIQL') => 'iqos-seller',
            constants('LOAN_TYPES.BNPL') => 'merchant-agent',
            constants('LOAN_TYPES.OVL') => 'merchant-agent-ovil',
        ];

        $role = $role_mapping[$loan_type_id] ?? null;

        return $user->hasRole('moderator') || $user->hasRole($role);
    }

    private function canPlLogin($loan_type_id, $phone)
    {
        $user = User::where('phone_number', $phone)->first();

        return $user && $user->hasRole('pl-user') && $loan_type_id == constants('LOAN_TYPES.PL');
    }

    private function auth($credentials)
    {
        try {
            $token = Auth::guard('api')->attempt($credentials);

            if (!$token) {
                throw new LoginFailureException();
            }

            return response()
                ->json([
                    'status' => 'ok',
                    'token' => $token,
                    'expires_in' => Auth::guard('api')->factory()->getTTL() * 60,
                ]);
        } catch (JWTException $e) {
            throw new HttpException(500);
        }
    }

    public function setPassword(SetPasswordRequest $request)
    {
        try {
            $security_service = resolve('App\Interfaces\ISecurityService');
            $loan_security = $security_service->resolveLoanSecurity();
            $citizen = $loan_security->loan->citizen;

            $payload = [
                'email' => $citizen->email,
                'phone_number' => $loan_security->phone_number,
                'password' => $request->get('password'),
            ];

            // Register new user
            $registration_service = resolve('App\Interfaces\CreditLine\IRegistrationService');
            $registration_service->registerUser($payload, $citizen);

            // Confirm credit
            $credit_service = resolve('App\Interfaces\CreditLine\IPayLaterService');
            $credit_service->confirmCredit($loan_security);

            $credentials = [
                'phone_number' => $loan_security->phone_number,
                'password' => $payload['password'],
            ];

            // Autologin user
            return $this->auth($credentials);
        } catch (InvalidSuuidException $e) {
            Log::error('Set Password, InvalidSuuidException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (BlacklistedException $e) {
            Log::error('Set Password, BlacklistedException', ['error' => $e->getTraceAsString()]);
            throw $e;
        } catch (LoginFailureException $e) {
            Log::error('Set Password, LoginFailureException', ['error' => $e->getTraceAsString()]);
            throw $e;
        } catch (HttpException $e) {
            Log::error('Set Password, HttpException', ['error' => $e->getTraceAsString()]);
            throw $e;
        } catch (Throwable $e) {
            Log::error('Set Password, Exception', ['error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }
}
