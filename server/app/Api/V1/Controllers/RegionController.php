<?php

namespace App\Api\V1\Controllers;

use App\Exceptions\InternalErrorException;
use App\Http\Controllers\Controller;
use Exception;
use Illuminate\Support\Facades\Log;

class RegionController extends Controller
{
    public function getRegions()
    {
        try {
            $region_service = resolve('App\Services\RegionService');

            $regions = $region_service->getRegions();

            return response()->json(['regions' => $regions]);
        } catch (Exception $e) {
            Log::error('Get regions, Exception', ['error' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }
}
