<?php

namespace App\Api\V1\Controllers;

use App\Api\V1\Transformers\ModeratorLoanTransformer;
use App\Exceptions\DoublePaymentException;
use App\Exceptions\InternalErrorException;
use App\Exceptions\InvalidModeratorLoanException;
use App\Exceptions\PaymentLockedException;
use App\Http\Controllers\Controller;
use App\Interfaces\IModeratorService;
use Auth;
use Exception;
use Illuminate\Http\Request;
use Log;

class ModeratorController extends Controller
{
    public function __construct(IModeratorService $moderator_service)
    {
        $this->moderator_service = $moderator_service;
    }

    public function getQueued()
    {
        try {
            $queued = $this->moderator_service->getQueued();

            return fractal($queued, new ModeratorLoanTransformer());
        } catch (Exception $e) {
            Log::error('Failed to fetch queued loans', ['error' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }

    public function getSingleAssigned(Request $request, $id)
    {
        $user = Auth::guard('api')->user();

        try {
            $loan = $this->moderator_service->getSingleAssigned($id, $user);

            return $loan;
        } catch (InvalidModeratorLoanException $e) {
            Log::error('Ivalid loan', ['error' => $e->getMessage()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Failed to fetch assigned loan', ['id' => $id, 'error' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }

    public function assign(Request $request, $id)
    {
        $user = Auth::guard('api')->user();

        try {
            $this->moderator_service->assign($id, $user->id);

            return ['id' => $id];
        } catch (InvalidModeratorLoanException $e) {
            Log::error('Ivalid loan', ['error' => $e->getMessage()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Failed to assign loan', ['id' => $id, 'error' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }

    public function getAssigned()
    {
        $user = Auth::guard('api')->user();

        try {
            $assigned = $this->moderator_service->getAssigned($user->id);

            return fractal($assigned, new ModeratorLoanTransformer());
        } catch (Exception $e) {
            Log::error('Failed to fetch assigned loans', ['error' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }

    public function getAll()
    {
        $user = Auth::guard('api')->user();

        try {
            $all = $this->moderator_service->getAll($user->id);

            return [
                'queued' => fractal($all['queued'], new ModeratorLoanTransformer()),
                'assigned' => fractal($all['assigned'], new ModeratorLoanTransformer()),
            ];
        } catch (Exception $e) {
            Log::error('Failed to fetch all loans', ['error' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }

    public function confirm(Request $request)
    {
        $id = $request->get('id');
        $active_tags = $request->get('active_tags');
        $user = Auth::guard('api')->user();

        try {
            $this->moderator_service->confirm($id, $user->id, $active_tags);

            return ['id' => $id];
        } catch (InvalidModeratorLoanException $e) {
            Log::error('Ivalid loan', ['error' => $e->getMessage()]);
            throw $e;
        } catch (DoublePaymentException $e) {
            Log::error('Double payment attempted by moderator', ['id' => $id, 'error' => $e->getMessage()]);
            throw $e;
        } catch (PaymentLockedException $e) {
            Log::error('Payment locked by moderator', ['id' => $id, 'error' => $e->getMessage()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Failed to confirm loan', ['id' => $id, 'error' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }

    public function reject(Request $request)
    {
        $id = $request->get('id');
        $notes = $request->get('notes');
        $active_tags = $request->get('active_tags');

        $user = Auth::guard('api')->user();

        try {
            $this->moderator_service->reject($id, $user->id, $notes, $active_tags);

            return ['id' => $id];
        } catch (InvalidModeratorLoanException $e) {
            Log::error('Ivalid loan', ['error' => $e->getMessage()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Failed to reject loan', ['id' => $id, 'notes' => $notes, 'error' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }

    public function getTags()
    {
        try {
            return response()->json($this->moderator_service->getTags());
        } catch (Exception $e) {
            Log::error('Failed to fetch tags', ['error' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }
}
