<?php

namespace App\Api\V1\Controllers;

use App\Api\V1\Requests\OWLApproveLoanRequests;
use App\Api\V1\Requests\OWLRequests;
use App\Api\V1\Transformers\LoanTransformer;
use App\Exceptions\BlacklistedException;
use App\Exceptions\CitizenNotFoundException;
use App\Exceptions\InternalErrorException;
use App\Exceptions\InvalidLoanException;
use App\Exceptions\InvalidSuuidException;
use App\Exceptions\PaymentFailureException;
use App\Exceptions\TransferTypeNotFoundException;
use App\Exceptions\UnpaidLoanException;
use App\Exceptions\UnprocessableApplicationException;
use App\Factory\OWLServiceFactory;
use App\Http\Controllers\Controller;
use App\Interfaces\ICitizenService;
use App\Interfaces\IDocumentService;
use App\Interfaces\IInternalRuleException;
use App\Interfaces\ILoanService;
use App\Interfaces\IOWLService;
use App\Interfaces\IPaymentService;
use App\Interfaces\ISecurityService;
use Exception;
use Log;
use Throwable;

class OFSLController extends Controller
{
    /** @var ILoanService */
    protected $loan_service;
    protected $citizen_service;
    protected $payment_service;
    protected $security_service;

    /**
     * OFSLController constructor.
     */
    public function __construct(
        ILoanService $loan_service,
        ICitizenService $citizen_service,
        IPaymentService $payment_service,
        ISecurityService $security_service
    ) {
        $this->citizen_service = $citizen_service;
        $this->loan_service = $loan_service;
        $this->payment_service = $payment_service;
        $this->security_service = $security_service;
    }

    public function getTerms(OWLRequests $request)
    {
        $amount = $request->get('amount') ?? $request->get('Amount');

        // TODO: this is temporary solution
        if ($amount == 10000) {
            $amount = 20000;
        }

        $terms = $this->loan_service->getTerms($amount);

        return view('wallet.ofsl.terms', $terms);
    }

    public function confirmTerms()
    {
        return view('wallet.ofsl.confirm_terms');
    }

    public function getLoan()
    {
        try {
            $loan_type_id = constants('LOAN_TYPES.OFSL');
            $owl_service = OWLServiceFactory::build($loan_type_id);

            $payload = $owl_service->composeCitizenInfo();
            $citizen = $owl_service->getLoan($payload, $loan_type_id);

            return view('wallet.ofsl.loan', $citizen);
        } catch (BlacklistedException | UnprocessableApplicationException | UnpaidLoanException | CitizenNotFoundException | IInternalRuleException | InternalErrorException $e) {
            Log::warning('Get Loan OFSL, '.get_class($e), ['error' => $e->getMessage()]);
            throw $e;
        } catch (Throwable $e) {
            Log::error('Get Loan OFSL, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function approveLoan(OWLApproveLoanRequests $request)
    {
        $payload = $request->only([
            'amount',
            'notification_method',
            'dispute_solution_method',
        ]);

        try {
            $loan_security = $this->security_service->resolveLoanSecurity();

            $payload = array_merge($payload, [
                'document_number' => $loan_security->document_number,
                'email' => $loan_security->email,
                'phone_number' => $loan_security->phone_number,
                'monthly_payment' => null,
                'token' => $loan_security->owl_token,
            ]);

            Log::info('Approve loan', ['payload' => $payload]);
            $loan = $this->loan_service->create($payload);
            Log::info('Loan approve response', ['response' => $loan]);

            $this->storeTransfer($loan);

            return fractal($loan, new LoanTransformer());
        } catch (InvalidSuuidException | InvalidLoanException | BlacklistedException $e) {
            Log::warning('Get transfer types, '.get_class($e), ['error' => $e->getMessage()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Approve loan, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    private function storeTransfer($loan)
    {
        $params['transfer_type'] = constants('TRANSFER_TYPES.WALLET_LOAN');

        try {
            Log::info('Storing OFSL transfer', ['loan' => $loan, 'params' => $params]);
            $this->payment_service->storeTransferMethod($loan, $params);
            Log::debug('OFSL transfer stored');

            // Setting sign date is required for document generation
            $loan = $this->loan_service->setSignDateAndApr($loan);

            Log::debug('Generating loan Documents');
            /** @var IDocumentService $loanDocumentService */
            $loanDocumentService = resolve('App\Interfaces\IDocumentService');
            $loanDocumentService->getLoanDocumentsJob($loan)->dispatch($loan, $force = true, true);
            Log::debug('Loan Documents generation successfully dispatched');
        } catch (PaymentFailureException | TransferTypeNotFoundException $e) {
            Log::warning('Store transfer, '.get_class($e), ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    public function getAgreement()
    {
        $loan_security = $this->security_service->resolveLoanSecurity();
        $loan = $loan_security->loan;

        /** @var IDocumentService $loanDocumentService */
        $loan_document_service = resolve('App\Interfaces\IDocumentService');
        $data = $loan_document_service->composePdfData($loan);

        return view('wallet.ofsl.agreement')->with($data);
    }

    public function getContract()
    {
        /** @var IOWLService $owl_service */
        $owl_service = OWLServiceFactory::build(constants('LOAN_TYPES.OFSL'));
        $loan_security = $this->security_service->resolveLoanSecurity();

        $contract_data = $owl_service->composeContractData($loan_security);

        return view('wallet.ofsl.contract')->with($contract_data);
    }

    public function getConfirmation($status)
    {
        if (!in_array($status, ['success', 'fail'])) {
            abort(404);
        }

        return view('wallet.ofsl.confirmation')->with(['status' => $status]);
    }

    public function down()
    {
        return view('wallet.down');
    }
}
