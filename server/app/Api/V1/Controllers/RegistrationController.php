<?php

namespace App\Api\V1\Controllers;

use App\Api\V1\Transformers\CitizenTransformer;
use App\Exceptions\BlacklistedException;
use App\Exceptions\CitizenNotFoundException;
use App\Exceptions\ExpiredPassportException;
use App\Exceptions\InternalErrorException;
use App\Exceptions\InvalidSSNException;
use App\Exceptions\InvalidSuuidException;
use App\Http\Controllers\Controller;
use App\Interfaces\CreditLine\ICitizenServicePL;
use App\Interfaces\ISecurityService;
use Log;
use Throwable;

class RegistrationController extends Controller
{
    protected $citizen_service;
    protected $security_service;

    public function __construct(ICitizenServicePL $citizen_service, ISecurityService $security_service)
    {
        $this->citizen_service = $citizen_service;
        $this->security_service = $security_service;
    }

    public function getPersonalInfo()
    {
        try {
            $loan_security = $this->security_service->resolveLoanSecurity();

            $personal_info = $this->citizen_service->getPersonalInfo($loan_security->document_number);
            $personal_info = array_merge($personal_info, [
                'ssn' => $loan_security->ssn,
                'phone_number' => $loan_security->phone_number,
                'email' => $loan_security->email,
            ]);

            Log::info('Personal Info data response', ['info' => $personal_info]);

            return fractal()->item($personal_info)->transformWith(new CitizenTransformer());
        } catch (InvalidSuuidException $e) {
            Log::error('Citizen show, InvalidSuuidException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (BlacklistedException $e) {
            Log::error('Citizen show, BlacklistedException', ['error' => $e->getTraceAsString()]);
            throw $e;
        } catch (ExpiredPassportException $e) {
            Log::error('Citizen show, ExpiredPassportException', ['error' => $e->getTraceAsString()]);
            throw $e;
        } catch (InvalidSSNException $e) {
            Log::error('Citizen show, InvalidSSNException', ['error' => $e->getTraceAsString()]);
            throw $e;
        } catch (CitizenNotFoundException $e) {
            Log::error('Citizen show, CitizenNotFoundException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException('Citizen not found', [], $e->getAction(), $e->getCase(), $e->getShouldAlert(), $e->getMeta());
        } catch (InternalErrorException $e) {
            Log::error('Citizen show, InternalErrorException', ['error' => $e->getTraceAsString()]);
            throw $e;
        } catch (Throwable $e) {
            Log::error('Citizen show, InternalErrorException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }
}
