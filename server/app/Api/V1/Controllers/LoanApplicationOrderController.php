<?php

namespace App\Api\V1\Controllers;

use App\Api\V1\Requests\CheckLoanApplicationOrderExistenceRequest;
use App\Api\V1\Requests\StoreLoanApplicationOrderDetailsRequest;
use App\Api\V1\Transformers\LoanApplicationOrderTransformer;
use App\Exceptions\CitizenNotFoundException;
use App\Exceptions\ExpiredLoanApplicationOrderException;
use App\Exceptions\ExpiredPassportException;
use App\Exceptions\InternalErrorException;
use App\Exceptions\InvalidDocumentException;
use App\Exceptions\InvalidSSNException;
use App\Exceptions\LoanApplicationOrderAlreadyExistsException;
use App\Exceptions\LoanApplicationOrderNotFoundException;
use App\Http\Controllers\Controller;
use App\Interfaces\ILoanApplicationOrderService;
use App\Traits\Transaction;
use Carbon\Carbon;
use Dingo\Api\Http\Request;
use Exception;
use Illuminate\Support\Facades\Log;

class LoanApplicationOrderController extends Controller
{
    use Transaction;

    protected $loan_application_order_service;

    public function __construct(ILoanApplicationOrderService $loan_application_order_service)
    {
        $this->loan_application_order_service = $loan_application_order_service;
    }

    public function storeOrderDetails(StoreLoanApplicationOrderDetailsRequest $request)
    {
        try {
            $payload = $request->only([
                'amount',
                'phone_number',
                'document_number',
                'order_id',
                'police_code',
                'vin',
                'released',
            ]);

            Log::info('Attempting to store loan application order details', ['payload' => $payload]);

            $this->startTransaction(function () use ($payload) {
                $result = $this->loan_application_order_service->storeOrderDetails($payload);

                $sms_service = resolve('App\Interfaces\ISmsService');
                $sms_service->send($result->phone_number, __('sms.ovil_app_redirect_msg', [
                    'app_redirect_link' => env('OVIL_APP_REDIRECT_LINK'),
                    'date' => Carbon::parse($result->created_at)->setTimezone(constants('ARM_TIMEZONE'))->format(constants('SMS_DATE_FORMAT')),
                ]));
            });

            return response()->json(['success' => true]);
        } catch (LoanApplicationOrderAlreadyExistsException | CitizenNotFoundException | InvalidDocumentException | ExpiredPassportException | InvalidSSNException $e) {
            Log::warning('Store Loan Application Order Details, '.get_class($e), ['message' => $e->getMessage()]);

            throw $e;
        } catch (Exception $e) {
            Log::error('Store Loan Application Order Details, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw new InternalErrorException();
        }
    }

    public function hasPendingApplicationOrder(CheckLoanApplicationOrderExistenceRequest $request)
    {
        try {
            $document_number = $request->get('document_number');

            $result = $this->loan_application_order_service->hasPendingApplicationOrder($document_number);

            Log::info('Has Pending Loan Application Order', ['document_number' => $document_number, 'exists' => $result]);

            return response()->json(['exists' => $result]);
        } catch (Exception $e) {
            Log::error('Has Pending Loan Application Order, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw new InternalErrorException();
        }
    }

    public function getOrders(Request $request)
    {
        try {
            $result = $this->loan_application_order_service->getOrdersByMerchant($request->searchableValue);

            $transformed_result = fractal($result, new LoanApplicationOrderTransformer());

            return response()->json($transformed_result);
        } catch (Exception $e) {
            Log::error('Get Loan Application Orders, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw new InternalErrorException();
        }
    }

    public function getMerchantDetails()
    {
        try {
            $result = $this->loan_application_order_service->resolveMerchantDetailsByAgent();

            return response()->json($result);
        } catch (Exception $e) {
            Log::error('Get Merchant Details, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw new InternalErrorException();
        }
    }

    public function getValidLoanApplicationOrder()
    {
        try {
            $result = $this->loan_application_order_service->getValidLoanApplicationOrder();
        } catch (LoanApplicationOrderNotFoundException $e) {
            $result = [];

            Log::warning('Get Valid Loan Application Order, LoanApplicationOrderNotFoundException', ['error' => $e->getMessage()]);
        } catch (ExpiredLoanApplicationOrderException $e) {
            Log::warning('Get Valid Loan Application Order, ExpiredLoanApplicationOrderException', ['error' => $e->getMessage()]);

            throw $e;
        } catch (Exception $e) {
            Log::error('Get Valid Loan Application Order, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw $e;
        }

        return response()->json($result);
    }

    public function getVehicles()
    {
        try {
            $vehicle_service = resolve('App\Interfaces\IVehicleService');
            $vehicles = $vehicle_service->getVehiclesMarks();

            return response()->json($vehicles);
        } catch (Exception $e) {
            Log::error('Get Vehicles, Exception', ['message' => $e->getMessage()]);

            throw new InternalErrorException();
        }
    }
}
