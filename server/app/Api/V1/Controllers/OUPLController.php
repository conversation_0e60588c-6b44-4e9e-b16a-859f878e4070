<?php

namespace App\Api\V1\Controllers;

use App\Api\V1\Requests\OWLApproveLoanRequests;
use App\Api\V1\Requests\OWLRequests;
use App\Api\V1\Transformers\LoanTransformer;
use App\Exceptions\BlacklistedException;
use App\Exceptions\CitizenNotFoundException;
use App\Exceptions\InternalErrorException;
use App\Exceptions\InvalidLoanException;
use App\Exceptions\InvalidSuuidException;
use App\Exceptions\PaymentFailureException;
use App\Exceptions\TransferTypeNotFoundException;
use App\Exceptions\UnpaidLoanException;
use App\Exceptions\UnprocessableApplicationException;
use App\Factory\OWLServiceFactory;
use App\Http\Controllers\Controller;
use App\Interfaces\IInternalRuleException;
use App\Interfaces\ILoanService;
use App\Interfaces\IPaymentService;
use App\Interfaces\ISecurityService;
use Exception;
use Illuminate\Support\Facades\Log;
use Throwable;

class OUPLController extends Controller
{
    protected $loan_service;
    protected $payment_service;
    protected $security_service;

    public function __construct(
        ILoanService $loan_service,
        IPaymentService $payment_service,
        ISecurityService $security_service
    ) {
        $this->loan_service = $loan_service;
        $this->payment_service = $payment_service;
        $this->security_service = $security_service;
    }

    public function getTerms(OWLRequests $request)
    {
        $amount = $request->get('amount');

        // TODO: this is temporary solution
        if ($amount == 10000) {
            $amount = 20000;
        }

        $terms = $this->loan_service->getTerms($amount);

        return view('wallet.oupl.terms', $terms);
    }

    public function confirmTerms()
    {
        return view('wallet.oupl.confirm_terms');
    }

    public function getLoan()
    {
        try {
            $loan_type_id = constants('LOAN_TYPES.OUPL');
            $owl_service = OWLServiceFactory::build($loan_type_id);

            $payload = $owl_service->composeCitizenInfo();
            $citizen = $owl_service->getLoan($payload, $loan_type_id);

            return view('wallet.oupl.loan', $citizen);
        } catch (BlacklistedException $e) {
            Log::warning('Get Loan OUPL, BlacklistedException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (UnprocessableApplicationException $e) {
            Log::warning('Get Loan OUPL, UnprocessableApplicationException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (IInternalRuleException $e) {
            Log::warning('Get Loan OUPL, '.get_class($e), ['error' => $e->getMessage()]);
            throw $e;
        } catch (UnpaidLoanException $e) {
            Log::warning('Get Loan OUPL, UnpaidLoanException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (CitizenNotFoundException $e) {
            Log::warning('Get Loan OUPL, CitizenNotFoundException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException('Citizen not found', [], $e->getAction(), $e->getCase(), $e->getShouldAlert(), $e->getMeta());
        } catch (InternalErrorException $e) {
            Log::error('Get Loan OUPL, InternalErrorException', ['error' => $e->getTraceAsString()]);
            throw $e;
        } catch (Throwable $e) {
            Log::error('Get Loan OUPL, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function approveLoan(OWLApproveLoanRequests $request)
    {
        $payload = $request->only([
            'amount',
            'notification_method',
            'dispute_solution_method',
        ]);

        try {
            $loan_security = $this->security_service->resolveLoanSecurity();

            $payload = array_merge($payload, [
                'document_number' => $loan_security->document_number,
                'email' => $loan_security->email,
                'phone_number' => $loan_security->phone_number,
                'monthly_payment' => null,
                'token' => $loan_security->owl_token,
            ]);

            Log::info('Approve loan', ['payload' => $payload]);
            $loan = $this->loan_service->create($payload);
            Log::info('Loan approve response', ['response' => $loan]);

            $this->storeTransfer($loan);

            return fractal($loan, new LoanTransformer());
        } catch (InvalidSuuidException $e) {
            Log::warning('Get transfer types, InvalidSuuidException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (InvalidLoanException $e) {
            Log::warning('Approve loan, InvalidLoanException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (BlacklistedException $e) {
            Log::warning('Approve loan, BlacklistedException', ['error' => $e->getTraceAsString()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Approve loan, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    private function storeTransfer($loan)
    {
        $params['transfer_type'] = constants('TRANSFER_TYPES.WALLET_LOAN');

        try {
            Log::info('Storing OUPL transfer', ['loan' => $loan, 'params' => $params]);
            $this->payment_service->storeTransferMethod($loan, $params);
            Log::debug('OUPL transfer stored');

            // Setting sign date is required for document generation
            $loan = $this->loan_service->setSignDateAndApr($loan);

            Log::debug('Generating loan Documents');
            /** @var IDocumentService $loanDocumentService */
            $loan_document_service = resolve('App\Interfaces\IDocumentService');
            $loan_document_service->getLoanDocumentsJob($loan)->dispatch($loan, $force = true, true);
            Log::debug('Loan Documents generation successfully dispatched');
        } catch (PaymentFailureException $e) {
            Log::warning('Store transfer, PaymentFailureException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (TransferTypeNotFoundException $e) {
            Log::error('Store transfer, TransferTypeNotFoundException', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    public function getAgreement()
    {
        $loan_security = $this->security_service->resolveLoanSecurity();
        $loan = $loan_security->loan;
        /** @var IDocumentService $loanDocumentService */
        $loan_document_service = resolve('App\Interfaces\IDocumentService');
        $data = $loan_document_service->composePdfData($loan);

        return view('wallet.oupl.agreement')->with($data);
    }

    public function getContract()
    {
        /** @var IOWLService $owl_service */
        $owl_service = OWLServiceFactory::build(constants('LOAN_TYPES.OUPL'));

        $loan_security = $this->security_service->resolveLoanSecurity();

        $contract_data = $owl_service->composeContractData($loan_security);

        return view('wallet.oupl.contract')->with($contract_data);
    }

    public function getConfirmation($status)
    {
        if (!in_array($status, ['success', 'fail'])) {
            abort(404);
        }

        return view('wallet.oupl.confirmation')->with(['status' => $status]);
    }

    public function down()
    {
        return view('wallet.down');
    }
}
