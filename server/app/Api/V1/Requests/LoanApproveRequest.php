<?php

namespace App\Api\V1\Requests;

use Dingo\Api\Http\FormRequest;
use Illuminate\Validation\Rules\RequiredIf;

class LoanApproveRequest extends FormRequest
{
    public function rules()
    {
        $loan_type_obl = constants('LOAN_TYPES.OBL');

        return [
            'amount' => 'required|numeric',
            'monthly_payment' => [
                'numeric',
                new RequiredIf($this->input('loan_type_id') != $loan_type_obl),
            ],
            'months' => [
                'numeric',
                new RequiredIf($this->input('loan_type_id') != $loan_type_obl),
            ],
            'last_month_payment' => [
                'numeric',
                new RequiredIf($this->input('loan_type_id') != $loan_type_obl),
            ],
            'total' => [
                'numeric',
                new RequiredIf($this->input('loan_type_id') != $loan_type_obl),
            ],
            'prepayment' => 'nullable|numeric',
        ];
    }

    public function authorize()
    {
        return true;
    }
}
