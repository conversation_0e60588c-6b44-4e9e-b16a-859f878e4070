<?php

namespace App\Api\V1\Requests;

use Dingo\Api\Http\FormRequest;

class PayLaterRequest extends FormRequest
{
    public function rules()
    {
        return [
            'email' => 'required|email',
            'dispute_solution_method' => 'required|string',
            'notification_method' => 'required|string',
        ];
    }

    public function authorize()
    {
        return true;
    }
}
