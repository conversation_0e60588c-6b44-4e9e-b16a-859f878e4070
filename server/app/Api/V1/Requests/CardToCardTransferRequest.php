<?php

namespace App\Api\V1\Requests;

use Dingo\Api\Http\FormRequest;

class CardToCardTransferRequest extends FormRequest
{
    public function rules()
    {
        $rules = [
            'card_number' => 'required|numeric|digits:16',
            'year' => 'required|year',
            'month' => 'required|month',
        ];

        return $rules;
    }

    public function authorize()
    {
        return true;
    }
}
