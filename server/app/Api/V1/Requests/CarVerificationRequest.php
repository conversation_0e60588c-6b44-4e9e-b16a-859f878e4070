<?php

namespace App\Api\V1\Requests;

use Dingo\Api\Http\FormRequest;

class CarVerificationRequest extends FormRequest
{
    public function rules()
    {
        $rules = [
            'checkup_date' => 'required|date|vehicle_check_up_date',
            'address' => 'required|string',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'notes' => 'string',
        ];

        return $rules;
    }

    public function authorize()
    {
        return true;
    }
}
