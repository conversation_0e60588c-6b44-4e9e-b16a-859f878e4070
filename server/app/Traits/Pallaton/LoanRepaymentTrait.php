<?php

namespace App\Traits\Pallaton;

use App\Models\Pallaton\LoanRepayment;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Log;

trait LoanRepaymentTrait
{
    protected function isRepaymentStatusProcessing($order_id): bool
    {
        try {
            $repayment = LoanRepayment::whereOrderId($order_id)->firstOrFail();

            $is_processing = $repayment->status === LoanRepayment::PROCESSING;
            Log::info('Checking loan repayment status in DB', [
                'order_id' => $order_id,
                'current_status' => $repayment->status,
                'is_processing' => $is_processing,
            ]);

            return $is_processing;
        } catch (ModelNotFoundException $e) {
            Log::warning('Loan Repayment not found in DB', [
                'order_id' => $order_id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    protected function createLoanRepayment($payload)
    {
        return LoanRepayment::create(array_merge($payload, [
            'status' => LoanRepayment::PENDING,
        ]));
    }
}
