<?php

namespace App\Traits;

use App\Exceptions\CitizenNotFoundException;
use Log;

trait CheckNorkResponse
{
    public function validate($response)
    {
        $action = constants('ALERT.ACTIONS.NORK');

        $this->checkIfDataExists($response, $action);
    }

    public function getCheckCases()
    {
        // get validation methods of class
        return preg_grep('~^check~', get_class_methods($this));
    }

    private function getCitizenNotFoundException($action = null, $case = null)
    {
        return new CitizenNotFoundException('Citizen not found', $action, $case, true);
    }

    public function checkIfDataExists($data, $action)
    {
        if (!$data) {
            Log::error('Get citizen from NORK, CitizenNotFoundException', ['response' => $data, 'case' => __FUNCTION__]);

            throw $this->getCitizenNotFoundException($action, __FUNCTION__);
        }
    }
}
