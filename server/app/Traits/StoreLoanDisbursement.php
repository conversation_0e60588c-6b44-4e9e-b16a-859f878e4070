<?php

namespace App\Traits;

use App\Models\LoanDisbursement;
use Exception;
use Log;

trait StoreLoanDisbursement
{
    public function storeLoanDisbursementRef($type, $disbursement_id, $loan_security_id, $date)
    {
        Log::info(
            "Storing loan {$type} disbursement",
            ['disbursement_type' => $type, 'disbursement_id' => $disbursement_id, 'loan_security_id' => $loan_security_id, 'date' => $date]
        );
        try {
            $loan_disbursement = LoanDisbursement::create([
                'loan_security_id' => $loan_security_id,
                'disbursement_type' => $type,
                'disbursement_id' => $disbursement_id,
                'date' => $date,
            ]);

            Log::info("Loan {$type} disbursement stored",
                ['disbursement_type' => $type, 'disbursement_id' => $disbursement_id, 'loan_security_id' => $loan_security_id, 'date' => $date]
            );

            return $loan_disbursement;
        } catch (Exception $e) {
            Log::error("Failed to store loan {$type} disbursement", ['message' => $e->getMessage()]);
            throw $e;
        }
    }
}
