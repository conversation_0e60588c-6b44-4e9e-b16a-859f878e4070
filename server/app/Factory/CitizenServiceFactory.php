<?php

namespace App\Factory;

use App\Services\CitizenServiceCommon;
use App\Services\CitizenServiceOASL;
use App\Services\CitizenServiceOCL;
use App\Services\CitizenServiceOIQL;
use App\Services\CitizenServiceOVL;
use App\Services\CitizenServiceOIWL;
use App\Services\CitizenServiceOWL;
use App\Services\CitizenServiceREML;
use App\Services\CitizenServiceVLX;
use App\Services\CreditLine\CitizenServiceBNPL;
use App\Services\CreditLine\CitizenServicePL;
use App\Services\CitizenServiceOBL;
use App\Services\SecurityUtilityService;

class CitizenServiceFactory
{
    public static function build($loan_type_id)
    {
        if ($loan_type_id == constants('LOAN_TYPES.OCL')) {
            return new CitizenServiceOCL();
        } elseif ($loan_type_id == constants('LOAN_TYPES.OVL')) {
            return new CitizenServiceOVL();
        } elseif ($loan_type_id == constants('LOAN_TYPES.OIQL')) {
            return new CitizenServiceOIQL();
        } elseif ($loan_type_id == constants('LOAN_TYPES.OASL')) {
            return new CitizenServiceOASL();
        } elseif (SecurityUtilityService::isOwlLoan($loan_type_id)) {
            return new CitizenServiceOWL($loan_type_id);
        } elseif (SecurityUtilityService::isREML($loan_type_id)) {
            return new CitizenServiceREML();
        } elseif ($loan_type_id == constants('LOAN_TYPES.VLX')) {
            return new CitizenServiceVLX($loan_type_id);
        } elseif ($loan_type_id == constants('LOAN_TYPES.PL')) {
            return new CitizenServicePL();
        } elseif ($loan_type_id == constants('LOAN_TYPES.BNPL')) {
            return new CitizenServiceBNPL();
        } elseif ($loan_type_id == constants('LOAN_TYPES.OIWL')) {
            return new CitizenServiceOIWL();
        } elseif (SecurityUtilityService::isOBL($loan_type_id)) {
            return new CitizenServiceOBL();
        } else {
            return new CitizenServiceCommon();
        }
    }
}
