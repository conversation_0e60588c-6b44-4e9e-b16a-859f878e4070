<?php

namespace App\Factory;

use App\Services\CreditLine\BNPLService;
use App\Services\CreditLine\InternalBNPLService;
use App\Services\CreditLine\OBLService;
use App\Services\CreditLine\PayLaterService;
use App\Services\SecurityUtilityService;

class CreditLineServiceFactory
{
    public static function build($indentifier)
    {
        if (SecurityUtilityService::isPayLaterVendor($indentifier)) {
            return new PayLaterService();
        } elseif (SecurityUtilityService::isInternalBNPLVendor($indentifier)) {
            return new InternalBNPLService();
        } elseif (SecurityUtilityService::isBNPLVendor($indentifier)) {
            return new BNPLService();
        } elseif (SecurityUtilityService::isOBL($indentifier)) {
            return new OBLService();
        }
    }
}
