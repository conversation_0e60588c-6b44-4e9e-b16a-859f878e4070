<?php

namespace App\Factory;

use App;
use App\Services\CreditLine\HcServiceBNPL;
use App\Services\CreditLine\HcServiceOBL;
use App\Services\CreditLine\HcServicePL;
use App\Services\DevHcService;
use App\Services\HcServiceCommon;
use App\Services\HcServiceOASL;
use App\Services\HcServiceOCL;
use App\Services\HcServiceOEPL;
use App\Services\HcServiceOFSL;
use App\Services\HcServiceOIDL;
use App\Services\HcServiceOIQL;
use App\Services\HcServiceOIWL;
use App\Services\HcServiceOTCL;
use App\Services\HcServiceOUPL;
use App\Services\HcServiceOVL;
use App\Services\HcServiceREML;
use App\Services\HcServiceVLX;

class HcServiceFactory
{
    public static function build($type = null)
    {
        if (App::isLocal() || App::runningUnitTests()) {
            return new DevHcService($type);
        }

        if ($type === constants('LOAN_TYPES.OCL')) {
            return new HcServiceOCL();
        } elseif ($type === constants('LOAN_TYPES.OVL')) {
            return new HcServiceOVL();
        } elseif ($type === constants('LOAN_TYPES.OIQL')) {
            return new HcServiceOIQL();
        } elseif ($type === constants('LOAN_TYPES.OASL')) {
            return new HcServiceOASL();
        } elseif ($type === constants('LOAN_TYPES.OIDL')) {
            return new HcServiceOIDL();
        } elseif ($type === constants('LOAN_TYPES.PL')) {
            return new HcServicePL();
        } elseif ($type === constants('LOAN_TYPES.OTCL')) {
            return new HcServiceOTCL();
        } elseif ($type === constants('LOAN_TYPES.OEPL')) {
            return new HcServiceOEPL();
        } elseif ($type === constants('LOAN_TYPES.REML')) {
            return new HcServiceREML();
        } elseif ($type === constants('LOAN_TYPES.VLX')) {
            return new HcServiceVLX();
        } elseif ($type === constants('LOAN_TYPES.BNPL')) {
            return new HcServiceBNPL();
        } elseif ($type === constants('LOAN_TYPES.OIWL')) {
            return new HcServiceOIWL();
        } elseif ($type === constants('LOAN_TYPES.OUPL')) {
            return new HcServiceOUPL();
        } elseif ($type === constants('LOAN_TYPES.OBL')) {
            return new HcServiceOBL();
        } elseif ($type === constants('LOAN_TYPES.OFSL')) {
            return new HcServiceOFSL();
        } else {
            // The cases when we don't have loan_type_id, but we need to use some HC methods,
            // e.g., Client update during every Ekeng call
            return new HcServiceCommon();
        }
    }
}
