<?php

namespace App\Imports\BankReportParsers;

use App\Abstracts\AbstractBankReportParser;
use App\Helpers\AnsiToUnicode;
use App\Interfaces\IBankReportParserService;
use PhpOffice\PhpSpreadsheet\Shared\Date;

class AmeriaParser extends AbstractBankReportParser implements IBankReportParserService
{
    public function parse($rows)
    {
        $result = [];

        foreach ($rows as $row_key => $row) {
            if ($this->isPaymentRow($row)) {
                if ((int) $row[6] === 0) {
                    continue;
                }

                $purposeTextUnicode = AnsiToUnicode::convert($row[4]);
                $payment_type = $this->getPaymentType($purposeTextUnicode);

                $result[] = [
                    'date' => Date::excelToDateTimeObject($row[0])->format('Y-m-d'),
                    'contract_number' => $this->extractContractNumberFromText($row[4]),
                    'payment_type' => $payment_type,
                    'amount' => (int) $row['6'],
                ];
            }
        }

        return $result;
    }


    /**
     * There are 2 types of Ameria bank reports, one of them has null in the last column, which breaks the default logic.
     * Thus we are iterating till before last
     * @param array $row - row, 1-d array
     * @return boolean
     */
    protected function isPaymentRow($row)
    {
        $count = count($row);
        for ($i = 0; $i < $count-1; $i++) {
            if ($row[$i] === null) {
                return false;
            }
        }
        return true;
    }

}
