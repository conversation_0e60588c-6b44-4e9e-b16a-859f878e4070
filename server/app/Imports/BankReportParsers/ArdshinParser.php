<?php

namespace App\Imports\BankReportParsers;

use App\Abstracts\AbstractBankReportParser;
use App\Helpers\AnsiToUnicode;
use App\Helpers\ArrayHelper;
use App\Interfaces\IBankReportParserService;

class ArdshinParser extends AbstractBankReportParser implements IBankReportParserService
{
    const NUMBER_OF_TRANSACTION_COLUMNS = 8;
    const DATE_STRING = 'Սկզբնական մնացորդ';
    const UNIQUE_KEYWORD = 'Worksheet';

    public function parse($rows)
    {
        $result = [];
        $table_rows = $rows[self::UNIQUE_KEYWORD]["Table"]["Row"];

        // Ardshin bank uses daily report, where date is located just in one place with string Սկզբնական մնացորդ
        $date_string = $this->findByKeyword($table_rows, self::DATE_STRING);
        $date = $this->getDate($date_string);

        foreach ($table_rows as $k => $row) {
            if ($this->isNotTransactionRow($row)) {
                continue;
            }

            $result[] = [
                'date' =>  $date,
                'check_number' => $this->getCheckNumber($row['Cell'][0]['Data']),
                'contract_number' => $this->extractContractNumberFromText($row['Cell'][6]['Data']),
                'payment_type' => $this->getPaymentType($row['Cell'][7]['Data']),
                'amount' => (int) str_replace( ",", "", $row['Cell'][3]['Data']),
            ];
        }

        return $result;
    }

    /**
     * @param $row
     * @return bool
     */
    private function isNotTransactionRow($row)
    {
        return empty($row) || !ArrayHelper::is_assoc($row) || ArrayHelper::is_assoc($row['Cell'])
            || count($row['Cell']) != self::NUMBER_OF_TRANSACTION_COLUMNS
            || (int)$row['Cell'][2]['Data'] != 0;
    }


    /**
     * Finds cell containing the given $keyword
     * @param array $array - Sheet, 2-d array with rows and columns
     * @param string $keyword - string which need to be found
     * @return string
     */
    private function findByKeyword($array, $keyword)
    {
        foreach ($array as $row) {
            if (empty($row)) {
                continue;
            }
            $cell = $row["Cell"];
            if (ArrayHelper::is_assoc($cell)) {
                $string = AnsiToUnicode::convert($cell['Data']);
                if (str_contains($string, $keyword)) {
                    return $string;
                }
            } else {
                foreach ($cell as $column) {
                    $string = AnsiToUnicode::convert($column['Data']);
                    if (str_contains($string, $keyword)) {
                        return $string;
                    }
                }
            }
        }
        return null;
    }


    /**
     * Extract and reformat date from given string
     * @param string $date_string
     * @return string
     */
    protected function getDate($date_string)
    {
        $is_matched = preg_match_all('/\d{2}\/\d{2}\/\d{2}/', $date_string,$matches);
        if ($is_matched == 0){
            return '';
        }

        list($day, $month, $year) = explode('/', $matches[0][0]);
        return '20'.$year.'-'.$month.'-'.$day;
    }

}
