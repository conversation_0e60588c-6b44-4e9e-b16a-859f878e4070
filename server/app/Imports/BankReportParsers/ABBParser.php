<?php

namespace App\Imports\BankReportParsers;

use App\Abstracts\AbstractBankReportParser;
use App\Helpers\AnsiToUnicode;
use App\Interfaces\IBankReportParserService;
use PhpOffice\PhpSpreadsheet\Shared\Date;

class ABBParser extends AbstractBankReportParser implements IBankReportParserService
{
    public function parse($rows)
    {
        $result = [];

        foreach ($rows as $row_key => $row) {
            if ($this->isPaymentRow($row)) {
                if ((int) $row[6] === 0) {
                    continue;
                }

                $purposeTextUnicode = AnsiToUnicode::convert($row[4]);
                $payment_type = $this->getPaymentType($purposeTextUnicode);

                $result[] = [
                    'date' => Date::excelToDateTimeObject($row[0])->format('Y-m-d'),
                    'contract_number' => $this->extractContractNumberFromText($row[4]),
                    'payment_type' => $payment_type,
                    'amount' => (int) $row['6'],
                ];
            }
        }

        return $result;
    }

}
