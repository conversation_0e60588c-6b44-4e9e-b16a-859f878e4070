<?php

namespace App\Interfaces;

interface IAWSService
{
    public function getPdfStorageName();

    public function getMediaStorageName();

    public function getQRCodeStorageName();

    public function getReferralCodePdfStorageName();

    public function storeDocument($path, $content);

    public function storeMortgageMedia($path, $file);

    public function storeQRCode($path, $file);

    public function getEkengMedia($path);

    public function storeEkengMedia($path, $content);

    public function getEkengStorageName();

    public function getFaceRecognitionStorageName();

    public function storeRecognitionMedia($path, $content);
}
