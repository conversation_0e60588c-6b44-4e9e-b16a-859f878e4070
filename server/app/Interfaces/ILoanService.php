<?php

namespace App\Interfaces;

interface ILoanService
{
    public function create($loan_details);

    public function processLoanConfirmation($loan);

    public function getSignDate($loan);

    public function setSignDate($loan);

    public function setSignDateAndApr($loan);

    public function updatePersonalInfo($loan, $details);

    public function storeAdditionalPhoneNumber($additional_phone_number);

    public function getLoanByPublicId($public_id);

    public function calculateMonthlyPaymentRange($payload);

    public function calculateScheduleDetails($payload);
}
