<?php

namespace App\Nova\Actions;

use App\Jobs\GenerateLoanDocuments;
use App\Models\Region;
use App\Models\Village;
use Epartment\NovaDependencyContainer\NovaDependencyContainer;
use function Functional\reduce_left;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;

class EditSolarPanelAddress extends BaseAction
{
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public $onlyOnDetail = true;

    private $loan = null;

    public function __construct($loan)
    {
        $this->loan = $loan;
    }

    public function name()
    {
        return __('Edit Address');
    }

    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $model) {
            $model->solar_panel()->update([
                'address' => $fields->address,
                'region_id' => $fields->region_id,
                'village_id' => $fields->village_id,
            ]);

            GenerateLoanDocuments::dispatch($model, false);
        }
    }

    public function fields()
    {
        $regions = Region::all()->pluck('name', 'id')->toArray();
        $village_groups = Village::all()->groupBy('region_id')->toArray();

        $address = $this->loan->solar_panel->address ?? null;
        $region_id = $this->loan->solar_panel->region->id ?? 0;
        $village_id = $this->loan->solar_panel->village->id ?? 0;

        $fields = [
            Select::make(__('region'), 'region_id')
                ->options($regions)
                ->withMeta(['value' => $region_id])
                ->rules('required')
                ->displayUsingLabels(),
        ];

        $fields = $this->setVillages($village_groups, $fields, $village_id);

        return array_merge($fields, [Text::make(__('Address'), 'address')->withMeta(['value' => $address])]);
    }

    private function setVillages($village_groups, $fields, $default)
    {
        return reduce_left($village_groups, function ($villages, $key, $collection, $fields) use ($default) {
            array_push($fields, NovaDependencyContainer::make([
                Select::make(__('village'), 'village_id')
                    ->options(collect($villages)->pluck('name', 'id'))
                    ->withMeta(['value' => $default])
                    ->rules('required')
                    ->displayUsingLabels(),
            ])->dependsOn('region_id', $key));

            return $fields;
        }, $fields);
    }
}
