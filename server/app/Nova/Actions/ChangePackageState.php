<?php

namespace App\Nova\Actions;

use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Select;

class ChangePackageState extends BaseAction
{
    /**
     * @return string
     */
    public function name()
    {
        return __('ChangePackageState');
    }

    /**
     * Perform the action on the given models.
     *
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        $state = $fields->state;

        foreach ($models as $model) {
            $model->update(['state' => $state]);

            $arpi_solar_loans = $model->arpi_solar_loans;

            foreach ($arpi_solar_loans as $arpi_solar_loan) {
                $arpi_solar_loan->solar_panel->update(['state' => $state]);
            }
        }
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields()
    {
        return [
            Select::make(__('State'), 'state')
                ->options([
                    'NOT_PRESENTED' => __('Not presented'),
                    'PRESENTED' => __('Presented'),
                    'REFINANCED' => __('Refinanced'),
                    'NOT_REFINANCED' => __('Not refinanced')
                ])
        ];
    }
}
