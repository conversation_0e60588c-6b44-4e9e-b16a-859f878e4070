<?php

namespace App\Nova\Actions\Stabilization;

use App\Nova\Actions\BaseAction;
use File;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use <PERSON><PERSON>\Nova\Fields\ActionFields;
use ZipArchive;

class DownloadLoanDocuments extends BaseAction
{
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private $zip = null;

    public function __construct(ZipArchive $zip)
    {
        $this->zip = $zip;
    }

    public function name()
    {
        return __('Download Loan Documents');
    }

    /**
     * Perform the action on the given models.
     *
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        // storing
        $path = env('NOVA_DOWNLOADS_PATH');
        $directory = public_path($path);

        $this->zip = new ZipArchive();
        $zip_name = $this->composeArchiveName();

        if (!File::exists($directory)) {
            File::makeDirectory($directory);
            chmod($directory, 0777);
        }

        if ($this->zip->open("$directory/$zip_name", ZipArchive::CREATE) === true) {
            /* @var Loan $model */
            foreach ($models as $loan) {
                $documents = $loan->documents;
                $folder_name = $this->composeFolderName($loan->citizen, $loan->id);

                $this->addDocuments($documents, $folder_name);
            }

            $this->zip->close();

            return Action::download("$path/$zip_name", $zip_name);
        }
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields()
    {
        return [];
    }

    protected function addDocuments($documents, $folder_name)
    {
        foreach ($documents as $document) {
            $download_file = file_get_contents($document->full_path_for_download);

            $this->zip->addFromString($folder_name.'/'.$document->document_type.'.pdf', $download_file);
        }
    }

    protected function composeArchiveName()
    {
        return 'LOAN_DOCUMENTS_'.time().'.zip';
    }

    protected function composeFolderName($citizen, $loan_id)
    {
        return $citizen->first_name.'-'.$citizen->last_name.'-'.$citizen->middle_name.'-'.$loan_id;
    }
}
