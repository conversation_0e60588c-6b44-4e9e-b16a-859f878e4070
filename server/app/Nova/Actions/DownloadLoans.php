<?php

namespace App\Nova\Actions;

use App\Helpers\ObjectMerger;
use App\Models\CardToCardPayment;
use App\Models\CashPayment;
use App\Models\IdramWalletPayment;
use App\Schemas\ContextSchema;
use function Functional\first;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Rap2hpoutre\FastExcel\FastExcel;

class DownloadLoans extends BaseAction
{
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    protected $file_name = 'Loans';

    public function name()
    {
        return __('DownloadLoans');
    }

    /**
     * Perform the action on the given models.
     *
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        // storing
        $path = env('NOVA_DOWNLOADS_PATH');
        $directory = public_path($path);
        $now = now()->setTimezone(constants('ARM_TIMEZONE'))->format(constants('FILE_TIME_FORMAT'));

        $file = $this->file_name."_$now.xlsx";

        (new FastExcel($models))->export($directory."/$file", function ($loan) {
            return $this->getExcelFields($loan);
        });

        return Action::download("$path/$file", $file);
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields()
    {
        return [];
    }

    protected function getExcelFields($loan)
    {
        $excel_fields = [
            __('Sign Date') => $this->getSignDate($loan),
            __('Passport Number') => $this->getPassport($loan),
            __('Soc Card') => $loan->citizen->getSocCard()['passport_number'],
            __('First Name') => $loan->citizen->first_name,
            __('Last Name') => $loan->citizen->last_name,
            __('Middle Name') => $loan->citizen->middle_name,
            __('Gender') => $loan->citizen->gender === 1 ? __('Male') : __('Female'),
            __('Birthday') => $this->getBirthday($loan),
            __('Salary') => $this->getSalary($loan),
            __('Fico') => $loan->fico_score,
            __('Phone Number') => $loan->citizen->phone_number,
            __('Transfer Type') => $this->getTransferType($loan),
            __('Card Number') => $this->getCardNumber($loan),
            __('Amount') => $loan->amount,
        ];

        if (auth()->user()->isAdmin()) {
            $excel_fields[__('Selected Loan Type')] = $this->getSelectedLoanType($loan);
        }

        return $excel_fields;
    }

    protected function getSalary($loan)
    {
        $metas = $loan->loan_security->security_metas;
        $citizen_info = first($metas, function ($data) {
            if ($data->step === 'CITIZEN_INFO') {
                return $data;
            }
        });

        if (!$citizen_info) {
            return '';
        }

        $contextSchema = new ContextSchema();
        $content = json_decode($citizen_info->content, true);
        $objectMerger = new ObjectMerger($contextSchema->get());
        $sum = $objectMerger->merge($content);

        return $sum['salary'];
    }

    protected function getTransferType($loan)
    {
        if ($loan->payment instanceof CashPayment) {
            return __('Cash');
        } elseif ($loan->payment instanceof CardToCardPayment) {
            return __('Card to Card');
        } elseif ($loan->payment instanceof IdramWalletPayment) {
            return __('Idram');
        } else {
            return '';
        }
    }

    protected function getCardNumber($loan)
    {
        if ($loan->payment instanceof CashPayment) {
            return '';
        } elseif ($loan->payment instanceof CardToCardPayment) {
            return $loan->payment->card_number;
        } elseif ($loan->payment instanceof IdramWalletPayment) {
            return $loan->payment->wallet_id;
        } else {
            return '';
        }
    }

    protected function getBirthday($loan)
    {
        if (!$loan->citizen->birth_date) {
            return '';
        }

        return $loan->citizen->birth_date->setTimezone(constants('ARM_TIMEZONE'))->format(constants('BIRTHDAY_DATE_FORMAT'));
    }

    protected function getSignDate($loan)
    {
        if (!$loan->sign_date) {
            return '';
        }

        return $loan->sign_date->setTimezone(constants('ARM_TIMEZONE'))->format(constants('SMS_DATE_FORMAT'));
    }

    protected function getPassport($loan)
    {
        $passport = $loan->citizen->getPrimaryDocument();

        return "{$passport['passport_number']}({$passport['from']},{$passport['given_date']->format(constants('EKENG_DATE_FORMAT'))})";
    }

    protected function getSelectedLoanType($loan)
    {
        if (!$loan->loan_security->selected_loan_type) {
            return '';
        }

        return __($loan->loan_security->selected_loan_type->name);
    }
}
