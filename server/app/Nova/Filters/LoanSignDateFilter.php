<?php

namespace App\Nova\Filters;

use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Ampeco\Filters\DateRangeFilter;

class LoanSignDateFilter extends DateRangeFilter
{
    public function name() {
        return __('LoanSignDateFilter');
    }

    /**
     * Apply the filter to the given query.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(Request $request, $query, $value)
    {
        $startDate = $value[0];
        $endDate = $value[1];
        return $query
            ->where('sign_date', '>=', Carbon::parse($startDate, constants('ARM_TIMEZONE'))->setTimezone('UTC'))
            ->where('sign_date', '<=', Carbon::parse($endDate ?? $startDate, constants('ARM_TIMEZONE'))->endOfDay()->setTimezone('UTC'));
    }
}