<?php

namespace App\Nova\Filters;

use App\Models\CashPayment;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Filters\Filter;

class WithdrawCompanyFilter extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    public function name()
    {
        return __('Withdraw Company Filter');
    }

    /**
     * Apply the filter to the given query.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param mixed                                 $value
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(Request $request, $query, $value)
    {
        return $query->where('type', $value);
    }

    /**
     * Get the filter's available options.
     *
     * @return array
     */
    public function options(Request $request)
    {
        return [
            __('Global Credit') => CashPayment::GLOBAL_CREDIT,
            __('Easypay') => CashPayment::EASYPAY,
            __('Upay') => CashPayment::UPAY,
            __('Telcell') => CashPayment::TELCELL,
        ];
    }
}
