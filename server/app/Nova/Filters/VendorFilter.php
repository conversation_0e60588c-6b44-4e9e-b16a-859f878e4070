<?php

namespace App\Nova\Filters;

use App\Models\CreditLine\Vendor;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Filters\Filter;

class VendorFilter extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    public function name()
    {
        return __('Vendor');
    }

    /**
     * Apply the filter to the given query.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param mixed                                 $value
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(Request $request, $query, $value)
    {
        if ($request->user()->can('view-bnpl-transactions') || $request->user()->isAdmin()) {
            return $query->where('transactions.vendor_id', $value);
        }

        return $query->where('vendor_id', $value);
    }

    /**
     * Get the filter's available options.
     *
     * @return array
     */
    public function options(Request $request)
    {
        if ($request->user()->can('view-bnpl-transactions') || $request->user()->isAdmin()) {
            return Vendor::whereIn('type', [
                constants('VENDOR_TYPES.PAY_LATER'),
                constants('VENDOR_TYPES.INTERNAL_BNPL'),
                constants('VENDOR_TYPES.BNPL'),
                constants('VENDOR_TYPES.ARMED'),
            ])->pluck('id', 'name')->all();
        }

        return Vendor::pluck('id', 'name')->all();
    }
}
