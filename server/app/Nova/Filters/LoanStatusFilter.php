<?php

namespace App\Nova\Filters;

use Illuminate\Http\Request;
use Laravel\Nova\Filters\Filter;

class LoanStatusFilter extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    public function name()
    {
        return __('Status');
    }

    /**
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param mixed                                 $value
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(Request $request, $query, $value)
    {
        return $query->where('status', $value);
    }

    /**
     * @return array
     */
    public function options(Request $request)
    {
        return [
            'Processing' => 'PROCESSING',
            'Processed' => 'PROCESSED',
            'Review' => 'REVIEW',
            'Pledged' => 'PLEDGED',
            'Confirmed' => 'CONFIRMED',
            'Rejected' => 'REJECTED',
        ];
    }
}
