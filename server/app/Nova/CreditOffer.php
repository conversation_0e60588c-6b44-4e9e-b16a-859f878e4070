<?php

namespace App\Nova;

use App\Helpers\NumberHelper;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\Date;
use <PERSON>vel\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON><PERSON>lius\SearchRelations\SearchesRelations;

class CreditOffer extends Resource
{
    use SearchesRelations;

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = 'App\Models\CreditOffer';

    /**
     * @var string[]
     */
    public static $with = ['affected_rules', 'loan_types', 'loan_security'];

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [];

    public static $searchRelations = [
        'loan_security' => ['ssn', 'document_number'],
    ];

    public static function label()
    {
        return __('Credit Offers');
    }

    /**
     * @return bool
     */
    public static function availableForNavigation(Request $request)
    {
        return $request->user()->hasPermissionTo('view-credit-offers', config('nova.guard'));
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            ID::make()->sortable()->onlyOnDetail(),

            Text::make(__('Passport Number'), function () {
                return $this->loan_security->document_number;
            }),

            Text::make(__('Soc Card'), function () {
                return $this->loan_security->ssn;
            }),

            Number::make(__('Loan Type'), function () {
                return $this->loan_types->name;
            }),

            Date::make(__('Credit Offer Date'), function () {
                return $this->created_at->setTimezone(constants('ARM_TIMEZONE'))->format(constants('SMS_DATE_FORMAT'));
            }),

            Number::make(__('Amount'), 'amount')->min(0)->displayUsing(function ($v) {
                return NumberHelper::numberToStringDram($v);
            })->onlyOnDetail(),

            Number::make(__('Duration'), 'duration')->onlyOnDetail(),

            Number::make(__('Rate'), 'rate')->onlyOnDetail(),

            Number::make(__('Amount'), 'amount'),

            Number::make(__('Service Fee Rate'), 'service_fee_rate')
                ->withMeta(['value' => $this->service_fee_rate ?? '___'])
                ->onlyOnDetail(),

            Boolean::make(__('Approved'))->values($this->rejected, $this->rejected),

            HasMany::make(__('Affected Rules'), 'affected_rules', 'App\Nova\AffectedRule'),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }
}
