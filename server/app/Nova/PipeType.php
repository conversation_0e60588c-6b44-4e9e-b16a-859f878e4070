<?php

namespace App\Nova;

use App\Helpers\NumberHelper;
use App\Models\PipeType as ModelsPipeType;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Fields\Heading;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Text;

class PipeType extends Resource
{
    public function __construct($resource)
    {
        parent::__construct($resource);
    }

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = 'App\Models\PipeType';

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'name',
    ];

    public static function availableForNavigation(Request $request)
    {
        return $request->user()->can('view-pipe-types');
    }

    public static function label()
    {
        return 'IQOS ';
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            Boolean::make(__('Disabled'), 'disabled')
                ->onlyOnForms()
                ->displayUsing(function ($v) {
                    return !$this->disabled;
                })
                ->sortable()
                ->trueValue('On')
                ->falseValue('Off'),

            Text::make(__('Name'), 'name')
                ->withMeta(['extraAttributes' => [
                    'readonly' => $this->resource->exists,
                ]])
                ->creationRules('required'),

            Text::make(__('Contract Name'), 'contract_name')
                ->withMeta(['extraAttributes' => [
                    'readonly' => $this->resource->exists,
                ]])
                ->creationRules('required'),

            Number::make(__('Price'), 'price')
                ->min(0)
                ->displayUsing(function ($v) {
                    return NumberHelper::numberToStringDram($v);
                })
                ->withMeta(['extraAttributes' => [
                    'readonly' => $this->resource->exists,
                ]])
                ->creationRules('required'),

            Number::make(__('Monthly Payment'), 'monthly_payment')
                ->min(0)
                ->displayUsing(function ($v) {
                    return NumberHelper::numberToStringDram($v);
                })
                ->withMeta(['extraAttributes' => [
                    'readonly' => $this->resource->exists,
                ]])
                ->creationRules('required'),

            Number::make(__('Duration Month'), 'duration')
                ->withMeta([
                    'extraAttributes' => [
                        'readonly' => $this->resource->exists,
                    ],
                ])
                ->creationRules('required'),

            Boolean::make(__('Active'), function () {
                return !$this->disabled;
            })
                ->sortable()
                ->trueValue('On')
                ->falseValue('Off'),

            Heading::make(__('Iqos Info Text'))
                ->asHtml(),
        ];
    }

    /**
     * Get a fresh instance of the model represented by the resource.
     *
     * @return mixed
     */
    public static function newModel()
    {
        $model = static::$model;
        $var = new $model();
        $var->rate = ModelsPipeType::DEFAULT_RATE;

        return $var;
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }
}
