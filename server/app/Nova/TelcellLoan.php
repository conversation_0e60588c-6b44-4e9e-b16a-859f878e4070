<?php

namespace App\Nova;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use <PERSON><PERSON>\Nova\Fields\Date;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class TelcellLoan extends Loan
{
    public static function availableForNavigation(Request $request)
    {
        return $request->user()->isAdmin();
    }

    public static function label()
    {
        return __('Loan');
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(Request $request)
    {
        return array_merge(
            parent::fields($request),
            [
                Text::make(__('Cashier'), function () {
                    $operator = $this->payment->withdraw_operator ?? null;

                    return $operator ? "$operator->first_name $operator->last_name" : null;
                })
                ->hideFromDetail()
                ->canSee(function ($request) {
                    return $request->user()->can('view-withdraw-details');
                }),
                Date::make(__('Withdrawn Date'), function () {
                    if ($this->payment && $this->payment->withdrawn) {
                        return $this->payment->withdrawn->setTimezone(constants('ARM_TIMEZONE'))->format(constants('WITHDRAW_DATE_FORMAT'));
                    }

                    return;
                })->hideFromDetail(),
                Date::make(__('Sign Date'), function () {
                    if ($this->sign_date) {
                        return $this->sign_date->setTimezone(constants('ARM_TIMEZONE'))->format(constants('SMS_DATE_FORMAT'));
                    }
                })->hideFromIndex(),
            ]
        );
    }

    public function filters(Request $request)
    {
        return [
            (new \App\Nova\Filters\LoanWithdrawDateFilter())->locale(env('NOVA_LOCALE')),
        ];
    }

    protected static function applySearch($query, $search)
    {
        $query->join('cash_payments', 'loans.payment_id', '=', 'cash_payments.id')
            ->join('users as agent', 'cash_payments.withdraw_operator_id', '=', 'agent.id')
            ->where(DB::raw("CONCAT(agent.first_name, ' ', agent.last_name)"), 'LIKE', '%'.$search.'%');

        return $query->orWhere(function ($query) use ($search) {
            parent::applySearch($query, $search);
        });
    }

    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query->withTelcellPayment($request->input('search'));
    }
}
