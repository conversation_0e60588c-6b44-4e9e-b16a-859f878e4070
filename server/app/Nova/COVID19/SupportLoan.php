<?php

namespace App\Nova\COVID19;

use App\Helpers\NumberHelper;
use App\Nova\Loan;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Date;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

// Tmp For-Support-Credit
class SupportLoan extends Loan
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = 'App\Models\COVID19\Loan';

    public static function availableForNavigation(Request $request)
    {
        return $request->user()->hasPermissionTo('view-loan-support', config('nova.guard'));
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            ID::make()->sortable()->hideFromIndex(),

            Select::make(__('Status'), 'status')
                ->options([
                    'PENDING' => 'Pending',
                    'VERIFIED' => 'Verified',
                    'CONFIRMED' => 'Confirmed',
                    'CONFIRMED_NO_PAY' => 'Confirmed without payment',
                ])
                ->hideFromIndex()
                ->canSee(function ($request) {
                    return $request->user()->can('view-all-details');
                }),

            Text::make(__('Passport Number'), function () {
                $passport = $this->citizen->getPrimaryDocument();

                return "{$passport['passport_number']}
                    ({$passport['from']},
                    {$passport['given_date']->format(constants('EKENG_DATE_FORMAT'))})";
            }),

            Text::make(__('Soc Card'), function () {
                return $this->citizen->getSocCard()['passport_number'];
            }),

            Text::make(__('First Name'), function () {
                return $this->citizen->first_name;
            }),

            Text::make(__('Last Name'), function () {
                return $this->citizen->last_name;
            }),

            Text::make(__('Middle Name'), function () {
                return $this->citizen->middle_name;
            }),

            Text::make(__('Gender'), function () {
                if ($this->citizen->gender === 1) {
                    return __('Male');
                } else {
                    return __('Female');
                }
            })->onlyOnDetail(),

            Date::make(__('Birthday'), function () {
                if ($this->citizen->birth_date) {
                    return $this->citizen->birth_date->setTimezone(constants('ARM_TIMEZONE'))->format(constants('BIRTHDAY_DATE_FORMAT'));
                }
            }),

            Text::make(__('Phone Number'), function () {
                return $this->citizen->phone_number;
            })
            ->onlyOnDetail(),

            Text::make(__('Contract Number'), 'contract_number'),

            Number::make(__('Amount'), 'amount')->min(0)->displayUsing(function ($v) {
                return NumberHelper::numberToStringDram($v);
            }),

            Date::make(__('Sign Date'), function () {
                if ($this->sign_date) {
                    return $this->sign_date->setTimezone(constants('ARM_TIMEZONE'))->format(constants('SMS_DATE_FORMAT'));
                }
            }),

            Number::make(__('Monthly Payment'), 'monthly_payment')
                ->min(0)
                ->hideFromIndex()
                ->canSee(function ($request) {
                    return $request->user()->can('view-all-details');
                })
                ->displayUsing(function ($v) {
                    return NumberHelper::numberToStringDram($v);
                }),

            Number::make(__('Last Month Payment'), 'last_month_payment')
                ->min(0)
                ->hideFromIndex()
                ->canSee(function ($request) {
                    return $request->user()->can('view-all-details');
                })
                ->displayUsing(function ($v) {
                    return NumberHelper::numberToStringDram($v);
                }),

            Number::make(__('Total'), 'total')
                ->min(0)
                ->hideFromIndex()
                ->canSee(function ($request) {
                    return $request->user()->can('view-all-details');
                })
                ->displayUsing(function ($v) {
                    return NumberHelper::numberToStringDram($v);
                }),

            Number::make(__('Interest Rate'), function () {
                return $this->service_fee_rate.'%' ?? '—';
            })
                ->min(0)
                ->hideFromIndex()
                ->canSee(function ($request) {
                    return $request->user()->can('view-all-details');
                }),

            Number::make(__('Actual Percentage Rate'), function () {
                return $this->apr.'%' ?? '—';
            })
                ->min(0)
                ->hideFromIndex()
                ->canSee(function ($request) {
                    return $request->user()->can('view-all-details');
                }),

            Number::make(__('Months'), 'months')
                ->min(2)
                ->max(36)
                ->hideFromIndex()
                ->canSee(function ($request) {
                    return $request->user()->can('view-all-details');
                }),

            Date::make(__('Next Payment Date'), 'next_payment_date')
                ->hideFromIndex()
                ->canSee(function ($request) {
                    return $request->user()->can('view-all-details');
                }),

            Select::make(__('Notification Method'), function () {
                return constants('NOTIFICATION_METHODS')[$this->notification_method] ?? '-';
            })
                ->options([
                    'email' => 'Email',
                    'in_person' => 'In Person',
                    'post' => 'Post',
                ])
                ->hideFromIndex()
                ->canSee(function ($request) {
                    return $request->user()->can('view-all-details');
                }),

            HasMany::make(__('Loan Documents'), 'documents', 'App\Nova\COVID19\SupportLoanDocument'),
        ];
    }

    public static function label()
    {
        return __('Support Loan');
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(Request $request)
    {
        return array_merge(
            [
                (new \App\Nova\Filters\LoanSignDateFilter())->locale(env('NOVA_LOCALE')),
            ],
            parent::filters($request)
        );
    }

    public static function indexQuery(NovaRequest $request, $query)
    {
        $user = $request->user();

        if ($user->isAdmin()) {
            return $query;
        }

        return $query->where('status', \App\Models\Loan::CONFIRMED);
    }
}
