<?php

namespace App\Nova;

use App\Helpers\NumberHelper;
use App\Nova\Filters\LoanSignDateFilter;
use App\Nova\Filters\VehicleImportDateFilter;
use Carbon\Carbon;
use Exception;
use Globalcredit\ApproveVehicleLoan\ApproveVehicleLoan;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class VehicleImportLoan extends VehicleLoan
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static function label()
    {
        return __('Vehicle Import Loans');
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(Request $request)
    {
        $agent = $this->getLastAssignedAgent();

        return [
            ID::make()->sortable()->hideFromIndex(),

            Text::make(__('Contract Number'), 'contract_number')
                ->sortable()
                ->hideWhenUpdating(),

            Date::make(__('Sign Date'), 'sign_date')
                ->sortable()
                ->resolveUsing(function ($value) {
                    return $value->setTimezone(constants('ARM_TIMEZONE'))->format(constants('SMS_DATE_FORMAT')); // The sorting logic can be implemented here
                })
                ->hideWhenUpdating(),

            Text::make(__('Passport Number'), function () {
                return $this->citizen ? $this->citizen->getPrimaryDocument()['passport_number'] : null;
            })->onlyOnDetail(),

            Text::make(__('First Name'), function () {
                return $this->citizen->first_name ?? null;
            })->onlyOnDetail(),

            Text::make(__('Last Name'), function () {
                return $this->citizen->last_name ?? null;
            })->onlyOnDetail(),

            Text::make(__('Full Name'), function () {
                $first_name = $this->citizen->first_name ?? null;
                $last_name = $this->citizen->last_name ?? null;

                return "{$first_name} {$last_name}";
            })
                ->onlyOnIndex(),

            Text::make(__('Vehicle Name'), function () {
                $mark = $this->vehicle->vehicle_model->mark ?? '-';
                $model = $this->vehicle->vehicle_model->model ?? '-';

                return "{$mark} ($model)";
            }),

            Text::make('VIN', function () {
                return $this->vehicle->vin ?? null;
            }),

            Text::make(__('Released Year'), function () {
                return $this->vehicle->released ?? null;
            }),

            DateTime::make(__('Checkup Date'), 'mortgage.checkup_date')->fillUsing(function ($request, $model) {
                $mortgage_checkup_date = $request->mortgage_checkup_date;

                try {
                    if (empty(trim($mortgage_checkup_date))) {
                        $mortgage_checkup_date = null;
                    } else {
                        $mortgage_checkup_date = Carbon::parse($mortgage_checkup_date);
                    }
                } catch (Exception $e) {
                    $mortgage_checkup_date = null;
                }

                $model->mortgage->checkup_date = $mortgage_checkup_date;
                $model->mortgage->save();
            })
                ->hideFromIndex()
                ->format(constants('MORTGAGE_DATE_FORMAT_NOVA')),

            Number::make(__('Trade Amount'), 'mortgage.trade_amount')->fillUsing(function ($request, $model) {
                if (empty($request->mortgage_trade_amount)) {
                    $request->mortgage_trade_amount = null;
                }

                $model->mortgage->trade_amount = $request->mortgage_trade_amount;
                $model->mortgage->save();
            })
                ->displayUsing(function ($v) {
                    return NumberHelper::numberToStringDram($v);
                })
                ->min(0),

            Date::make(__('Import Date'), 'loan_security.loan_application_order.vehicle_order_detail.import_date')
                ->fillUsing(function ($request, $model) {
                    $import_date = $request->loan_security_loan_application_order_vehicle_order_detail_import_date;
                    if (empty(trim($import_date))) {
                        $import_date = null;
                    }

                    $model->loan_security->loan_application_order->vehicle_order_detail->import_date = $import_date;
                    $model->loan_security->loan_application_order->vehicle_order_detail->save();
                }),

            Text::make(__('Agent'), function () use ($agent) {
                return $agent ? "$agent->first_name $agent->last_name" : null;
            })
                ->onlyOnDetail(),

            Text::make(__('Notes'), function () use ($agent) {
                return $agent->pivot->notes ?? null;
            })
                ->onlyOnDetail(),

            HasMany::make(__('Loan Documents'), 'documents', 'App\Nova\LoanDocument'),

            ApproveVehicleLoan::make()
                ->withMeta([
                    'canVerify' => $request->user()->hasPermissionTo('verify-ovl-loan', config('nova.guard')),
                    'canConfirm' => $request->user()->hasPermissionTo('confirm-ovl-loan', config('nova.guard')),
                    'pledge_flow' => $this->loan_type ? $this->loan_type->loan_configs->where('key', 'pledge_flow')->first() : null,
                    'status' => $this->status,
                ])
                ->canSee(function ($request) {
                    return $request->user()->can('verify-ovl-loan') || $request->user()->can('confirm-ovl-loan');
                }),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(Request $request)
    {
        return [
            (new LoanSignDateFilter())->locale(env('NOVA_LOCALE')),
            (new VehicleImportDateFilter())->locale(env('NOVA_LOCALE')),
        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    public static function indexQuery(NovaRequest $request, $query)
    {
        $user = $request->user();

        $query
            ->where('loan_type_id', constants('LOAN_TYPES.OVL'))
            ->where('status', \App\Models\Loan::PARTIAL_PROCESSED);

        if ($user->isGCAgent()) {
            return $query
                ->join('loan_agent', 'loans.id', '=', 'loan_agent.loan_id')
                ->where('loan_agent.user_id', $user->id);
        }

        return $query;
    }
}
