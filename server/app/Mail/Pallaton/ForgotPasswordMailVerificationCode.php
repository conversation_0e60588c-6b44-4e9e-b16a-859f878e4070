<?php

namespace App\Mail\Pallaton;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ForgotPasswordMailVerificationCode extends Mailable
{
    use Queueable;
    use SerializesModels;

    protected $code = null;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($code)
    {
        $this->code = $code;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this
            ->view('forgotPasswordVerificationCode')
            ->with(
            [
                'code' => $this->code,
            ])
            ->subject(__('mail.pallaton_subject_verification_mail'));
    }
}
