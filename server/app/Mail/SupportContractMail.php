<?php

namespace App\Mail;

use App\Models\LoanDocument;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SupportContractMail extends Mailable
{
    use Queueable;
    use SerializesModels;

    private $loan = null;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($loan)
    {
        $this->loan = $loan;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $aws_service = resolve('App\Interfaces\IAWSService');

        $contract_number = $this->loan->contract_number;
        $document = $this->loan->documents()
            ->where('document_type', LoanDocument::CONTRACT_SUPPORT['name'])->first();

        $msg = $this->view('supportContractMail')
            ->with(
            [
                'contract_number' => $contract_number,
                'sign_date' => $this->loan->sign_date->format(constants('STANDARD_DASHED_DATE_FORMAT')),
            ])
            ->subject(__('mail.subject_loan_docs').$contract_number);

        $msg->attachFromStorageDisk(
            $aws_service->getPdfStorageName(),
            $document->path,
            __('mail.subject_loan_docs').'.pdf',
            [
                'mime' => 'application/pdf',
            ]
        );

        return $msg;
    }
}
