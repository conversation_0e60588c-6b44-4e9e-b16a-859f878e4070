<?php

namespace App\Abstracts;

use App\Exceptions\BlacklistedException;
use App\Exceptions\InvalidPhoneNumberException;
use App\Exceptions\OWLInvalidAmountException;
use App\Factory\CitizenServiceFactory;
use App\Helpers\DateHelper;
use App\Helpers\NumberHelper;
use App\Helpers\PassportHelper;
use App\Interfaces\IOWLService;
use App\Models\LoanSecurity;
use App\Models\Pallaton\Profile;
use App\Services\SecurityUtilityService;
use App\Validators\Rules\PhoneNumber;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Log;

abstract class AbstractOWLService implements IOWLService
{
    protected $client;
    protected $redis_service;

    public function __construct()
    {
        $this->redis_service = resolve('App\Interfaces\IRedisService');
        $this->client = new Client();
    }

    abstract protected function requestWalletInfo($token, $suuid);

    abstract public function transfer($token, $amount, $agreement_id, $suuid);

    abstract protected function getLoanConfigs($loan);

    abstract protected function getWalletKey();

    abstract public function getWalletDuration();

    public function getWalletinfo($token, $suuid = null)
    {
        $wallet_info = $this->getWalletInfoFromCacheOrWallet($token, $suuid);

        $this->validatePhoneNumber($wallet_info['Phone']);

        //TODO, this solution is temporary
        $ls = LoanSecurity::where('suuid', $suuid)->first();
        if ($ls->loan_type_id == constants('LOAN_TYPES.OIDL') && $wallet_info['Amount'] == 10000) {
            throw new OWLInvalidAmountException();
        }

        if ($wallet_info['Amount'] == 10000) {
            $wallet_info['Amount'] = 20000;
        }

        /*
         * Wallet api has a bug and in some rare cases it might return wrong amount,
         * we make this check to guard against that issue.
         */
        if (!in_array($wallet_info['Amount'], constants('WALLET_AVAILABLE_AMOUNTS'))) {
            Log::error(
                'Requesting user data from Wallet, OWLInvalidAmountException',
                [
                    'err' => 'Invalid amount in response - '.$wallet_info['Amount'],
                ]
            );
            throw new OWLInvalidAmountException();
        }

        return $this->composeWalletInfo($wallet_info);
    }

    public function validatePhoneNumber($phone_number)
    {
        $phone_validator = new PhoneNumber();
        if (!$phone_validator->passes('phone_number', $phone_number)) {
            Log::warning('Non-Armenian phone number detected', ['phone_number' => $phone_number]);

            throw new InvalidPhoneNumberException();
        }
    }

    public function composeWalletInfo($wallet_info)
    {
        return array_merge($wallet_info, [
            'phone_number' => $wallet_info['Phone'],
            'document_number' => trim($wallet_info['PassportNum']),
            'email' => $wallet_info['Email'],
        ]);
    }

    public function composeCitizenInfo(): array
    {
        $security_service = resolve('App\Interfaces\ISecurityService');

        $loan_security = $security_service->resolveLoanSecurity();

        $owl_token = $loan_security->owl_token;
        $suuid = $loan_security->suuid;
        [
            'phone_number' => $phone_number,
            'email' => $wallet_email,
            'document_number' => $document_number,
            'Amount' => $requested_amount, // Submitted amount from OWL APP
        ] = $this->getWalletinfo($owl_token, $suuid);

        $aggregator_service = resolve('App\Interfaces\IAggregatorService');
        $ekeng_data = $aggregator_service->getEkengData($document_number);
        $ssn = $ekeng_data['SSN'] ?? null;
        // Profile email has higher priority
        $profile = Profile::getProfileBySsn($ssn);
        $email = $profile->email ?? $wallet_email;

        $loan_security->update([
            'document_number' => $document_number,
            'phone_number' => $phone_number,
            'email' => $email,
            'ssn' => $ssn,
            'requested_loan_amount' => $requested_amount,
        ]);
        $loan_type_id = $loan_security->loan_type_id;

        $citizen_service = CitizenServiceFactory::build($loan_type_id);
        $passport_numbers_as_array = PassportHelper::getEkengPassportNumbersAsArray($ekeng_data['passport_data'] ?? []);
        $citizen_service->checkInternalCredits($ssn, $loan_type_id, $passport_numbers_as_array);

        $this->isCitizenBlocked($ssn, $phone_number, $loan_type_id);

        return [
            'document_number' => $document_number,
            'token' => $owl_token,
        ];
    }

    public function getLoan($payload, $loan_type_id)
    {
        $citizen_service = CitizenServiceFactory::build($loan_type_id);

        return $citizen_service->fetchCitizen($payload, true);
    }

    public function composeContractData($loan_security)
    {
        $loan = $loan_security->loan;
        $citizen = $loan->citizen;

        $now = Carbon::now();

        $loan_last_payment = $loan->loan_schedule->sortByDesc('date')->first();

        $loan_schedule = $loan->loan_schedule;

        $service_fee_sum = $loan->loan_schedule->sum(function ($item) {
            return $item->service_fee;
        });

        $contract_data = array_merge(
            $loan->toArray(),
            $citizen->toArray(),
            $this->getLoanConfigs($loan),
            [
                'passport_number' => $loan_security->document_number,
                'address' => PassportHelper::composeAddress($citizen),
                'date_now' => DateHelper::parseDate($now),
                'amount_in_words' => NumberHelper::floatToText($loan->amount),
                'loan_exp_date' => DateHelper::parseDate($loan_last_payment->date),
                'rate_in_words' => NumberHelper::floatToText($loan->interest_rate),
                'service_fee_rate' => $loan->service_fee_rate,
                'verification_code' => $loan_security->verification_code,
                'loan_schedule' => $loan_schedule,
                'service_fee_sum' => NumberHelper::numberToDram($service_fee_sum),
                'apr' => $loan->apr,
                'payment_type' => lang('contract.non_cash_loan'),
            ]
        );

        return $contract_data;
    }

    public function checkTokenAvailability($token)
    {
    }

    public function getWalletInfoFromCacheOrWallet($token, $suuid)
    {
        Log::debug('Getting Wallet data from cache', ['token' => $token]);

        $loan_key = $this->getWalletKey();
        $cached = $this->redis_service->get("$token:$loan_key:$suuid");

        if ($cached) {
            Log::debug('Return cached WALLET data');

            return $cached;
        }
        Log::debug('There are no WALLET data in cache');

        $wallet_info = $this->requestWalletInfo($token, $suuid);

        $this->redis_service->set("$token:$loan_key:$suuid", $wallet_info, constants('WALLET_CACHE_EXPIRATION'));

        return $wallet_info;
    }

    protected function isCitizenBlocked($ssn, $phone_number, $loan_type_id)
    {
        $is_blocked = SecurityUtilityService::isCitizenBlocked($ssn, $phone_number, null, null, $loan_type_id);
        if ($is_blocked) {
            Log::info('AbstractOWLService, Citizen Blocked',
                ['ssn' => $ssn, 'phone_number' => $phone_number]
            );

            throw new BlacklistedException();
        }
    }
}
