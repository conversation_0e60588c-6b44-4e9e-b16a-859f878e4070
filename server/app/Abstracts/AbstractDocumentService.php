<?php

namespace App\Abstracts;

use App\Helpers\ArrayHelper;
use App\Helpers\NumberHelper;
use App\Interfaces\IDocumentService;
use App\Models\CardToCardPayment;
use App\Models\CashPayment;
use App\Models\EasypayWalletPayment;
use App\Models\IdramWalletPayment;
use App\Models\LoanDocument;
use App\Models\ManualTransfer;
use App\Models\ProductProvision;
use App\Models\WalletLoanPayment;
use App\Models\WirePayment;
use Carbon\Carbon;

abstract class AbstractDocumentService extends BaseDocumentService implements IDocumentService
{
    abstract public function getLoanDocumentsJob($loan);

    public function getDocumentTypes($obfuscated = true, $loan = null)
    {
        if ($obfuscated) {
            return array_merge([
                LoanDocument::WHAT_TO_DO,
                LoanDocument::ARBITRATION_PRIVATE,
            ],
                $this->getPrivateDisputeSolution($loan->dispute_solution_method ?? null)
            );
        }

        return array_merge(
            [
                LoanDocument::ARBITRATION,
            ],
            $this->getDisputeSolution($loan->dispute_solution_method ?? null)
        );
    }

    public function storeExistingDirectoryDocument($loan, $type, $file)
    {
        $directory = $this->extractDirectoryPath($loan);
        $file_name = $this->composeFileName(
            $loan->citizen->first_name,
            $loan->citizen->last_name,
            $type['name'],
            $file->extension()
        );
        $path = "$directory/$file_name";

        $this->persistDocument($loan, $path, $type['name'], file_get_contents($file), $type['public']);
    }

    public function composePdfData($loan)
    {
        $citizen_data = $this->getCitizenData($loan);
        $citizen_private = $this->makePrivate($citizen_data);
        $loan_private = $this->makePrivate(ArrayHelper::pick($loan, ['contract_number', 'credit_code']));

        $configs = $this->getLoanConfigs($loan);

        return array_merge(
            parent::composePdfData($loan),
            $citizen_private,
            $loan_private,
            [
                'annual_rate_in_words' => NumberHelper::numberInWords($configs['annual_rate']),
                'withdrawal_fee_rate' => $configs['withdrawal_fee_rate'] * 100,
            ],
            $this->getPayment($loan)
        );
    }

    protected function contractDates($loan)
    {
        $loan_last_payment = $loan->loan_schedule->sortByDesc('date')->first();

        $loan_exp_date = Carbon::createFromFormat(constants('SMS_DATE_FORMAT'), $loan_last_payment->date, constants('ARM_TIMEZONE'));
        $loan_exp_date_in_words = $this->composeLocalizedDate($loan_exp_date);

        $loan_next_payment_date = $loan->next_payment_date->format(constants('EKENG_DATE_FORMAT'));
        $loan_exp_date_format = $loan_exp_date->format(constants('EKENG_DATE_FORMAT'));

        return parent::contractDates($loan) +
            [
                'loan_exp_date' => $loan_exp_date_in_words,
                'loan_exp_date_format' => $loan_exp_date_format,
                'loan_next_payment_date' => $loan_next_payment_date,
            ];
    }

    protected function getPayment($loan)
    {
        if ($loan->payment instanceof IdramWalletPayment) {
            return [
                'payment_type' => lang('contract.non_cash_loan'),
                'payment_target' => $loan->payment->wallet_id,
                'payment_transfer' => lang('contract.payment_transfer'),
                'payment_to' => lang('contract.idram_wallet'),
            ];
        } elseif ($loan->payment instanceof WalletLoanPayment) {
            return [
                'payment_type' => lang('contract.non_cash_loan'),
                'payment_target' => '',
                'payment_transfer' => lang('contract.payment_transfer'),
                'payment_to' => '',
            ];
        } elseif ($loan->payment instanceof CashPayment) {
            return [
                'payment_type' => lang('contract.cash_loan'),
                'payment_target' => '',
                'payment_transfer' => '',
                'payment_to' => '',
            ];
        } elseif ($loan->payment instanceof CardToCardPayment) {
            return [
                'payment_type' => lang('contract.non_cash_loan'),
                'payment_target' => NumberHelper::cardMask($loan->payment->card_number),
                'payment_transfer' => lang('contract.payment_transfer'),
                'payment_to' => lang('contract.bank_card'),
            ];
        } elseif ($loan->payment instanceof WirePayment) {
            return [
                'payment_type' => lang('contract.non_cash_loan'),
                'payment_target' => $loan->payment->bank_account,
                'payment_transfer' => lang('contract.payment_transfer'),
                'payment_to' => lang('contract.bank_account'),
            ];
        } elseif ($loan->payment instanceof EasypayWalletPayment) {
            return [
                'payment_type' => lang('contract.non_cash_loan'),
                'payment_target' => $loan->payment->wallet_id,
                'payment_transfer' => lang('contract.payment_transfer'),
                'payment_to' => lang('contract.easypay_wallet'),
            ];
        } elseif ($loan->payment instanceof ManualTransfer) {
            return [
                'payment_type' => lang('contract.manual_transfer'),
                'payment_target' => '',
                'payment_transfer' => '',
                'payment_to' => '',
            ];
        } elseif ($loan->payment instanceof ProductProvision) {
            return [];
        } else {
            //TODO: we have not enough info for this. Waiting for it
            return [
                'payment_type' => '',
                'payment_target' => '',
                'payment_transfer' => '',
                'payment_to' => '',
            ];
        }
    }
}
