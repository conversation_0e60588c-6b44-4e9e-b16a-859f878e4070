<?php

namespace App\Abstracts;

use App\Exceptions\LoanNotFoundException;
use App\Factory\HcServiceFactory;
use App\Helpers\ArrayHelper;
use App\Interfaces\ICitizenService;
use App\Models\Loan;
use App\Models\QrCode;
use Carbon\Carbon;
use function Functional\filter;
use function Functional\some;
use Illuminate\Support\Facades\Log;

abstract class AbstractCitizenService extends BaseCitizenService implements ICitizenService
{
    protected $credit_offer_service;
    protected $hc_service;
    protected $type;

    public function __construct($type)
    {
        parent::__construct();

        $this->hc_service = HcServiceFactory::build($type);
        $this->type = $type;
        $this->credit_offer_service = resolve('App\Interfaces\ICreditOfferService');
    }

    public function hasCredit($soc_card)
    {
        return $this->hc_service->hasCredit($soc_card);
    }

    public function hasNonSyncedCredits($ssn): bool
    {
        return $this->hc_service->hasNonSyncedCredits($ssn);
    }

    public function hasActiveOclOrWallet($ssn)
    {
        return $this->hc_service->hasActiveOclOrWallet($ssn);
    }

    // Override if necessary
    public function afterFetch($citizen)
    {
    }

    public function fetchCitizen($payload, $calculate_allowance)
    {
        $citizen = $this->getCitizen($payload, $calculate_allowance);

        $this->afterFetch($citizen);

        return $citizen;
    }

    protected function aggregateCitizenData($document_number)
    {
        $citizen = parent::aggregateCitizenData($document_number);

        $ssn = $citizen['ekeng']['SSN'];
        $citizen['hc'] = [
            'hasCredit' => $this->hasCredit($ssn),
            'hasNonSyncedCredits' => $this->hasNonSyncedCredits($ssn),
        ];

        $citizen['loanTotal'] = $this->getLoanTotal($citizen['ekeng']['SSN']);

        $security_service = resolve('App\Interfaces\ISecurityService');
        $loan_security = $security_service->resolveLoanSecurity();

        $citizen['referral_code'] = $loan_security->referral_code;
        $citizen['selected_loan_type_id'] = $loan_security->selected_loan_type_id;
        $citizen['requested_loan_amount'] = $loan_security->requested_loan_amount;
        $citizen['loan_type_id'] = $loan_security->loan_type_id;
        $citizen['loan_subtype_id'] = $loan_security->loan_subtype_id;

        $warehouse_service = resolve('App\Interfaces\IWarehouseService');
        $citizen['internal_active_loans'] = $warehouse_service->getLoansBySsn($ssn, true);

        return $citizen;
    }

    protected function getLoanTotal($ssn)
    {
        $loan_configs = resolve('App\Services\LoanConfigService');

        return array_merge(
            $this->getQrDiscount(),
            parent::getLoanTotal($ssn),
            $loan_configs->getLoanSummary($this->type)
        );
    }

    protected function getQrDiscount()
    {
        $security_service = resolve('App\Interfaces\ISecurityService');
        $loan_security = $security_service->resolveLoanSecurity();

        $discount = QrCode::getDiscount($loan_security->qr_token, $loan_security->loan_type_id);

        return ['qr_discount' => $discount];
    }

    protected function calculateCredit($citizen, $calculate_allowance = null)
    {
        $credit = [];
        if ($calculate_allowance) {
            $calculated_credit = $this->credit_offer_service->getCreditOffer($citizen, $this->type)['result']['credit'];

            if ($this->isCreditAvailable($calculated_credit)) {
                $credit = ArrayHelper::pick($calculated_credit, ['amount', 'duration', 'service_fee_rate', 'score']);
                $credit['interest_rate'] = $calculated_credit['rate'];
            }
        }

        return $credit;
    }

    public function isNewCustomer($hc_loans, $new_customer_transmission)
    {
        if (empty($hc_loans)) {
            return true;
        }

        return !some($hc_loans, function ($loan) use ($new_customer_transmission) {
            $sign_date = Carbon::parse($loan->fDGDATEGIVE);
            $close_date = Carbon::parse($loan->fDGDATECLOSE);

            return $sign_date->diffInDays($close_date) > $new_customer_transmission;
        });
    }

    public function storeRequestedLoanDetails($amount, $requested_type)
    {
        $security_service = resolve('App\Interfaces\ISecurityService');
        $loan_security = $security_service->resolveLoanSecurity();

        $loan_security->update([
            'requested_loan_amount' => $amount,
            'requested_type' => $requested_type,
        ]);
    }

    /**
     * Get active credit balances for a given SSN and loan type from DWH.
     *
     * @param string $ssn the Social Security Number (SSN) of the user
     *
     * @throws LoanNotFoundException when active credit details are not found from DWH
     */
    public function getActiveCreditsBalance(string $ssn)
    {
        $warehouse_service = resolve('App\Interfaces\IWarehouseService');

        $credits = (array) $warehouse_service->getLoansByType($ssn, $this->type);

        Log::info('Get active credits balances for specified loan type from DWH', ['loan_type_id' => $this->type, 'ssn' => $ssn, 'credits' => $credits]);

        if (empty($credits)) {
            Log::warning('Active credits not found for specified loan type from DWH', ['loan_type_id' => $this->type, 'ssn' => $ssn]);

            throw new LoanNotFoundException();
        }
        // OCL and Velox have same NOTES in HC,
        //that's why we need to additional filtering based on internal DB
        $credits = filter($credits, function ($credit) {
            return Loan::whereContractNumber(trim($credit['fDGCODE']))->where('loan_type_id', $this->type)->exists();
        });

        $balances = array_column($credits, 'outs');

        return array_sum($balances);
    }

    public function getCitizenBasedOnDB($loan_security): array
    {
        // Retrieve the latest 'CITIZEN_INFO' metadata
        $meta = $loan_security->security_metas()
            ->where('step', 'CITIZEN_INFO')
            ->latest()
            ->first();

        if (!$meta) {
            Log::warning('Citizen metadata not found for loan security', ['loan_security_id' => $loan_security->id]);

            return [];
        }

        $citizen_data = json_decode($meta->content, true);
        // The Whole system is waiting passport_data key as an object,
        //that's why we need to convert it to avoid unexpected changes
        $citizen_data['ekeng']['passport_data'] = json_decode(json_encode($citizen_data['ekeng']['passport_data']));
        $citizen_data['internal_active_loans'] = json_decode(json_encode($citizen_data['internal_active_loans']));

        // Retrieve the latest DR prediction for the loan type
        $dr_prediction = $loan_security
            ->dr_predicitions()
            ->where('loan_type_id', $this->type)
            ->latest()
            ->first();
        if ($dr_prediction) {
            $decoded_prediction = json_decode($dr_prediction->result, true);
            $prediction_values = $decoded_prediction['data'][0]['predictionValues'] ?? [];

            $citizen_data['dr']['dr_score'] = $this->getCitizenDrScore($prediction_values);
        } else {
            $citizen_data['dr']['dr_score'] = [];
        }

        Log::debug('Got Citizen based on DB', [
            'meta_step_id' => $meta->id,
            'dr_prediction_id' => optional($dr_prediction)->id,
        ]);

        return $citizen_data;
    }
}
