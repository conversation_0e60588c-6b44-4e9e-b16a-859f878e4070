<?php

namespace App\Abstracts;

use App\Calculator\CalculatorFactory;
use App\Exceptions\CheckLoanAvailableAmountException;
use App\Exceptions\DoubleLoanException;
use App\Exceptions\DoublePaymentException;
use App\Exceptions\DuplicatePhoneNumberException;
use App\Exceptions\HcException;
use App\Exceptions\InvalidLoanException;
use App\Exceptions\LoanNotFoundException;
use App\Exceptions\OWLInsufficientBalanceException;
use App\Exceptions\PaymentFailureException;
use App\Exceptions\PaymentLockedException;
use App\Exceptions\ResourceLockedException;
use App\Exceptions\TransferTypeNotFoundException;
use App\Factory\DocumentServiceFactory;
use App\Factory\HcServiceFactory;
use App\Helpers\NumberHelper;
use App\Interfaces\ILoanService;
use App\Jobs\GenerateLoanDocuments;
use App\Models\Loan;
use App\Models\LoanDocument;
use App\Models\LoanType;
use App\Models\Paymentable;
use App\Models\PreapprovedCreditOffer;
use App\Models\TransferType;
use App\Traits\Transaction;
use Auth;
use Carbon\Carbon;
use Exception;
use Log;

abstract class AbstractLoanService implements ILoanService
{
    use Transaction;

    protected $type;

    public function __construct($type)
    {
        $this->type = $type;
    }

    public function create($details)
    {
        Log::info('Creating loan', ['details' => $details]);

        $citizen = $this->getLoanCitizen($details);

        $citizen_credit = $this->extractCredit($citizen, $details);
        ['interest_rate' => $interest_rate, 'duration' => $duration] = $citizen_credit;

        Log::info('Checking loan validity', ['citizen_context' => $citizen]);

        $configs = $this->getLoanConfigs();

        $service_fee_rate = $this->getServiceFeeRate($configs, $citizen_credit, $citizen['qr_discount']);

        Log::info('Generating schedule', [
            'amount' => $details['amount'],
            'monthly' => $details['monthly_payment'],
            'rate' => $interest_rate,
            'service_fee_rate' => $service_fee_rate,
            'duration' => $duration,
        ]);
        $calculator = $this->initCalculator(
            array_merge(
                [
                    'loan_type' => $this->type,
                    'interest_rate' => $interest_rate,
                    'service_fee_rate' => $service_fee_rate,
                    'duration' => $duration,
                ],
                $details
            )
        );

        [
            'schedule' => $schedule,
            'schedule_summary' => $schedule_summary,
        ] = $this->getScheduleAndSummary(
            $calculator, $details['monthly_payment']
        );

        $is_valid = $this->isValidLoan($schedule_summary, $citizen_credit, $details, $citizen, $schedule);

        $security_service = resolve('App\Interfaces\ISecurityService');

        $withdrawal_fee = $this->getWithdrawalFee($details['amount'], $configs['withdrawal_fee_rate']);
        $details = array_merge($details, $withdrawal_fee);

        // Record schedule summary in security meta
        $security_service->recordMeta($schedule_summary, constants('META_STEPS.LOAN_CALC'));

        if ($is_valid) {
            Log::info('Loan validated');

            $loan_security = $security_service->resolveLoanSecurity();

            return $this->storeLoan($loan_security, $citizen, $configs, $details, $schedule_summary, $schedule, $citizen_credit);
        }

        Log::critical('Create loan, InvalidLoanException');
        throw new InvalidLoanException();
    }

    protected function extractCredit($citizen, $details = null)
    {
        return $citizen['credit'];
    }

    /**
     * Get loan citizen data.
     *
     * This optimized implementation gets citizen data from the database instead of collecting it from scratch.
     * It uses the existing credit offer to prepare credit data.
     *
     * @param array $details Loan details
     *
     * @return array Citizen data with credit information
     */
    protected function getLoanCitizen(array $details)
    {
        $citizen_service = resolve('App\Interfaces\ICitizenService');
        $security_service = resolve('App\Interfaces\ISecurityService');

        $loan_security = $security_service->resolveLoanSecurity();
        // We do not need to recollect citizen during loan approving;
        // We already have necessary info in DB to compose it
        $citizen = $citizen_service->getCitizenBasedOnDB($loan_security);

        // We do not need to affect a rule result during loan approving;
        // that's why we need to get existing offer
        $credit_offer = $this->resolveCreditOffer($loan_security);

        $citizen_credit = $this->prepareCreditData($credit_offer);

        // Special handling for top-up offers
        if ($this->isTopUpOfferSubmission()) {
            $top_up_service_ocl = resolve('App\Services\TopUpServiceOCL');
            $top_up_service_ocl->adjustCreditBasedTopUpOffer($citizen, $citizen_credit);
        }

        return $citizen_service->preparePersonalInfo($citizen, $citizen_credit, $loan_security->document_number);
    }

    protected function resolveCreditOffer($loan_security)
    {
        // Fallback to the Latest Standard Offer by Loan Type
        return $loan_security->credit_offers()
            ->where('loan_type_id', $this->type)
            ->latest()
            ->first();
    }

    protected function prepareCreditData($credit_offer): array
    {
        return [
            'credit' => [
                'amount' => $credit_offer->amount,
                'duration' => $credit_offer->duration,
                'service_fee_rate' => $credit_offer->service_fee_rate,
                'score' => $credit_offer->score,
                'interest_rate' => $credit_offer->rate,
            ],
        ];
    }

    protected function getLoanConfigs()
    {
        $loanConfigService = resolve('App\Services\LoanConfigService');

        return $loanConfigService->getConfigs($this->type);
    }

    public function updatePersonalInfo($loan, $details)
    {
        $citizen = $loan->citizen;

        $phone_number = $citizen->phone_number;
        $additional_phone_number = NumberHelper::phoneMask($details['additional_phone_number']);

        if ($phone_number === $additional_phone_number) {
            throw new DuplicatePhoneNumberException();
        }

        $loan->update($details);

        return $citizen->update($details);
    }

    public function storeAdditionalPhoneNumber($additional_phone_number)
    {
        $user = Auth::user();

        $user->profile()->update([
            'additional_phone_number' => $additional_phone_number,
        ]);
    }

    public function getLoanByPublicId($public_id)
    {
        $loan = Loan::getByPublicId($public_id);

        if (!$loan) {
            throw new LoanNotFoundException();
        }

        $payment_service = resolve('App\Interfaces\IPaymentService');
        $loan['loan_expiration_days'] = $payment_service->getPaymentExpirationDays($loan);

        $loan_security = $loan->loan_security;

        $merchant = $loan_security->loan_application_order->merchant ?? null;
        if (isset($merchant)) {
            $loan['merchant_details'] = $merchant->toArray();
        }

        return $loan;
    }

    public function verifyLoan($loan)
    {
        return $loan->update([
            'status' => Loan::VERIFIED,
            'verified_at' => now(),
            'public_id' => generate_uuid(), // We will use public_id to get loan
        ]);
    }

    public function setLoanState($loan, $status, $failed_payment, $confirmed_at)
    {
        $loan->update([
            'status' => $status,
            'failed_payment' => $failed_payment,
            'confirmed_at' => $confirmed_at,
        ]);
    }

    public function rejectLoan($loan, $status, $rejected_at)
    {
        $loan->update([
            'status' => $status,
            'confirmed_at' => $rejected_at,
        ]);
    }

    public function updateLoanStatus($loan, $status)
    {
        $loan->update([
            'status' => $status,
        ]);
    }

    public function expireLoan($loan)
    {
        $now = Carbon::now();

        // Make suuid expired to restrict access to loan after confirmation
        $loan->loan_security->update(['suuid_exp' => $now]);

        return $now;
    }

    public function expireTopUpOffers($loan)
    {
        if ($loan->isTopUp()) {
            PreapprovedCreditOffer::expireExistingTopUpOfferByLoanType($this->type);
        }
    }

    public function updateLoanContractCredentials($loan, $details)
    {
        $hc_service = HcServiceFactory::build($loan->loan_type_id);

        $ssn = $loan->citizen->getSocCard()->passport_number;

        $credit_codes = $hc_service->getCreditCodeAndOuterId($ssn, $loan->loan_type_id);

        if (!$loan->credit_code) {
            $loan->update([
                'credit_code' => $credit_codes['credit_code'],
            ]);
        }

        if ($this->shouldUpdateContractNumber($loan)) {
            $this->updateContractNumber($loan);
        }

        return $loan;
    }

    protected function updateContractNumber($loan)
    {
        $hc_service = HcServiceFactory::build($loan->loan_type_id);

        $loan->update([
            'contract_number' => $hc_service->generateContractNumber($loan->loan_type_id, $loan->created_at),
        ]);
    }

    public function isValidLoan($schedule_summary, $citizen_credit, $data, $citizen = null, $schedule = null)
    {
        return $schedule_summary['months'] == $data['months'] &&
            $schedule_summary['last_month_payment'] == $data['last_month_payment'] &&
            $schedule_summary['total'] == $data['total'] &&
            $data['amount'] <= $citizen_credit['amount'] &&
            $data['months'] <= $citizen_credit['duration'];
    }

    public function getScheduleAndSummary($calculator, $monthly = null, $start_date = null)
    {
        $schedule = $calculator->generateSchedule($monthly, $start_date);

        Log::debug('Generate schedule', ['schedule' => $schedule]);

        $schedule_summary = $calculator->scheduleSummary($monthly, $start_date);

        Log::info('Schedule summary', ['schedule' => $schedule_summary]);

        return ['schedule' => $schedule, 'schedule_summary' => $schedule_summary];
    }

    /**
     * @property array $params {
     *
     * @var string $loan_type - Loan type
     * @var string $amount - Loan amount
     * @var string $interest_rate - Annual rate
     * @var string $service_fee_rate - Nominal fee
     * @var string $duration - Maxduration
     * @var string $solar_panel_type_id - Only in OASL
     * @var string $term_id - Only in OASL
     *             }
     */
    protected function initCalculator($params)
    {
        $loan_type = $params['loan_type'];
        $amount = $params['amount'];
        $interest_rate = $params['interest_rate'];
        $service_fee_rate = $params['service_fee_rate'];
        $duration = $params['duration'];

        return CalculatorFactory::build($loan_type, $amount, $interest_rate, $service_fee_rate, [
            'max_duration' => $duration,
        ]);
    }

    /**
     * @param Loan  $loan      - Existing loan instance
     * @param array $overrides - Any overrides to default loan parameters
     */
    protected function initCalculatorByLoan($loan, $overrides = [])
    {
        $params = [
            'loan_type' => $loan->loan_type->id,
            'amount' => $loan->amount,
            'interest_rate' => $loan->interest_rate,
            'service_fee_rate' => $loan->service_fee_rate,
            'duration' => $loan->months,
        ];

        return $this->initCalculator(array_merge($params, $overrides));
    }

    protected function getWithdrawalFee($amount, $withdrawal_fee_rate)
    {
        return ['withdrawal_fee' => $amount * $withdrawal_fee_rate];
    }

    // Extract service fee rate for particular credit and
    // fallback to global config if not available
    protected function getServiceFeeRate($configs, $credit, $discount = 0)
    {
        $rate = $credit['service_fee_rate'] ?? $configs['service_fee_rate'];

        return $rate - $discount;
    }

    public function isExpiredLoan($loan)
    {
        return $loan->payment->withdrawn_expiration_date < now()
            || $loan->payment_status === Paymentable::PAYMENT_EXPIRED;
    }

    public function getSignDate($loan)
    {
        return Carbon::now();
    }

    public function setSignDate($loan)
    {
        $loan->update([
            'sign_date' => $this->getSignDate($loan),
        ]);

        return $loan;
    }

    public function setSignDateAndApr($loan)
    {
        $loan = $this->setSignDate($loan);

        return $this->setApr($loan);
    }

    /**
     * @param Loan $loan
     *
     * @return Loan
     */
    public function setApr($loan)
    {
        $loan_config_service = resolve('App\Services\LoanConfigService');

        $configs = $loan_config_service->getConfigsByLoan($loan);

        $calculator = $this->initCalculatorByLoan($loan, ['duration' => $configs['max_months'], 'months' => $loan->months]);

        $loan->update([
            'apr' => $calculator->calculateAPR($loan->monthly_payment, $loan->sign_date, $loan->withdrawal_fee, $loan->months),
        ]);

        return $loan;
    }

    protected function storeLoan($loan_security, $citizen, $configs, $details, $schedule_summary, $schedule, $citizen_credit)
    {
        // We need to store incoming loan credentials but replace interest rate by citizen
        $payload = array_merge($configs, $details, $schedule_summary,
            [
                'interest_rate' => $citizen_credit['interest_rate'],
                'service_fee_rate' => $this->getServiceFeeRate($configs, $citizen_credit, $citizen['qr_discount']),
                'nominal_rate' => $citizen_credit['service_fee_rate'],
            ]
        );

        if ($this->isLoanExists($loan_security)) {
            $this->cleanupBeforeUpdate($loan_security->loan);
            $loan = $this->updateLoan($loan_security->loan, array_merge($citizen, $payload), $schedule);
        } else {
            $loan = $this->insertLoan($loan_security, array_merge($citizen, $payload), $schedule);
        }

        return $loan;
    }

    protected function insertLoan($loan_security, $details, $schedule)
    {
        // Create new loan
        $loan = new Loan();

        $details = array_merge($details, [
            'is_offline' => $loan_security->is_offline,
        ]);

        $loan->fill($details);

        $loan_type = LoanType::where('id', $this->type)->first();
        $loan->loan_type()->associate($loan_type);

        $loan->save();

        // Create new citizen
        $citizen = $loan->citizen()->create($details);
        $citizen->passports()->createMany($details['passports']);

        // Associate security information to a loan
        $loan_security->loan()->associate($loan);
        $loan_security->update();

        // Create loan schedule
        $loan->storeLoanSchedule($schedule);

        $this->updateLoanContractCredentials($loan, $details);

        return $loan;
    }

    private function getCredit($details)
    {
        return $details['credit'][0] ?? $details['credit'];
    }

    protected function updateLoan($loan, $details, $schedule)
    {
        return $this->startTransaction(function () use ($loan, $details, $schedule) {
            // Update loan
            $loan->update($details);

            $loan_type = LoanType::where('id', $this->type)->first();
            $loan->loan_type()->associate($loan_type);
            $loan->save();

            // Update loan schedule
            $loan->deleteLoanSchedule();
            $loan->storeLoanSchedule($schedule);

            $this->updateLoanContractCredentials($loan, $details);

            return $loan;
        });
    }

    public function isLoanExists($loan_security): bool
    {
        return (bool) $loan_security->loan;
    }

    protected function cleanupBeforeUpdate($loan)
    {
        $loan->documents()->delete();
        $loan->vehicle()->delete();
        $loan->mortgage()->delete();
        $loan->citizen->spouse()->delete();
    }

    public function validateConfirmation($loan)
    {
        // Let us resolve HcService after loan verifying
        $hc_service = HcServiceFactory::build($loan->loan_type_id);

        if ($hc_service->hasCreditByLoan($loan)) {
            throw new DoubleLoanException();
        }

        if ($loan->payment->paid) {
            throw new DoublePaymentException();
        }
    }

    public function processLoanPayment($loan, $provided_status = null)
    {
        $payment_service = resolve('App\Interfaces\IPaymentService');

        $status = $provided_status ?? Loan::CONFIRMED;
        $failed_payment = null;

        try {
            Log::info('Making transfer', ['loan' => $loan]);
            $payment_service->makeTransfer($loan);
            Log::info('Transfer successfully completed');
        } catch (TransferTypeNotFoundException $e) {
            Log::error('Making transfer, TransferTypeNotFoundException', ['error' => $e->getTraceAsString()]);
            $status = Loan::CONFIRMED_NO_PAY;
        } catch (PaymentFailureException $e) {
            Log::critical('Validate code, PaymentFailureException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            if ($this->isFallbackAllowed($loan->loan_type_id)) {
                $failed_payment = $loan->payment_type;

                Log::info('Fallback Payment');
                $payment_service->fallbackOnHandPayment($loan);
                Log::info('Transfer type changed to Fallback Payment type');

                GenerateLoanDocuments::dispatchNow($loan, false);
            } else {
                $status = Loan::FAILED;
            }
        } catch (ResourceLockedException $e) {
            Log::critical('Validate code, ResourceLockedException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        } catch (PaymentLockedException $e) {
            Log::error('Payment, PaymentLockedException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        } catch (OWLInsufficientBalanceException $e) {
            Log::critical('Payment, Balance error, OWLInsufficientBalanceException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            Paymentable::setFailureReason($loan);
            throw $e;
        } catch (Exception $e) {
            Log::critical('Payment, Uknown error', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        } finally {
            Log::info('Update Loan Payment Details', ['payment_id' => $loan->payment->id, 'payment_type' => $loan->payment_type]);

            $loan->setPaymentDetails($loan->payment->id, $loan->payment_type);
        }

        $this->setLoanState($loan, $status, $failed_payment, Carbon::now());

        return $status;
    }

    protected function isFallbackAllowed($loan_type_id)
    {
        if (!env('FALLBACK_ALLOWED')) {
            return false;
        }

        $transfer_types = TransferType::getAvailableTypes($loan_type_id);

        return $transfer_types->where('name', constants('TRANSFER_TYPES.CASH_PAYMENT'))->first();
    }

    public function saveLoanToHC($loan)
    {
        try {
            Log::info('Storing to armsoft');

            // Let us resolve HcService after loan verifying
            $hc_service = HcServiceFactory::build($loan->loan_type_id);
            $hc_service->save($loan);
            Log::info('Stored to armsoft successfully');
        } catch (HcException $e) {
            // Log as critical to handle manually and proceed
            Log::critical('Storing to Armsoft failed', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString(), 'loan' => $loan]);
        }
    }

    public function regenerateSchedule($loan)
    {
        $calculator = $this->initCalculatorByLoan($loan);
        // TODO: Use max allowed duration instead of $loan->months (getAllowedMaxDuration in LoanServiceOVL)
        [
            'schedule' => $schedule,
            'schedule_summary' => $schedule_summary,
        ] = $this->getScheduleAndSummary($calculator, $loan->monthly_payment, $loan->sign_date);

        return $this->updateLoan($loan, $schedule_summary, $schedule);
    }

    public function updateTopUpSchedule($loan, $schedule)
    {
        $loan->deleteLoanSchedule();

        $loan->storeLoanSchedule($schedule);

        return $loan;
    }

    public function regenerateTopUpSchedule($loan, $top_up_offer)
    {
        $top_up_service_ocl = resolve('App\Services\TopUpServiceOCL');

        $top_up_offer_detail = $top_up_offer->top_up_offer_detail;

        $calculator = $this->initCalculatorByLoan($loan);
        $schedule = $calculator->generateSchedule($loan->monthly_payment, $loan->sign_date);
        $amortized_schedule = $top_up_service_ocl->amortizeLoanSchedule($schedule, $top_up_offer_detail);

        $apr = $calculator->calculateAPR($loan->monthly_payment, $loan->sign_date, $loan->withdrawal_fee, $loan->months, $amortized_schedule);
        $loan->update([
            'apr' => $apr,
        ]);

        Log::info('regenerateTopUpSchedule, re-calculated apr', ['current_apr' => $loan->apr, 'new_apr' => $apr]);

        return $this->updateTopUpSchedule($loan, $amortized_schedule);
    }

    // INFO: Method basically used for getting min-max monthly amounts for the slider in UI
    public function calculateMonthlyPaymentRange($payload): array
    {
        // We are sending offered_credit from UI to back-end to avoid unnecessary getCitizen() call.
        $configs = $this->getLoanConfigs();
        $service_fee_rate = $this->getServiceFeeRate($configs, $payload['offered_credit']);
        $calculator = $this->initCalculator([
            'amount' => $payload['amount'],
            'loan_type' => $this->type,
            'interest_rate' => $payload['offered_credit']['interest_rate'],
            'duration' => $payload['offered_credit']['duration'],
            'service_fee_rate' => $service_fee_rate,
        ]);

        // Ignore osm for new ovl customers
        // TODO: this logic doesn't belong here, but rather to rule engine
        [$monthly_min, $monthly_max] = $this->getMonthlyMinMax($calculator, $configs, $payload['osm'], !$payload['is_new_ovl_customer']);

        return [
            'monthly_min' => $monthly_min,
            'monthly_max' => $monthly_max,
            'service_fee_rate' => $service_fee_rate,
        ];
    }

    private function getMonthlyMinMax($calculator, $configs, $osm = 0, $osm_disabled = true)
    {
        [$min, $max] = $calculator->monthlyMinMax($configs['min_months']);

        if ($osm_disabled) {
            return [$min, $max];
        }

        $allowed_monthly_payment = min(
            $max,
            NumberHelper::roundDown($osm, 1000)
        );

        $allowed_max = $osm ? $allowed_monthly_payment : $max;
        $allowed_min = $min > $osm ? $allowed_monthly_payment : $min;

        // Max monthly payment should not be greater than OSM
        return [$allowed_min, $allowed_max];
    }

    // INFO: Method basically used for getting loan terms in UI based on submitted loan details
    public function calculateScheduleDetails($payload)
    {
        // We are sending offered_credit from UI to back-end to avoid unnecessary getCitizen() call.
        $configs = $this->getLoanConfigs();
        $service_fee_rate = $this->getServiceFeeRate($configs, $payload['offered_credit']);
        $calculator = $this->initCalculator([
            'amount' => $payload['amount'],
            'loan_type' => $this->type,
            'interest_rate' => $payload['offered_credit']['interest_rate'],
            'duration' => $payload['offered_credit']['duration'],
            'service_fee_rate' => $service_fee_rate,
        ]);

        // Because we do not send monthly_payment key during OCL, that's why we have made overrides via this method
        $monthly_payment = $this->getMonthlyPayment($calculator, $payload);

        [
            'schedule_summary' => $schedule_summary,
        ] = $this->getScheduleAndSummary($calculator, $monthly_payment);

        $withdrawal_fee = $this->getWithdrawalFee($payload['amount'], $configs['withdrawal_fee_rate']);
        $apr = $calculator->calculateAPR($monthly_payment, Carbon::now(), $withdrawal_fee['withdrawal_fee']);

        return array_merge($schedule_summary, [
            'apr' => $apr,
        ]);
    }

    public function getMonthlyPayment($calculator, $payload)
    {
        $schedule = $calculator->generateSchedule();

        return $schedule[0]['payment'];
    }

    public function shouldUpdateContractNumber($loan): bool
    {
        if (!$loan->contract_number) {
            return true;
        }

        return false;
    }

    protected function checkLoanAvailableAmount($loan)
    {
        $citizen_service = resolve('App\Interfaces\ICitizenService');
        $loan_security = $loan->loan_security;
        $loan_amount = $loan->amount;

        if ($loan->isTopUp()) {
            $loan_amount = $loan->top_up_amount;
        }

        $citizen = $citizen_service->getCitizen([
            'document_number' => $loan_security->document_number,
            'token' => $loan_security->owl_token,
        ], true);

        Log::info('Check Loan Available Amount', ['credit' => $citizen['credit'], 'loan_amount' => $loan_amount]);

        $available_amount = $citizen['credit']['amount'] ?? 0;
        if ($loan_amount > $available_amount) {
            throw new CheckLoanAvailableAmountException();
        }
    }

    public function archiveAndCleanUpDocuments($loan)
    {
        if ($loan->isTopUp()) {
            $withdraw_doc_name = LoanDocument::WITHDRAW_CHECK['name'];
            $vendor_doc_name = LoanDocument::VENDOR_FILE['name'];

            $loan->archiveLoanDocuments();
            $loan->archiveLoanDocuments([$withdraw_doc_name, $vendor_doc_name], true);

            $loan_document_service = DocumentServiceFactory::build($loan->loan_type_id);

            $document_names = array_merge(
                $loan_document_service->getDocumentsForRemove(true, $loan),
                $loan_document_service->getDocumentsForRemove(false, $loan),
                [
                    $withdraw_doc_name,
                    $vendor_doc_name,
                ]
            );

            $loan->documents()
                ->whereIn('document_type', $document_names)
                ->delete();
        }
    }

    public function associateLoanSecurityWithPreapprovedOffer($loan_security)
    {
        $preapproved_top_up_offer = PreapprovedCreditOffer::getValidTopUpOfferByLoanType($this->type);
        $preapproved_top_up_offer->loan_security()->associate($loan_security);
        $preapproved_top_up_offer->save();
        // Attach loan_security_preapproved_credit_offer pivot table for DWH usage,
        // because multiple loan securities can be attached to a single preapproved offer,
        // that's why we need to have this relation to use as history data
        $loan_security->preapproved_credit_offers_pivot()->syncWithoutDetaching($preapproved_top_up_offer->id);

        return $preapproved_top_up_offer;
    }

    protected function createCitizenForTopUp($loan, $details)
    {
        $archived_citizen = $loan->archive_citizen()->toArray();

        $details = array_merge($archived_citizen, $details);
        $new_citizen = $loan->citizen()->create($details);
        $new_citizen->passports()->createMany($details['passports']);
    }

    protected function isTopUpOfferSubmission(): bool
    {
        return (bool) PreapprovedCreditOffer::getValidTopUpOfferByLoanType($this->type);
    }
}
