<?php

namespace App\Dtos\InternalClient;

use Illuminate\Contracts\Support\Arrayable;

class HashableDataDto implements Arrayable
{
    /**
     * @var array|null
     */
    private $citizen;

    /**
     * @var string|null
     */
    private $phone_number;

    /**
     * @var string|null
     */
    private $email;

    public function __construct($citizen, $phone_number, $email)
    {
        $this->setValue('citizen', $citizen);
        $this->setValue('phone_number', $phone_number);
        $this->setValue('email', $email);
    }

    public function getValue($name)
    {
        return $this->{$name};
    }

    /**
     * @param $value mixed
     */
    public function setValue(string $field, $value): void
    {
        $this->{$field} = $value;
    }

    public function toArray(): array
    {
        return [
            'citizen' => $this->citizen,
            'phone_number' => $this->phone_number,
            'email' => $this->email,
        ];
    }
}
