<?php

namespace App\Jobs;

use App\Factory\DocumentServiceFactory;
use App\Models\Loan;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Log;
use PDF;

class GenerateLoanDocuments implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private $loan;
    private $obfuscated;
    private $loanDocumentService;
    private $is_sample;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($loan, $obfuscated = true, $is_sample = false)
    {
        $this->loan = $loan;
        $this->loanDocumentService = DocumentServiceFactory::build($loan->loan_type_id);
        $this->obfuscated = $obfuscated;
        $this->is_sample = $is_sample;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // Deleting documents, because user can go back and change loan details,
        // so we need to generate them again
        $this->removeDocuments();
        $directory = $this->loanDocumentService->prepareDirectory();

        $types = $this->loanDocumentService->getDocumentTypes($this->obfuscated, $this->loan);

        foreach ($types as $type) {
            $this->generatePdf($this->loan, $type['name'], $directory, $type['public'], $type['path'] ?? null);
        }
    }

    private function removeDocuments()
    {
        if ($this->loan->isTopUp()) {
            $this->loan->archiveLoanDocuments();
        }

        $document_names = $this->loanDocumentService->getDocumentsForRemove($this->obfuscated, $this->loan);

        $this->loan->documents()
            ->whereIn('document_type', $document_names)
            ->delete();
    }

    protected function generatePdf($loan, $type, $directory, $public, $file_path)
    {
        $path = $this->loanDocumentService->getCitizenPath($loan, $type, $directory);

        if (!empty($file_path)) {
            $content = file_get_contents(resource_path($file_path));
        } else {
            $pdf = PDF::loadView(
                $this->loanDocumentService->getPdfTemplatePath($type),
                $this->loanDocumentService->composePdfData($loan, $this->is_sample)
            );

            $content = $pdf->output();
        }

        $this->loanDocumentService->persistDocument($loan, $path, $type, $content, $public);
    }

    public function failed(Exception $exception)
    {
        Log::error('Generate Loan Documents failed', [
            'loan id' => $this->loan->id,
            'message' => $exception->getMessage(),
        ]);
    }
}
