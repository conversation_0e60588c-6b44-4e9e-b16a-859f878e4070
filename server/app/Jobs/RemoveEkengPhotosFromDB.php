<?php

namespace App\Jobs;

use App\Models\LoanSecurityMeta;
use Illuminate\Contracts\Queue\ShouldQueue;
use Log;

class RemoveEkengPhotosFromDB implements ShouldQueue
{
    protected $offset_file_path;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->offset_file_path = public_path('ekeng_loan_security_meta_offset.json');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        if (file_exists($this->offset_file_path)) {
            $content = file_get_contents($this->offset_file_path);
            $json_data = json_decode($content, true);
        } else {
            $last_ekeng_data = LoanSecurityMeta::whereIn('step', [
                constants('META_STEPS.EKENG_DATA'),
                constants('META_STEPS.CITIZEN_INFO'),
            ])
                ->orderBy('id', 'asc')
                ->first();

            $json_data = [
                'loan_security_meta_id' => $last_ekeng_data->id - 1,
                'faild_ids' => [],
            ];
            $json_string = json_encode($json_data);
            file_put_contents($this->offset_file_path, $json_string);
        }

        LoanSecurityMeta::whereIn('step', [
                constants('META_STEPS.EKENG_DATA'),
                constants('META_STEPS.CITIZEN_INFO'),
            ])
            ->where('id', '>', $json_data['loan_security_meta_id'])
            ->orderBy('id', 'asc')
            ->chunk(100, function ($loan_security_metas) use (&$json_data) {
                foreach ($loan_security_metas as $loan_security_meta) {
                    try {
                        $data = json_decode($loan_security_meta['content'], true);

                        if (isset($data['ekeng']['passport_data']['Photo'])) {
                            $data['ekeng']['passport_data']['Photo'] = null;

                            $loan_security_meta['content'] = json_encode($data, JSON_UNESCAPED_UNICODE);
                            $loan_security_meta->save();
                        }
                    } catch (Exception $e) {
                        Log::error('Ekeng remove photo Exception', ['error' => $e->getMessage()]);

                        array_push($json_data['faild_ids'], $loan_security_meta->id);
                    }

                    $json_data['loan_security_meta_id'] = $loan_security_meta->id;
                    $new_json_string = json_encode($json_data);
                    file_put_contents($this->offset_file_path, $new_json_string);
                }
            });
    }
}
