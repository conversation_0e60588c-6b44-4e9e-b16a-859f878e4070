<?php

namespace App\Jobs;

use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Log;

class NotifyLoanTypeStatus implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $push_notification_service = resolve('App\Interfaces\Notifier\IPushNotificationService');
        $notifiable_service = resolve('App\Interfaces\Pallaton\INotifiableUsersService');

        $notifiable_users = $notifiable_service->getNotifiableUsers();

        $notifiable_users->map(function ($user) use ($push_notification_service) {
            $params = [
                'subject' => __('notification.app_availability_title'),
                'body' => __('notification.app_availability_message'),
                'userUuId' => $user->user_uuid,
            ];

            $push_notification_service->create($params);
        });

        $notifiable_service->updateNotifiableUsers($notifiable_users);
    }

    public function failed(Exception $exception)
    {
        Log::info('Notify about loan type status', [
            'message' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }
}
