<?php

namespace App\Jobs;

use App\Interfaces\ISmsService;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendReviewedSMS implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private $loan;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($loan)
    {
        $this->loan = $loan;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(ISmsService $smsService)
    {
        $gc_reml_admins = User::getGCREMLAdmins();

        foreach ($gc_reml_admins as $admin) {
            $smsService->send(
                $admin->phone_number,
                __('sms.reviewed_message',
                    [
                        'contract_number' => $this->loan->contract_number,
                    ])
            );
        }
    }
}
