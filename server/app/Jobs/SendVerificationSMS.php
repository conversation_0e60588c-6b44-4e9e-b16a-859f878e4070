<?php

namespace App\Jobs;

use App\Factory\SecurityServiceFactory;
use App\Interfaces\ISmsService;
use App\Services\VerificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendVerificationSMS implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private $suuid;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($suuid)
    {
        $this->suuid = $suuid;

        $this->queue = config('queue.types.sms');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(ISmsService $smsService, VerificationService $verificationService)
    {
        $securityService = SecurityServiceFactory::build($this->suuid);
        $loan = $securityService->resolveLoan();

        $code = $verificationService->generateVerificationCode();

        $smsService->send($loan->citizen->phone_number, __('sms.verification_message', ['code' => $code]));

        $verificationService->persistVerificationCode($this->suuid, $code);
    }
}
