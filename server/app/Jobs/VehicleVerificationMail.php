<?php

namespace App\Jobs;

use App\Mail\VehicleMail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Mail;

class VehicleVerificationMail implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private $loan = null;
    private $agent_admins = null;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($loan, $agent_admins)
    {
        $this->loan = $loan;
        $this->agent_admins = $agent_admins;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        foreach ($this->agent_admins as $admin) {
            Mail::to($admin->email, env('MAIL_FROM_ADDRESS'))
                ->bcc(env('MAIL_BCC'))
                ->send(new VehicleMail($this->loan));
        }
    }
}
