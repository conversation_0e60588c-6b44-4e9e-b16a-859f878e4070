<?php

namespace App\Jobs;

use App\Mail\LoanProcessingMail;
use App\Models\Loan;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Mail;

class SendLoanProcessingEmail implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private $loan;
    private $admins;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($loan, $gc_reml_sub_admins)
    {
        $this->loan = $loan;
        $this->admins = $gc_reml_sub_admins;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        if ($this->loan->status == Loan::PROCESSING) {
            foreach ($this->admins as $admin) {
                Mail::to($admin['email'], env('MAIL_FROM_ADDRESS'))
                    ->send(new LoanProcessingMail($this->loan));
            }
        }
    }
}
