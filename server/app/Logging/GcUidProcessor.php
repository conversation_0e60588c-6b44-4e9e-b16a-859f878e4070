<?php

namespace App\Logging;

use Illuminate\Log\Logger;

class GcUidProcessor
{
    const LENGTH = 16;

    /**
     * Push uid processor for adding a unique identifier into records.
     *
     * @return void
     */
    public function __invoke(Logger $logger)
    {
        collect($logger->getHandlers())->each(function ($handler) {
            $handler->pushProcessor(new \Monolog\Processor\UidProcessor(self::LENGTH));
        });
    }
}
