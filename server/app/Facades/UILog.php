<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

class UILog extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return 'log';
    }

    public static function debug($message, $log_channel, $payload = [])
    {
        return parent::channel($log_channel)->debug($message, $payload);
    }

    public static function warning($message, $log_channel, $payload = [])
    {
        return parent::channel($log_channel)->warning($message, $payload);
    }

    public static function info($message, $log_channel, $payload = [])
    {
        return parent::channel($log_channel)->info($message, $payload);
    }

    public static function error($message, $log_channel, $payload = [])
    {
        return parent::channel($log_channel)->error($message, $payload);
    }

    public static function critical($message, $log_channel, $payload = [])
    {
        return parent::channel($log_channel)->critical($message, $payload);
    }
}
