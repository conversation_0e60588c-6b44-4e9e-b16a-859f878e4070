<?php

namespace App\Calculator;

use function Functional\map;
use function Functional\reduce_left;

class LoanCalculatorOWL extends AbstractLoanCalculator
{
    public function __construct($amount, $annual_rate, $service_fee_rate, $params)
    {
        $max_duration = $params['max_duration'];

        parent::__construct($amount, $annual_rate, $service_fee_rate, $max_duration);
    }

    public function generateSchedule($monthly_payment = null, $start_date = null)
    {
        $r = range(1, $this->max_duration);
        $today = $this->today($start_date);

        $s = reduce_left($r, function ($m, $i, $collection, $acc) use ($today, &$total) {
            [$next_payment_date, $days] = $this->getNextPaymentDate($today, $i);

            $base = 0;
            $balance = $this->amount;
            $service_fee = $this->percentage($this->amount, $this->service_fee_rate) * $days;

            if ($i === $this->max_duration - 1) {
                $base = $this->amount;
                $balance = 0;
            }

            $payment = $base + $service_fee;

            array_push($acc, [
                'base' => $base,
                'service_fee' => $service_fee,
                'balance' => $balance,
                'payment' => $payment,
                'date' => $next_payment_date,
            ]);

            return $acc;
        }, []);

        return map($s, function ($r) {
            $balance = $r['balance'] < 0.5 ? 0 : $this->decimalPoints($r['balance']);
            $service_fee = $this->decimalPoints($r['service_fee']);

            return array_merge($r, [
                'balance' => $balance,
                'service_fee' => $service_fee,
                'service_fee_plain' => $service_fee,
                'service_fee_interest' => 0,
                'payment' => $this->decimalPoints($r['payment']),
                'base' => $this->decimalPoints($r['base']),
            ]);
        });
    }

    protected function calculateRealRate($schedule)
    {
        return;
    }
}
