<?php

namespace App\Calculator;

use Carbon\Carbon;

class LoanCalculatorRegenerated extends LoanCalculator
{
    public function __construct($amount, $original_amount, $annual_rate, $service_fee_rate, $params)
    {
        $max_duration = $params['max_duration'];
        // If the new amount is bigger than the original one (which means the client got
        // the loan recently), we can't know the maxiumm length of the schedule, +10 is a
        // good approximation
        if ($amount > $original_amount) {
            $max_duration += 10;
        }
        parent::__construct($amount, $annual_rate, $service_fee_rate, $params);
    }

    public function regenerate($monthly_payment, $new_max, $start_date = null)
    {
        $now = Carbon::now();
        $date = $start_date->copy();

        $date->month = $date->day <= $now->day ? $now->month : $now->month - 1;
        $date->year = $now->year;

        $schedule = $this->generateSchedule($monthly_payment, $date);

        if ($new_max < count($schedule)) {
            $schedule = array_slice($schedule, 0, $new_max);

            $last = &$schedule[count($schedule) - 1];
            $last['base'] = $schedule[count($schedule) - 2]['balance'];
            $last['payment'] = $last['base'] + $last['service_fee'];
            $last['balance'] = 0;
        }

        return $schedule;
    }

    public function today($d = null)
    {
        $now = Carbon::now();

        $today = $d ?? $now;
        $today = $today->copy()->setTimezone(constants('ARM_TIMEZONE'));

        return $today;
    }

    protected function getNextPaymentDate($today, $i)
    {
        $current_date = $today->copy()->addMonthsNoOverflow($i);
        // First month days is usually smaller than full month
        if ($i === 0) {
            $current_date = $this->today();
        }

        $next_payment_date = $today->copy()->addMonthsNoOverflow($i + 1);

        return [$next_payment_date, $next_payment_date->diffInDays($current_date)];
    }
}
