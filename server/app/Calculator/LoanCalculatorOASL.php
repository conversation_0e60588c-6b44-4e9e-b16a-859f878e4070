<?php

namespace App\Calculator;

use Carbon\Carbon;
use function Functional\map;
use function Functional\reduce_left;
use function Functional\reduce_right;
use function Functional\take_right;

class LoanCalculatorOASL extends AbstractLoanCalculator
{
    const MONTHS = 120;

    private static $ANNUITY_START_MONTH = 6;

    public function __construct($amount, $annual_rate, $service_fee_rate, $params)
    {
        parent::__construct($amount, $annual_rate, $service_fee_rate, self::MONTHS);

        if ($params['is_kfw']) {
            self::$ANNUITY_START_MONTH = 0;
        }
    }

    public function generateSchedule($monthly_payment = null, $start_date = null)
    {
        $schedule = $this->generateScheduleWithOffset(0, $start_date);
        $last = $schedule[count($schedule) - 1];

        // If the final balance doesn't settle on 0, calculate the
        // offset and regenerate the schedule with offset
        if ($last['balance_raw'] !== 0) {
            $principal_offset = $this->computeMonthlyExtraFee($schedule);

            return $this->generateScheduleWithOffset($principal_offset, $start_date);
        }

        return $schedule;
    }

    public function summarizeSchedule($schedule)
    {
        $sum = parent::summarizeSchedule($schedule);

        unset($sum['monthly_payment']);

        return $sum;
    }

    public function generateScheduleWithOffset($principal_offfset = 0, $start_date = null)
    {
        $r = range(1, self::MONTHS);
        $today = $start_date ?? Carbon::now();

        $s = reduce_left($r, function ($m, $i, $collection, $acc) use ($today, $principal_offfset) {
            [$next_payment_date, $days] = $this->getNextPaymentDate($today, $i);
            $last_balance = $i === 0 ? $this->amount : $acc[$i - 1]['balance'];
            $monthly_rate = $this->percentage(
              1,
              ($this->annual_rate - $this->service_fee_rate) / self::DAYS_IN_YEAR * $days
            );
            $service_fee = $this->decimalPoints($last_balance * $monthly_rate);

            $base_raw = 0;
            $payment_raw = $base_raw + $service_fee;
            // For the rest of 114 months compute an annuity
            if ($i > self::$ANNUITY_START_MONTH - 1) {
                $payment_raw = $this->pmt(
                    $this->annual_rate / 100 / 12,
                    self::MONTHS - self::$ANNUITY_START_MONTH,
                    -$this->amount,
                    0
                ) + $principal_offfset;

                $base_raw = $payment_raw - $service_fee;
            }

            // If principalOffset is poitive number, it means we are adjusting the balance
            // In order to avoid rounding errors, for last month we just take the previous
            // month balance as principal
            if ($principal_offfset !== 0 && $i === self::MONTHS - 1) {
                $base_raw = $last_balance;
            }

            $base = $this->decimalPoints($base_raw);
            $payment = $this->decimalPoints($base + $service_fee);
            $balance = $last_balance - $base;

            array_push($acc, [
                'base_raw' => $base_raw,
                'base' => $base,
                'service_fee' => $service_fee,
                'service_fee_plain' => 0,
                'service_fee_interest' => $service_fee,
                'balance' => $balance,
                'payment_raw' => $payment_raw,
                'payment' => $payment,
                'date' => $next_payment_date,
                'days' => $days,
                'monthly_rate' => $monthly_rate,
            ]);

            return $acc;
        }, []);

        return map($s, function ($r) {
            $balance = $r['balance'] < 0.5 ? 0 : $this->decimalPoints($r['balance']);

            return array_merge($r, [
                'balance' => $balance,
                'balance_raw' => $r['balance'],
            ]);
        });
    }

    protected function computeMonthlyExtraFee($schedule)
    {
        $delta = $this->computeDelta($schedule);
        $annuity = $schedule[self::$ANNUITY_START_MONTH]['payment_raw'];

        return $this->getAnnuityAmount($schedule) / $delta - $annuity;
    }

    protected function computeDelta($schedule)
    {
        $annuity_schedule = take_right($schedule, self::MONTHS - self::$ANNUITY_START_MONTH);

        return reduce_right($annuity_schedule, function ($r, $i, $s, $delta) {
            $j = $i === 0 ? 0 : 1;

            return $delta / (1 + $r['monthly_rate']) + $j;
        }, 1);
    }

    // The balance of the sixth month is the amount for wich annuity is computed
    // For kfw we compute annuity righ from the first month, hence we take full amount
    protected function getAnnuityAmount($schedule)
    {
        if (!self::$ANNUITY_START_MONTH) {
            return $this->amount;
        }

        return $schedule[self::$ANNUITY_START_MONTH - 1]['balance'];
    }

    protected function calculateRealRate($schedule)
    {
        return;
    }
}
