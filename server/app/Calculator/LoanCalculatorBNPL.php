<?php

namespace App\Calculator;

use App;
use Carbon\Carbon;
use function Functional\reduce_left;

class LoanCalculatorBNPL extends AbstractLoanCalculator
{
    const PAYMENT_DAY = 7;

    public function __construct($amount, $annual_rate, $service_fee_rate, $params)
    {
        $max_duration = $params['max_duration'];
        parent::__construct($amount, $annual_rate, $service_fee_rate, $max_duration);
    }

    public function generateSchedule($monthly_payment = null, $start_date = null)
    {
        $r = range(1, $this->max_duration);
        $today = $this->today($start_date);

        $total = 0;
        $monthly_payment = floor($this->amount / $this->max_duration / 10) * 10;

        $s = reduce_left($r, function ($m, $i, $collection, $acc) use ($monthly_payment, $today, &$total) {
            [$next_payment_date] = $this->getNextPaymentDate($today, $i);

            $last_balance = $i === 0 ? $this->amount : $acc[$i - 1]['balance'];

            // For last month principal is amount minus the sum of principals of prevous months
            $base_raw = ($i === $this->max_duration - 1) ? $this->amount - $total : $monthly_payment;

            $service_fee = 0;
            $base = $this->decimalPoints($base_raw);
            $payment = $base;
            $total = $total + $base;
            $balance = $last_balance - $base_raw;

            array_push($acc, [
                'base_raw' => $base_raw,
                'base' => $base,
                'balance' => $balance,
                'service_fee_plain' => $service_fee,
                'service_fee_interest' => $service_fee,
                'service_fee' => $service_fee,
                'payment' => $payment,
                'date' => $next_payment_date,
            ]);

            return $acc;
        }, []);

        return $s;
    }

    protected function getNextPaymentDate($today, $i)
    {
        $current_date = $today->copy()->addMonthsNoOverflow($i);
        $next_payment_date = $today->copy()->addMonthsNoOverflow($i + 1);
        // If APP_ENV='bnpl_mocked_credit_line_creation' we need to use current day to create mocked transaction date correctly
        $next_payment_date->day = App::environment('bnpl_mocked_credit_line_creation') ? Carbon::now()->day : self::PAYMENT_DAY;

        return [$next_payment_date, $next_payment_date->diffInDays($current_date)];
    }

    protected function calculateRealRate($schedule)
    {
        return;
    }
}
