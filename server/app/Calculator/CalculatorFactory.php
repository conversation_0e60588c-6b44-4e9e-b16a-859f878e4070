<?php

namespace App\Calculator;

use App\Services\SecurityUtilityService;

class CalculatorFactory
{
    public static function build($type, $amount, $rate, $service_fee_rate, $params = [])
    {
        if ($type == constants('LOAN_TYPES.OCL')) {
            return new LoanCalculatorOCL($amount, $rate, $service_fee_rate, $params);
        } elseif ($type == constants('LOAN_TYPES.OIQL')) {
            return new LoanCalculatorOIQL($amount, $rate, $service_fee_rate, $params);
        } elseif ($type == constants('LOAN_TYPES.OASL')) {
            $term = $params['term'];
            $params['is_kfw'] = $term->isKfw();
            $solar_panel_type = $params['solar_panel_type'];

            if ($solar_panel_type->isTun() && $params['is_kfw']) {
                return new LoanCalculatorOASLForgive($amount, $rate, $service_fee_rate, $params);
            }

            return new LoanCalculatorOASL($amount, $rate, $service_fee_rate, $params);
        } elseif (SecurityUtilityService::isOwlLoan($type)) {
            return new LoanCalculatorOWL($amount, $rate, $service_fee_rate, $params);
        } elseif ($type == constants('LOAN_TYPES.PL')) {
            return new LoanCalculatorPL($amount, $rate, $service_fee_rate, $params);
        } elseif ($type == constants('LOAN_TYPES.REML')) {
            return new LoanCalculatorREML($amount, $rate, $service_fee_rate, $params);
        } elseif ($type == constants('LOAN_TYPES.VLX')) {
            return new LoanCalculatorVLX($amount, $rate, $service_fee_rate, $params);
        } elseif ($type == constants('LOAN_TYPES.BNPL')) {
            return new LoanCalculatorBNPL($amount, $rate, $service_fee_rate, $params);
        } elseif ($type == constants('LOAN_TYPES.OIWL')) {
            return new LoanCalculatorOWL($amount, $rate, $service_fee_rate, $params);
        }

        return new LoanCalculator($amount, $rate, $service_fee_rate, $params);
    }
}
