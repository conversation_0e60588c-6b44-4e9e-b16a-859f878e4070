<?php

namespace App\Providers;

use App\Helpers\RequestHelper;
use App\Models\EkengRequest;
use App\Models\HC\HcClient;
use App\Models\HC\HcLoansCredDisbursements;
use App\Models\LoanSecurity;
use App\Models\Pallaton\Notification;
use App\Observers\EkengRequestObserver;
use App\Observers\HcClientObserver;
use App\Observers\HcLoansCredDisbursementsObserver;
use App\Observers\LoanSecurityObserver;
use App\Observers\NotificationObserver;
use App\Services\SecurityUtilityService;
use App\Validators\RestValidator;
use App\Validators\RestValidatorNova;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;
use Validator;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        // If request is in nova we should not modify request validator
        if (!RequestHelper::isNovaRequst($this->app->request)) {
            Validator::resolver(function ($translator, $data, $rules, $messages) {
                return new RestValidator($translator, $data, $rules, $messages);
            });
        }

        // If request is in nova we should modify request validator
        if (RequestHelper::isNovaRequst($this->app->request)) {
            Validator::resolver(function ($translator, $data, $rules, $messages) {
                return new RestValidatorNova($translator, $data, $rules, $messages);
            });
        }

        // If request is idram we should add loan_type_id in request
        if (strpos($this->app->request->getPathInfo(), '/idram/request/')) {
            $this->app->request->request->add(['loan_type_id' => constants('LOAN_TYPES.OIDL')]);
        }

        // If request is fastshift we should add loan_type_id in request
        if (strpos($this->app->request->getPathInfo(), '/fastshift/request/')) {
            $this->app->request->request->add(['loan_type_id' => constants('LOAN_TYPES.OFSL')]);
        }

        // If request is telcell we should add loan_type_id in request
        if (strpos($this->app->request->getPathInfo(), '/telcell/request/')) {
            $loan_type_id = constants('LOAN_TYPES.OTCL');
            if ($this->app->request->query('loan_type_id') == constants('LOAN_TYPES.OVL')) {
                $loan_type_id = constants('LOAN_TYPES.OVL');

                $this->app->request->request->add([
                    'referrer_source' => constants('REFERRER_SOURCES.TELCELL'),
                ]);
            }

            $this->app->request->request->add(['loan_type_id' => $loan_type_id]);
        }

        // If request is easypay we should add loan_type_id in request
        if (strpos($this->app->request->getPathInfo(), '/easypay/request/')) {
            $this->app->request->request->add(['loan_type_id' => constants('LOAN_TYPES.OEPL')]);
        }

        // If request is Upay we should add loan_type_id in request
        if (strpos($this->app->request->getPathInfo(), '/upay/request/')) {
            $this->app->request->request->add(['loan_type_id' => constants('LOAN_TYPES.OUPL')]);
        }

        // If request is velox we should add loan_type_id in request
        if (strpos($this->app->request->getPathInfo(), '/velox/')) {
            $this->app->request->request->add(['loan_type_id' => constants('LOAN_TYPES.VLX')]);
        }

        // If request is pl we should add loan_type_id in request
        if (strpos($this->app->request->getPathInfo(), '/pl/')) {
            $this->app->request->request->add([
                'loan_type_id' => constants('LOAN_TYPES.PL'),
                'referrer_source' => SecurityUtilityService::getVendor()->name ?? null,
            ]);
        }

        if (strpos($this->app->request->getPathInfo(), '/internal-bnpl/') ||
            strpos($this->app->request->getPathInfo(), '/bnpl/') ||
            strpos($this->app->request->getPathInfo(), '/armed/')) {
            $this->app->request->request->add([
                    'loan_type_id' => constants('LOAN_TYPES.BNPL'),
                    'referrer_source' => SecurityUtilityService::getVendor()->name ?? null,
                ]);
        }

        // If request is pay4me  we should add loan_type_id in request
        if (strpos($this->app->request->getPathInfo(), '/pay4me/')) {
            $this->app->request->request->add(['loan_type_id' => constants('LOAN_TYPES.OBL')]);
        }

        Blade::directive('number_to_money', function ($number) {
            return "<?php echo number_format($number, 2, '.', ','); ?>";
        });

        $this->app->singleton('api.exception', function ($app) {
            return new \App\Exceptions\ApiExceptionHandler($app['Illuminate\Contracts\Debug\ExceptionHandler'], Config('api.errorFormat'), Config('api.debug'));
        });

        // Model Observers
        HcLoansCredDisbursements::observe(HcLoansCredDisbursementsObserver::class);
        LoanSecurity::observe(LoanSecurityObserver::class);
        HcClient::observe(HcClientObserver::class);
        Notification::observe(NotificationObserver::class);
        EkengRequest::observe(EkengRequestObserver::class);
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
    }
}
