<?php

namespace App\Providers;

use App\Factory\DocumentServiceFactory;

class DocumentServiceProvider extends ServiceResolveProvider
{
    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
    }

    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind('App\Interfaces\IDocumentService', function ($app) {
            $loan_type_id = $this->getLoanType($app->request);

            return DocumentServiceFactory::build($loan_type_id);
        });
    }
}
