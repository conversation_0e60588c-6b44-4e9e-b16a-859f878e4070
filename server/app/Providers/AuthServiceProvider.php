<?php

namespace App\Providers;

use App\Models\AffectedRule;
use App\Models\Blacklist;
use App\Models\BlacklistReason;
use App\Models\CashPayment;
use App\Models\CitizenLock;
use App\Models\COVID19\Blacklist as SupportBlacklist;
use App\Models\COVID19\Loan as SupportLoan;
use App\Models\COVID19\LoanDocument as SupportLoanDocument;
use App\Models\CreditLine\PurchaseRequest;
use App\Models\CreditLine\Transaction;
use App\Models\CreditLine\Vendor;
use App\Models\CreditOffer;
use App\Models\Loan;
use App\Models\LoanDocument;
use App\Models\LoanDocumentHistory;
use App\Models\LoanHistory;
use App\Models\MarketingMedia;
use App\Models\Merchant;
use App\Models\MerchantBlacklist;
use App\Models\Package;
use App\Models\Pallaton\Profile;
use App\Models\PipeType;
use App\Models\QrCode;
use App\Models\QrOwner;
use App\Models\RealEstateSeller;
use App\Models\ReferralCode;
use App\Models\ReferralCodeRepayment;
use App\Models\Rule;
use App\Models\Stabilization\Loan as StabilizationLoan;
use App\Models\StoryMedia;
use App\Models\TransactionDocument;
use App\Models\User;
use App\Models\VehicleModel;
use App\Models\VehiclePrice;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;
use Laravel\Nova\Events\ServingNova;
use Laravel\Nova\Nova;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        Loan::class => \App\Policies\App\LoanPolicy::class,
    ];

    protected $nova_policies = [
        Package::class => \App\Policies\Nova\PackagePolicy::class,
        Loan::class => \App\Policies\Nova\LoanPolicy::class,
        RealEstateSeller::class => \App\Policies\Nova\RealEstateSellerPolicy::class,
        LoanDocument::class => \App\Policies\Nova\LoanDocumentPolicy::class,
        TransactionDocument::class => \App\Policies\Nova\TransactionDocumentPolicy::class,
        User::class => \App\Policies\Nova\UserPolicy::class,
        VehicleModel::class => \App\Policies\Nova\VehicleModelPolicy::class,
        Blacklist::class => \App\Policies\Nova\BlacklistPolicy::class,
        MerchantBlacklist::class => \App\Policies\Nova\MerchantBlacklistPolicy::class,
        VehiclePrice::class => \App\Policies\Nova\VehiclePricePolicy::class,
        Rule::class => \App\Policies\Nova\RulePolicy::class,
        Profile::class => \App\Policies\Nova\ProfilePolicy::class,

        // Tmp For-Support-Credit
        SupportBlacklist::class => \App\Policies\Nova\COVID19\SupportBlacklistPolicy::class,
        SupportLoan::class => \App\Policies\Nova\COVID19\SupportLoanPolicy::class,
        SupportLoanDocument::class => \App\Policies\Nova\COVID19\SupportLoanDocumentPolicy::class,

        StabilizationLoan::class => \App\Policies\Nova\Stabilization\StabilizationLoanPolicy::class,
        Vendor::class => \App\Policies\Nova\VendorPolicy::class,
        CreditOffer::class => \App\Policies\Nova\CreditOfferPolicy::class,
        AffectedRule::class => \App\Policies\Nova\AffectedRulePolicy::class,
        QrCode::class => \App\Policies\Nova\QrCodePolicy::class,
        QrOwner::class => \App\Policies\Nova\QrOwnerPolicy::class,
        PurchaseRequest::class => \App\Policies\Nova\PurchaseRequestPolicy::class,
        ReferralCode::class => \App\Policies\Nova\ReferralCodePolicy::class,
        ReferralCodeRepayment::class => \App\Policies\Nova\ReferralCodeRepaymentPolicy::class,
        PipeType::class => \App\Policies\Nova\PipeTypePolicy::class,
        CitizenLock::class => \App\Policies\Nova\CitizenLockPolicy::class,
        LoanHistory::class => \App\Policies\Nova\LoanUpdateHistoryPolicy::class,
        LoanDocumentHistory::class => \App\Policies\Nova\LoanDocumentHistoryPolicy::class,
        MarketingMedia::class => \App\Policies\Nova\MarketingMediaPolicy::class,
        StoryMedia::class => \App\Policies\Nova\StoryMediaPolicy::class,
        CashPayment::class => \App\Policies\Nova\WithdrawableLoanPolicy::class,
        Merchant::class => \App\Policies\Nova\MerchantPolicy::class,
        BlacklistReason::class => \App\Policies\Nova\BlacklistReasonPolicy::class,
        Transaction::class => \App\Policies\Nova\TransactionPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        Nova::serving(function (ServingNova $event) {
            $this->registerNovaPolicies();
        });
    }

    public function registerNovaPolicies()
    {
        foreach ($this->nova_policies as $key => $value) {
            Gate::policy($key, $value);
        }
    }
}
