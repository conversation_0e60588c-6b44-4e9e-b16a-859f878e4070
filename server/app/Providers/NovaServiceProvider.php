<?php

namespace App\Providers;

use App\Models\CreditLine\PurchaseRequest;
use App\Models\MerchantAgent;
use App\Models\Pallaton\Profile;
use App\Models\QrCode;
use App\Models\User;
use App\Nova\ArpiSolarLoan;
use App\Nova\Metrics\LoansPartition;
use App\Nova\Permission;
use App\Nova\Role;
use App\Observers\MerchantAgentObserver;
use App\Observers\ProfileObserver;
use App\Observers\PurchaseRequestObserver;
use App\Observers\QRCodeObserver;
use App\Observers\UserObserver;
use Globalcredit\AcraMonitoring\AcraMonitoring;
use Globalcredit\BankReport\BankReport;
use Globalcredit\DeviatedSchedules\DeviatedSchedules;
use Globalcredit\Home\Home;
use Globalcredit\PaymentInvoice\PaymentInvoice;
use Globalcredit\Settings\Settings;
use Illuminate\Support\Facades\Gate;
use Laravel\Nova\Events\ServingNova;
use Laravel\Nova\Nova;
use Laravel\Nova\NovaApplicationServiceProvider;

class NovaServiceProvider extends NovaApplicationServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Nova::serving(function (ServingNova $event) {
            $this->app->setLocale(env('NOVA_LOCALE'));

            if (
                $event->request->user()->isUpayAdmin() ||
                $event->request->user()->isTelcellAdmin() ||
                ($event->request->user()->hasPermissionTo('view-oasl-users') && !$event->request->user()->isAdmin())
            ) {
                User::observe(UserObserver::class);
            }

            QrCode::observe(QRCodeObserver::class);
            Profile::observe(ProfileObserver::class);
            PurchaseRequest::observe(PurchaseRequestObserver::class);
            MerchantAgent::observe(MerchantAgentObserver::class);
        });

        Nova::script('custom-js', public_path('/js/custom.js'));

        Nova::style('custom_styles', public_path('/css/admin_custom.css'));

        parent::boot();
    }

    /**
     * Register the Nova routes.
     *
     * @return void
     */
    protected function routes()
    {
        Nova::routes()
                ->withAuthenticationRoutes()
                ->withPasswordResetRoutes()
                ->register();
    }

    /**
     * Register the Nova gate.
     *
     * This gate determines who can access Nova in non-local environments.
     *
     * @return void
     */
    protected function gate()
    {
        Gate::define('viewNova', function ($user) {
            return $user->hasPermissionTo('view-nova', config('nova.guard'));
        });
    }

    /**
     * Get the cards that should be displayed on the Nova dashboard.
     *
     * @return array
     */
    protected function cards()
    {
        return [
            (new LoansPartition())
                ->canSee(function ($request) {
                    return $request->user()->isAdmin();
                }),

            (new Home())
                ->title(__('Unpaid Loans'))
                ->url(config('nova.path').'/resources/withdrawable-loans')
                ->type('paid-loan')
                ->canSee(function ($request) {
                    return $request->user()->can('view-cash')
                        && !$request->user()->isAdmin();
                }),
            (new Home())
                ->title(__('Paid Loans'))
                ->url(config('nova.path').'/resources/withdrawn-loans')
                ->type('unpaid-loan')
                ->canSee(function ($request) {
                    return $request->user()->can('view-loan-history')
                        && !$request->user()->isAdmin();
                }),
            (new Home())
                ->title(__('All Loans'))
                ->url(config('nova.path').'/resources/loans')
                ->type('paid-loan')
                ->canSee(function ($request) {
                    return $request->user()->isAdmin() || $request->user()->can('view-loans');
                }),

            (new Home())
                ->title(__('Vehicle Loans'))
                ->url(config('nova.path').'/resources/vehicle-loans')
                ->type('unpaid-loan')
                ->canSee(function ($request) {
                    return $request->user()->hasPermissionTo('view-ovl-loan');
                }),

            (new Home())
                ->title(__('Vehicle Import Loans'))
                ->url(config('nova.path').'/resources/vehicle-import-loans')
                ->type('unpaid-loan')
                ->canSee(function ($request) {
                    return $request->user()->hasPermissionTo('view-ovl-loan');
                }),

            (new Home())
                ->title(__('Credit Lines'))
                ->url(config('nova.path').'/resources/credit-lines')
                ->type('unpaid-loan')
                ->canSee(function ($request) {
                    return $request->user()->isAdmin();
                }),

            (new Home())
                ->title(__('Users'))
                ->url(config('nova.path').'/resources/users')
                ->type('users')
                ->canSee(function ($request) {
                    return $request->user()->isAdmin()
                        || $request->user()->isUpayAdmin()
                        || $request->user()->isTelcellAdmin()
                        || $request->user()->hasPermissionTo('view-oasl-users');
                }),
            (new Home())
                ->title(__('Solar Loans'))
                ->url(config('nova.path').'/resources/'.ArpiSolarLoan::uriKey())
                ->type('paid-loan')
                ->canSee(function ($request) {
                    return $request->user()->can('view-oasl-loan');
                }),
            (new Home())
            ->title(__('StabilizationLoan'))
            ->url(config('nova.path').'/resources/stabilization-loans')
            ->type('unpaid-loan')
            ->canSee(function ($request) {
                return $request->user()->can('stabilization-loan');
            }),
            (new Home())
                ->title(__('Mortgage Loan Applications'))
                ->url(config('nova.path').'/resources/mortgage-loan-applications')
                ->canSee(function ($request) {
                    return $request->user()->can('view-reml-loan-applications');
                }),
            (new Home())
                ->title(__('Mortgage Loans'))
                ->url(config('nova.path').'/resources/mortgage-loans')
                ->canSee(function ($request) {
                    return $request->user()->can('view-reml-loan-applications');
                }),
        ];
    }

    /**
     * Get the tools that should be listed in the Nova sidebar.
     *
     * @return array
     */
    public function tools()
    {
        return [
            \Vyuldashev\NovaPermission\NovaPermissionTool::make()
                ->roleResource(Role::class)
                ->permissionResource(Permission::class)
                ->canSee(function () {
                    return auth()->user()->hasRole('admin');
                }),
            (new DeviatedSchedules())->canSee(function ($request) {
                return $request->user()->hasPermissionTo('correct-devated-schedule', config('nova.guard'));
            }),
            (new BankReport())->canSee(function ($request) {
                return $request->user()->hasPermissionTo('bank-report');
            }),
            (new AcraMonitoring())->canSee(function () {
                return auth()->user()->hasPermissionTo('acra-monitoring');
            }),
            (new Settings())->canSee(function () {
                return auth()->user()->hasRole('admin') || auth()->user()->hasRole('gc-customer-admin');
            }),
            (new PaymentInvoice())->canSee(function ($request) {
                return $request->user()->hasPermissionTo('view-payment-invoice');
            }),
        ];
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
    }
}
