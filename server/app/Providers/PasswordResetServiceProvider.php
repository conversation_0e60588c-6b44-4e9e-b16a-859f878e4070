<?php

namespace App\Providers;

use App\Auth\Passwords\PasswordBrokerManager;
use App\Helpers\RequestHelper;
use Illuminate\Auth\Passwords\PasswordBrokerManager as BasePasswordBrokerManager;
use Illuminate\Auth\Passwords\PasswordResetServiceProvider as BasePasswordResetServiceProvider;

class PasswordResetServiceProvider extends BasePasswordResetServiceProvider
{
    /**
     * Register the password broker instance.
     *
     * @return void
     */
    protected function registerPasswordBroker()
    {
        $this->app->singleton('auth.password', function ($app) {
            if (RequestHelper::isNovaRequst(request())) {
                return new BasePasswordBrokerManager($app);
            } else {
                return new PasswordBrokerManager($app);
            }
        });

        $this->app->bind('auth.password.broker', function ($app) {
            return $app->make('auth.password')->broker();
        });
    }
}
