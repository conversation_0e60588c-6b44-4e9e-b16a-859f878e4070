<?php

namespace App\Providers;

class AuthenticationServiceProvider extends ServiceResolveProvider
{
    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
    }

    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind('App\Interfaces\IAuthService', function ($app) {
            $loan_type_id = $this->getLoanType($app->request);

            if ($loan_type_id == constants('LOAN_TYPES.OIQL')) {
                return new \App\Services\AuthServiceOIQL();
            } elseif ($loan_type_id == constants('LOAN_TYPES.OASL')) {
                return new \App\Services\AuthServiceOASL();
            } elseif ($loan_type_id == constants('LOAN_TYPES.PL')) {
                return new \App\Services\AuthServicePL();
            } else {
                return new \App\Services\AuthServiceCommon();
            }
        });
    }
}
