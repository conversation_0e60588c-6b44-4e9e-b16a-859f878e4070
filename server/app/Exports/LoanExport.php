<?php

namespace App\Exports;

use App\Factory\HcServiceFactory;
use App\Helpers\DateHelper;
use App\Models\Package;
use App\Services\HcServiceOASL;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithTitle;
use Carbon\Carbon;

class LoanExport implements FromView, WithTitle
{
    /** @var Package */
    protected $package;
    protected $loan;
    protected $count;

    public function __construct($loan, $count, $package = null)
    {
        $this->package = $package;
        $this->loan = $loan;
        $this->count = $count;
    }

    public function view(): View
    {
        return view('exports.loan', $this->composeExportData($this->loan));
    }

    public function title(): string
    {
        return 'Loan '.$this->count;
    }

    private function composeExportData($loan)
    {
        return array_merge(
            $loan->toArray(),
            $this->composeLoanInfo($loan),
            $this->composeMortgageInfo($loan)
        );
    }

    private function composeLoanInfo($loan)
    {
        $meta = $loan->loan_security->security_metas()->where('step', 'CONTEXT_INFO')->first();

        $decoded_content = json_decode($meta->content, true);

        $warehouse_service = resolve('App\Services\WarehouseService');
        $loan_schedule = $warehouse_service->getSchedule($loan->contract_number, Carbon::now()->format(constants('SCHEDULE_DATE_FORMAT')));

        /** @var HcServiceOASL $hc_service */
        $hc_service = HcServiceFactory::build(constants('LOAN_TYPES.OASL'));

        $loan_schedule = array_map(function ($schedule) {
            return array_merge($schedule, [
                'fdate' => DateHelper::parseDate($schedule['fdate'])
            ]);
        }, $loan_schedule);

        $loan_last_payment = end($loan_schedule);

        return array_merge($loan->citizen->toArray(), [
            'credit_code' => $hc_service->getCreditRegisterCodeNew($loan->contract_number),
            'identificator' => $this->generateIdentificator($loan),
            'balance' => $hc_service->getBalance($loan->contract_number),
            'sign_date' => DateHelper::parseDate($loan->sign_date),
            'payment_date' => DateHelper::parseDate($loan->payment->withdrawn),
            'interest_rate_type' => constants('OASL_XLSX_EXPORT_DATA.INTEREST_RATE_TYPE'),
            'credit_purpose' => constants('OASL_XLSX_EXPORT_DATA.CREDIT_PURPOSE'),
            'credit_type' => constants('OASL_XLSX_EXPORT_DATA.CREDIT_TYPE'),
            'loan_officer' => constants('OASL_XLSX_EXPORT_DATA.LOAN_OFFICER'),
            'amount_rate' => constants('OASL_XLSX_EXPORT_DATA.AMOUNT_RATE'),
            'last_payment_date' => $loan_last_payment['fdate'],
            'pti' => constants('OASL_XLSX_EXPORT_DATA.PTI'),
            'oti' => constants('OASL_XLSX_EXPORT_DATA.OTI'),
            'participants' => constants('OASL_XLSX_EXPORT_DATA.PARTICIPANTS'),
            'passport_number' => $loan->loan_security->document_number,
            'social_card_number' => $loan->loan_security->ssn,
            'gender' => constants('OASL_XLSX_EXPORT_DATA.GENDER')[$loan->citizen->gender],
            'birth_date' => DateHelper::parseDate($loan->citizen->birth_date),
            'delay_quantity' => $decoded_content['delayQuantityInYear'] ?? null,
            'loan_class' => $decoded_content['loanClass'] ?? null,
            'loan_schedule' => $loan_schedule,
            'education' => constants('OASL_XLSX_EXPORT_DATA.EDUCATION'),
        ]);
    }

    protected function generateIdentificator($loan)
    {
        $package_id = '{PACKAGE_ID}';

        if (!empty($this->package)) {
            $package_id = $this->package->package_number;
        }

        return constants('OASL_XLSX_EXPORT_DATA.IDENTIFICATOR_PREFIX_1').
        $package_id.
        constants('OASL_XLSX_EXPORT_DATA.IDENTIFICATOR_PREFIX_2').
        sprintf('%03d', $this->count);
    }

    private function composeMortgageInfo($loan)
    {
        $solar_panel = $loan->solar_panel;

        return array_merge($solar_panel->evaluation_report->toArray(), [
            'installation_country' => constants('OASL_XLSX_EXPORT_DATA.COUNTRY'),
            'installation_region' => $solar_panel->region ? $solar_panel->region->name : '',
            'installation_village' => $solar_panel->village ? $solar_panel->village->name : '',
            'installation_address' => $solar_panel->address,
            'building_type' => constants('OASL_XLSX_EXPORT_DATA.BUILDING_TYPE'),
            'phone_number' => $loan->citizen->phone_number,
            'evaluation_date' => DateHelper::parseDate($solar_panel->evaluation_report->evaluation_date),
            'estimated_cost' => constants('OASL_XLSX_EXPORT_DATA.ESTIMATED_COST'),
        ]);
    }
}
