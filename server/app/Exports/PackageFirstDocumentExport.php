<?php

namespace App\Exports;

use App\Factory\HcServiceFactory;
use App\Helpers\DateHelper;
use App\Models\Citizen;
use App\Models\EvaluationReport;
use App\Models\Loan;
use App\Models\Package;
use App\Models\SolarPanel;
use App\Services\HcServiceOASL;
use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\DefaultValueBinder;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;

class PackageFirstDocumentExport extends DefaultValueBinder implements FromView, WithTitle, WithEvents
{
    protected $package;
    protected $loans_count = 0;

    public function __construct(Package $package)
    {
        $this->package = $package;
    }

    public function view(): View
    {
        return view('exports.package_doc_1', $this->generateData());
    }

    protected function generateData()
    {
        $excel_data = [];
        $loans_count = 0;
        $sum_amount = 0;
        $sum_balance = 0;
        $sum_bonus = 0;

        /** @var HcServiceOASL $hcService */
        $hc_service = HcServiceFactory::build(constants('LOAN_TYPES.OASL'));

        /** @var Loan $arpi_solar_loan */
        foreach ($this->package->arpi_solar_loans as $arpi_solar_loan) {
            /** @var SolarPanel $solar_panel */
            /** @var EvaluationReport $evaluation_report */
            if (!empty($solar_panel = $arpi_solar_loan->solar_panel) &&
                !empty($evaluation_report = $solar_panel->evaluation_report)) {
                $balance = $hc_service->getBalance($arpi_solar_loan->contract_number);
                $sum_amount += $arpi_solar_loan->amount;
                $sum_balance += $balance;
                $sum_bonus += $evaluation_report->bonus_amount;

                /** @var Citizen $citizen */
                $citizen = $arpi_solar_loan->citizen;

                ++$loans_count;

                $row_number = __('Package Excel Loan Number Prefix 1').
                    $this->package->package_number.
                    __('Package Excel Loan Number Prefix 2').
                    $this->paddedNumber($loans_count);

                $excel_data[] = [
                    $loans_count,
                    $row_number,
                    $citizen->last_name.' '.$citizen->first_name.' '.$citizen->middle_name,
                    __('Package Excel Loan Type Arpi Solar'),
                    $arpi_solar_loan->amount,
                    $balance,
                    $evaluation_report->bonus_amount,
                    __('Package Excel File 1 Label Yes'),
                    $evaluation_report->evaluation_id,
                ];
            }
        }

        $this->loans_count = $loans_count;

        $package_number = $this->paddedNumber($this->package->package_number);
        $main_heading = __('Package Excel File 1 Heading Main').' '.$package_number;

        return [
            'rows' => $excel_data,
            'sum_amount' => $sum_amount,
            'sum_balance' => $sum_balance,
            'sum_bonus' => $sum_bonus,
            'main_heading' => $main_heading,
            'date' => DateHelper::composeLocalizedDate(Carbon::now()),
        ];
    }

    protected function paddedNumber($number)
    {
        return str_pad($number, 3, '0', STR_PAD_LEFT);
    }

    public function title(): string
    {
        return __('Package Excel Sheet Name 1');
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $sheet = $event->sheet;
                $heading_rows_count = 3;
                $starting_row = $heading_rows_count + 1;
                $last_row = $this->loans_count + $starting_row + 1;
                $bordered_cells = 'A'.$starting_row.':I'.$last_row;
                $loans_starting_row = $starting_row + 1;
                $loans_last_row = $last_row - 1;

                // Setting borders for each cell.
                $columns = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I'];

                foreach ($columns as $column) {
                    for ($row = $starting_row; $row <= $last_row; ++$row) {
                        $cell = $column.$row;
                        $sheet->styleCells(
                            $cell,
                            [
                                'borders' => [
                                    'outline' => [
                                        'borderStyle' => Border::BORDER_THIN,
                                        'color' => ['argb' => '00000000'],
                                    ],
                                ],
                            ]
                        );
                    }
                }

                $sheet->verticalAlign($bordered_cells, Alignment::VERTICAL_CENTER);

                for ($row = $loans_starting_row; $row <= $loans_last_row; ++$row) {
                    $sheet->rowHeight($row, 50);
                }
            },
        ];
    }
}
