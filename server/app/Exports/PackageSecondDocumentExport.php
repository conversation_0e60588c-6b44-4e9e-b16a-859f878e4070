<?php

namespace App\Exports;

use App\Factory\HcServiceFactory;
use App\Helpers\DateHelper;
use App\Models\Citizen;
use App\Models\EvaluationReport;
use App\Models\Loan;
use App\Models\Package;
use App\Models\SolarPanel;
use App\Services\HcServiceOASL;
use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\DefaultValueBinder;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;

class PackageSecondDocumentExport extends DefaultValueBinder implements FromView, WithTitle, WithEvents
{
    protected $package;
    protected $loans_count = 0;

    public function __construct(Package $package)
    {
        $this->package = $package;
    }

    public function view(): View
    {
        return view('exports.package_doc_2', $this->generateData());
    }

    protected function generateData()
    {
        $excel_data = [];
        $loans_count = 0;
        $sum_amount = 0;
        $sum_balance = 0;

        /** @var HcServiceOASL $hcService */
        $hc_service = HcServiceFactory::build(constants('LOAN_TYPES.OASL'));

        /** @var Loan $arpi_solar_loan */
        foreach ($this->package->arpi_solar_loans as $arpi_solar_loan) {
            /** @var SolarPanel $solar_panel */
            /** @var EvaluationReport $evaluation_report */
            if (!empty($solar_panel = $arpi_solar_loan->solar_panel) &&
                !empty($evaluation_report = $solar_panel->evaluation_report)) {
                $balance = $hc_service->getBalance($arpi_solar_loan->contract_number);
                $sum_amount += $arpi_solar_loan->amount;
                $sum_balance += $balance;
                ++$loans_count;

                /** @var Citizen $citizen */
                $citizen = $arpi_solar_loan->citizen;
                $loan_last_payment = $arpi_solar_loan
                    ->loan_schedule
                    ->sortByDesc('date')
                    ->first();

                $citizen_name = $this->makeCapitalCase($citizen->last_name ?? '').' '.
                    $this->makeCapitalCase($citizen->first_name ?? '').' '.
                    $this->makeCapitalCase($citizen->middle_name ?? '');

                $excel_data[] = [
                    $citizen_name,
                    $hc_service->getCreditRegisterCodeNew($arpi_solar_loan->contract_number),
                    $solar_panel->full_address,
                    number_format($arpi_solar_loan->amount, 1, '.', ','),
                    $arpi_solar_loan->sign_date->format('d/m/y'),
                    '',
                    $loan_last_payment->date->format('d/m/y'),
                    number_format($arpi_solar_loan->interest_rate, 1, '.', null).'%',
                    $balance,
                ];
            }
        }

        $this->loans_count = $loans_count;

        $package_number = $this->paddedNumber($this->package->package_number);
        $main_heading = __('Package Excel File 2 Heading Main').' '.__('Package Excel File 2 Heading Prefix 1', ['package_number' => $package_number]);

        $formatted_sum_amount_1 = number_format($sum_balance, 0, null, ',');
        $formatted_sum_amount_2 = number_format($sum_amount, 2, null, ',');

        return [
            'rows' => $excel_data,
            'main_heading' => $main_heading,
            'sum_balance' => $sum_balance,
            'date' => DateHelper::composeLocalizedDate(Carbon::now()),
            'loans_count_text' => __('Package Excel File 2 Loans Count', ['count' => $loans_count]),
            'loans_amount_sum_text' => __('Package Excel File 2 Loans Amount Sum', ['sum' => $formatted_sum_amount_1]),
            'loans_amount_sum_formatted' => $formatted_sum_amount_2,
        ];
    }

    protected function paddedNumber($number)
    {
        return str_pad($number, 3, '0', STR_PAD_LEFT);
    }

    protected function makeCapitalCase(string $data)
    {
        return mb_convert_case(mb_strtolower($data), MB_CASE_TITLE);
    }

    public function title(): string
    {
        return __('Package Excel Sheet Name 2');
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $sheet = $event->sheet;
                $heading_rows_count = 10;
                $starting_row = $heading_rows_count + 1;
                $last_row = $this->loans_count + $starting_row + 1;
                $bordered_cells = 'A'.$starting_row.':K'.$last_row;
                $loans_starting_row = $starting_row + 1;
                $loans_last_row = $last_row - 1;

                // Setting borders for each cell.
                $columns = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K'];

                $sheet->getDelegate()->getColumnDimension($columns[0])->setWidth(5);

                foreach ($columns as $column) {
                    for ($row = $starting_row; $row <= $last_row; ++$row) {
                        $cell = $column.$row;
                        $sheet->styleCells(
                            $cell,
                            [
                                'borders' => [
                                    'outline' => [
                                        'borderStyle' => Border::BORDER_THIN,
                                        'color' => ['argb' => '00000000'],
                                    ],
                                ],
                            ]
                        );
                    }
                }

                $sheet->verticalAlign($bordered_cells, Alignment::VERTICAL_CENTER);

                for ($row = $loans_starting_row; $row <= $loans_last_row; ++$row) {
                    $sheet->rowHeight($row, 50);
                }
            },
        ];
    }
}
