<?php

namespace App\RuleEngine\Phases;

use App\Calculator\LoanCalculatorOWL;
use Log;

class DSTIPhaseOwl extends DSTIPhase
{
    public function __construct()
    {
        parent::__construct(RulePhases::COMMON['DSTI']);
    }

    protected function getValidCredit($credit, $dsti_repayment, $income)
    {
        if (empty($credit) || $credit['amount'] <= 0) {
            return [];
        }

        $configs = $this->getConfigs();

        $calculator = new LoanCalculatorOWL($credit['amount'], $credit['rate'], $configs['service_fee_rate'], [
            'max_duration' => $credit['duration'],
        ]);
        $monthly = $calculator->generateSchedule()[0]['payment'] ?? 0;

        Log::info('DSTI Phase Owl', ['payment' => $monthly]);

        if ($this->validateDsti($dsti_repayment, $monthly, $income)) {
            return $credit;
        }

        return [];
    }

    protected function getConfigs()
    {
        $loanConfigService = resolve('App\Services\LoanConfigService');

        return $loanConfigService->getConfigs(constants('LOAN_TYPES.OIDL'));
    }
}
