<?php

namespace App\RuleEngine\Phases\Factory;

use App\RuleEngine\Phases\AcraActiveLoansPhaseVlx;
use App\RuleEngine\Phases\AmountCalculationPhase;
use App\RuleEngine\Phases\DSTIPhaseVlx;
use App\RuleEngine\Phases\FinalAmountCalculationPhaseVlx;
use App\RuleEngine\Phases\OSMPhaseVlx;
use App\RuleEngine\Phases\ProductCategoryRatePhaseVlx;
use App\RuleEngine\Phases\RulePhases;

class VlxPhaseFactory
{
    public static function build($phase)
    {
        if ($phase === RulePhases::VLX['AMOUNT_CALCULATION']) {
            return new AmountCalculationPhase($phase);
        }
        if ($phase === RulePhases::VLX['OSM']) {
            return new OSMPhaseVlx();
        }
        if ($phase === RulePhases::VLX['DSTI']) {
            return new DSTIPhaseVlx();
        }
        if ($phase === RulePhases::VLX['ACRA_ACTIVE_LOANS']) {
            return new AcraActiveLoansPhaseVlx();
        }
        if ($phase === RulePhases::VLX['PRODUCT_CATEGORY_RATE']) {
            return new ProductCategoryRatePhaseVlx();
        }
        if ($phase === RulePhases::VLX['FINAL_AMOUNT_CALCULATION']) {
            return new FinalAmountCalculationPhaseVlx();
        }
    }
}
