<?php

namespace App\RuleEngine\Phases\Factory;

use App\RuleEngine\Phases\AmountCalculationPhase;
use App\RuleEngine\Phases\DSTIPhaseOasl;
use App\RuleEngine\Phases\RulePhases;

class OaslPhaseFactory
{
    public static function build($phase)
    {
        if ($phase === RulePhases::OASL['AMOUNT_CALCULATION']) {
            return new AmountCalculationPhase($phase);
        }
        if ($phase === RulePhases::OASL['DSTI']) {
            return new DSTIPhaseOasl();
        }
    }
}
