<?php

namespace App\RuleEngine\Phases;

use App\Calculator\LoanCalculatorBNPL;
use Log;

class DSTIPhaseBNPL extends DSTIPhase
{
    public function __construct()
    {
        parent::__construct(RulePhases::BNPL['DSTI']);
    }

    public function applyPhase($payload)
    {
        $result = $payload->getResult();

        if ($result['credit']['amount'] < constants('AMOUNT_LIMITS.BNPL')) {
            return true;
        }

        Log::info('DSTI Phase BNPL skipped', ['credit' => $result['credit']]);

        return false;
    }

    protected function getValidCredit($credit, $dsti_repayment, $income)
    {
        $configs = $this->getConfigs();
        $credit = $this->getValidCreditRecursive($dsti_repayment, $income, $configs, $credit);

        Log::info('DSTI Phase get valid credit', ['phase' => $this->phase, 'credit' => $credit]);

        return $credit;
    }

    protected function getValidCreditRecursive($dsti_repayment, $income, $configs, &$credit)
    {
        if (empty($credit) || $credit['amount'] <= 0 || $credit['amount'] < $configs['min_amount']) {
            return [];
        }

        $calculator = new LoanCalculatorBNPL($credit['amount'], $credit['rate'], $configs['service_fee_rate'], [
            'max_duration' => $configs['duration'],
        ]);

        $monthly_payment = $calculator->generateSchedule()[0]['payment'];

        Log::info('DSTI Phase BNPL', ['amount' => $credit['amount'], 'payment' => $monthly_payment]);

        if ($this->validateDsti($dsti_repayment, $monthly_payment, $income)) {
            return $credit;
        }

        $credit['amount'] = $credit['amount'] - $configs['amount_step'];

        return $this->getValidCreditRecursive($dsti_repayment, $income, $configs, $credit);
    }

    protected function getConfigs()
    {
        $loan_config_service = resolve('App\Services\LoanConfigService');

        return $loan_config_service->getConfigs(constants('LOAN_TYPES.BNPL'));
    }
}
