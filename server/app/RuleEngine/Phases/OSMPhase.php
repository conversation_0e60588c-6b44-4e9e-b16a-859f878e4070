<?php

namespace App\RuleEngine\Phases;

use App\Calculator\LoanCalculator;
use Log;

abstract class OSMPhase extends AbstractPhase
{
    protected $MONTHLY_REPAYMENT_LIMIT;

    abstract protected function getConfigs();

    public function __construct($phase)
    {
        $this->phase = $phase;
    }

    public function __invoke(PhasePayload $payload)
    {
        if (!$this->applyPhase($payload)) {
            return $payload;
        }

        Log::info('OSM Phase', ['phase' => $this->phase]);

        $rules = $payload->getRules();
        $context = $payload->getContext();

        $result = $payload->getResult();

        // do not continue phase execution if already rejected by another phase
        if ($result['credit']['amount'] == 0) {
            Log::debug('Skipped OSM phase because of another phase rejection');

            return $payload;
        }

        $osm_credit = $this->getValidCredit($result['credit'], $context['osm']);

        $prepared_context = $this->prepareContext($context, ['osmAmount' => $osm_credit['amount'] ?? 0]);

        $phase_result = $this->execute($rules[$this->phase], $prepared_context);

        $prepared_result = $this->prepareResult($phase_result);
        $payload->addResult($prepared_result);

        return $payload;
    }

    protected function getValidCredit($credit, $osm)
    {
        $configs = $this->getConfigs();

        $credit = $this->getValidCreditRecursive($osm, $credit, $configs);

        Log::info('OSM Phase: Valid credit calculated', ['phase' => $this->phase, 'credit' => $credit]);

        return $credit;
    }

    protected function getValidCreditRecursive($osm, &$credit, $configs)
    {
        // If credit amount becomes 0 or negative we must reject
        // Also reject if it's less than min allowed amount for this loan type
        if (empty($credit) || $credit['amount'] <= 0 || $credit['amount'] < $configs['min_amount']) {
            return [];
        }

        // Calculating monthly payment min value for given amount to compare it with OSM
        $calculator = new LoanCalculator($credit['amount'], $credit['rate'], $this->getServiceFeeRate($configs, $credit), [
            'max_duration' => $credit['duration'],
        ]);

        [$monthly_min_payment] = $calculator->monthlyMinMax($configs['min_months']);

        $allowed_max = $osm - $this->MONTHLY_REPAYMENT_LIMIT;
        // If this value is 0 or negative that means user can't get loan, so we must reject
        if ($allowed_max <= 0) {
            return [];
        }

        // If allowed max value counted by OSM is equal or greater than monthly payment min value
        // that means user can get loan with given amount
        if ($monthly_min_payment <= $allowed_max) {
            return $credit;
        }

        // Reducing credit amount by amount step value and repeat this function
        $credit['amount'] = $credit['amount'] - $configs['amount_step'];

        return $this->getValidCreditRecursive($osm, $credit, $configs);
    }

    protected function getServiceFeeRate($configs, $credit)
    {
        return $credit['service_fee_rate'] ?? $configs['service_fee_rate'];
    }
}
