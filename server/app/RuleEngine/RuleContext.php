<?php

namespace App\RuleEngine;

use App\Schemas\ContextSchema;

class RuleContext implements IRuleContext
{
    private $context = null;

    public function __construct($data)
    {
        $this->context = $data;
    }

    public function getContext()
    {
        return $this->context;
    }

    public static function getContextSchema()
    {
        $contextSchema = new ContextSchema();

        return $contextSchema->get();
    }
}
