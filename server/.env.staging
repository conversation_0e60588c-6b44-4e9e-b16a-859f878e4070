APP_NAME=Laravel
APP_ENV=staging
APP_KEY=base64:cw9XcFCERG6M/PElAWRe8ZG+GiUD+bropF900lS+N1w=
APP_DEBUG=false
APP_URL=http://***********

MOBILE_APP_VERSION=4.2.0

LOG_CHANNEL=daily
LOG_SLACK_WEBHOOK_URL=*****************************************************************************
LOG_SLACK_LIVENESS_WEBHOOK_URL=

DB_CONNECTION=pgsql
DB_HOST=postgres
DB_PORT=5432
DB_DATABASE=default
DB_USERNAME=default
DB_PASSWORD=secret

DB_HOST_COVID19=**************
DB_PORT_COVID19=5432
DB_DATABASE_COVID19=aobyte
DB_USERNAME_COVID19=aobyte
DB_PASSWORD_COVID19=secret

DB_HOST_SUPPORT_LOAN=postgres
DB_PORT_SUPPORT_LOAN=5432
DB_DATABASE_SUPPORT_LOAN=default
DB_USERNAME_SUPPORT_LOAN=default
DB_PASSWORD_SUPPORT_LOAN=secret

DB_HOST_REPLICA=**************
DB_PORT_REPLICA=5432
DB_DATABASE_REPLICA=aobyte
DB_USERNAME_REPLICA=aobyte
DB_PASSWORD_REPLICA=secret

SIMULATOR_REDIS_HOST=**************
SIMULATOR_REDIS_PASSWORD=null
SIMULATOR_REDIS_PORT=6379

SIMULATION_URL=http://**************/

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
SESSION_DRIVER=file
SESSION_LIFETIME=15
QUEUE_DRIVER=database
SMS_QUEUE=sms

REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_DRIVER=smtp
MAIL_HOST=smtp.mail.ru
MAIL_PORT=465
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=Testing.2019
MAIL_ENCRYPTION=ssl
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=GlobalCredit
MAIL_BCC=<EMAIL>

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

API_PREFIX=api
API_SUBTYPE=app
API_VERSION=v1

SIGN_UP_RELEASE_TOKEN=false
PASSWORD_RESET_RELEASE_TOKEN=true

JWT_SECRET=my-dummy-token

QUEUE_HOST=beanstalkd

EKENG_URL=https://ssn-api.ekeng.am
EKENG_DECRYPT=aes256
EKENG_TOKEN=a148a3c4-6b39-32f8-b757-a1d983ca4097
EKENG_SECRET=481946f259fd35e7b484a32e4ac42a7f
EKENG_IV=O9fGelU066lJf7tiIjTw7w==

POLICE_URL=https://eth.ekeng.am/

ACRA_MONITORING_URL=https://*************/acra/soap/?mon=1
ACRA_URL=https://*************/acra/soap/
ACRA_USER=Test_06_GLC
ACRA_PASSWORD=qwQW12!@

NORK_URL=http://*********:9232/NorqDXChangeService?wsdl
NORK_LOGIN=globalcredit_service
NORK_PASSWORD=nc9PDCf9vL

OPAQUE_LENGTH=26

SMS_HTTP_SERVICE_URL=http://************:80/broker/
SMS_HTTP_SERVICE_LOGIN=globalcreditmt
SMS_HTTP_SERVICE_PASSWORD=globalcreditmt
SMS_HTTP_SERVICE_NUMBER=GLB.CREDIT
SMS_HTTP_SERVICE_VALIDITY_PERIOD=3

SMS_SMPP_SERVICE_HOST=************
SMS_SMPP_SERVICE_PORT=2775
SMS_SMPP_SERVICE_LOGIN=globcrd
SMS_SMPP_SERVICE_PASSWORD=glb33crd
SMS_SMPP_SERVICE_SENDER_ID=GLB.CREDIT

FTP_IQOS_HOST=distrimex.ru
FTP_IQOS_PORT=21
FTP_IQOS_USERNAME=<EMAIL>
FTP_IQOS_PASSWORD=%TGB5tgb

IDRAM_TRANSFER_URL=https://money.idram.am
IDRAM_SESSION_ID=b5695d28-0327-4960-9039-47981eab217f
IDRAM_PIN=265565
IDRAM_ID=946277810
IDRAM_SERVICE_ID=26

IDRAM_LOAN_URL=https://dev.idt.am/api/Loan/
IDRAM_LOAN_SESSION_ID=3dd2b2d7-fd39-4d57-9441-afca1d14ef73
IDRAM_LOAN_API_PIN=757305

TELCELL_LOAN_URL=https://telcellmoney.am/globalcredit/do
TELCELL_SALT=v8D6jGgro93=Kns27J2dl2x*dH2l92sai631Klsnf93k8skL2sfnsTd12n
TELCELL_LOAN_API_SIGN=

EASYPAY_LOAN_URL=https://*********:17737/PaymentApi.svc
EASYPAY_LOAN_USERNAME=UaGlcredit12
EASYPAY_LOAN_PASSWORD=globalcr96@!#
EASYPAY_LOAN_TOKEN=0F79292A71A4C7487F7A8227646FD0CE958484B321CC7020A744D078416726289C19E6B2
EASYPAY_LOAN_PROVIDER_ID=14204
EASYPAY_LOAN_CURRENCY_ISO=AMD
EASYPAY_LOAN_COMMISSION=0

UPAY_LOAN_URL=https://upay-mobile-api.helix.am/api/en
UPAY_LOAN_PATH=/api/en/cashme
UPAY_LOAN_USERNAME=
UPAY_LOAN_PASSWORD=
UPAY_LOAN_SECRET_KEY=D4yCeOKT9VpBZpbsTgGtAZbKEL9uXAY8gBVAaHC2BO5fdk3JoEHDjbIcecFiH8kv
UPAY_LOAN_TOKEN=
UPAY_LOAN_PROVIDER_ID=
UPAY_LOAN_CURRENCY_ISO=AMD
UPAY_LOAN_COMMISSION=0
UPAY_LOAN_BANK_ACCOUNT=***********

WAREHOUSE_MS_SQL_HOST=**************\GCASB
WAREHOUSE_MS_SQL_PORT=52201
WAREHOUSE_MS_SQL_DATABASE=proc
WAREHOUSE_MS_SQL_USERNAME=proc_user
WAREHOUSE_MS_SQL_PASSWORD=Q@w3e4r5

##### DW CIRC SERVER #####
DW_MS_SQL_HOST=**************\GCWH
DW_MS_SQL_PORT=49767
DW_MS_SQL_DATABASE=circ
DW_MS_SQL_USERNAME=crm_user
DW_MS_SQL_PASSWORD=T8CgBhHdG9Fd2PDd

HC_MS_SQL_HOST=**************\GCASB
HC_MS_SQL_PORT=49661
HC_MS_SQL_DATABASE=appbuffer
HC_MS_SQL_USERNAME=buffer
HC_MS_SQL_PASSWORD=Q@w3e4r5

HC_URL_DOMAIN=http://*************
HC_URL_PATH=MobidramGateway/ASBankIntegrationService.svc
HC_PARTNER_ID=63599
HC_PARTNER_SK='Nja>ZVT8E*rd=)-c'
HC_OUR_SK='VGrX6<FF!:zyG84t'

HC_PAYMENT_ARARAT_BANK_PARTNER_ID=15199
HC_PAYMENT_ARARAT_BANK_PARTNER_SK=QDky2F3Fvjaq9axP
HC_PAYMENT_ARARAT_BANK_OUR_SK=tdCkEvJnyxg88VZt

HC_PAYMENT_EVOCA_BANK_PARTNER_ID=63597
HC_PAYMENT_EVOCA_BANK_PARTNER_SK=7c8heJ9o0mpNubU8Qg3A
HC_PAYMENT_EVOCA_BANK_OUR_SK=DWGONpFL9C42H7iwwcou

HC_ACBA_BANK_PARTNER_ID=22000
HC_ACBA_BANK_PARTNER_SK=K=GzN%gJ/W@{\dn.Hh3r
HC_ACBA_BANK_OUR_SK =x8<<:3PH*A>yx4g#R\&<

HC_AMERIA_BANK_PARTNER_ID=15700
HC_AMERIA_BANK_PARTNER_SK=LwAVf<G7x9=7#Wy!W9E^
HC_AMERIA_BANK_OUR_SK =RJkS?#tjd>^Q!5M8%]8$

HC_ID_BANK_PARTNER_ID=11800
HC_ID_BANK_PARTNER_SK=aS2=9}*%>h`?9RuAJ/q,
HC_ID_BANK_OUR_SK =Cm5-zQc@dHbY48E+;h6@

HC_ARARAT_BANK_PARTNER_ID=15100
HC_ARARAT_BANK_PARTNER_SK=HJdGYdj2nFSpbc76
HC_ARARAT_BANK_OUR_SK =NguNNdAo0DduvM87

HC_ARDSHIN_BANK_PARTNER_ID=24700
HC_ARDSHIN_BANK_PARTNER_SK=WE4t#x2+V)fR'(]U:w:~
HC_ARDSHIN_BANK_OUR_SK =s,zu6X?F,jtP%V4:\.G7

HC_ARMSWISS_BANK_PARTNER_ID=25000
HC_ARMSWISS_BANK_PARTNER_SK=p=6bv[e:]JJWh;g$$/c'
HC_ARMSWISS_BANK_OUR_SK =g_%W+9C<Y(.MyjraZ6p}

HC_ARTSAKH_BANK_PARTNER_ID=22300
HC_ARTSAKH_BANK_PARTNER_SK=v/8JSW*sa-%t9~"ZWn&w
HC_ARTSAKH_BANK_OUR_SK =EuBz;2<SpRA6?DTSVq`V

HC_BYBLOS_BANK_PARTNER_ID=21400
HC_BYBLOS_BANK_PARTNER_SK=qddxGE2zP::<`_AZH{:.
HC_BYBLOS_BANK_OUR_SK =C~>U+P}LGfdWC@>3[c8<

HC_EVOCA_BANK_PARTNER_ID=16600
HC_EVOCA_BANK_PARTNER_SK=Fx*!>PD(?Y3'>f?V4L@@
HC_EVOCA_BANK_OUR_SK =y3e7b+$r((-K;qLG9hFX

HC_CONVERSE_BANK_PARTNER_ID=19300
HC_CONVERSE_BANK_PARTNER_SK=%3!acayF?%YV\!Et9wdm
HC_CONVERSE_BANK_OUR_SK =<EMAIL>\+5)EZkS.D4^g

HC_ARMBUSINESS_BANK_PARTNER_ID=11500
HC_ARMBUSINESS_BANK_PARTNER_SK=T]*WF=Bw,N\ZZ6}%!?Cq
HC_ARMBUSINESS_BANK_OUR_SK =F6{>,r]Df'GY^_:kA*EL

HC_ARMECONOM_BANK_PARTNER_ID=16300
HC_ARMECONOM_BANK_PARTNER_SK=sZ_-#!Y"M'g,Y)Bv{m2=
HC_ARMECONOM_BANK_OUR_SK =NMn/f2>cj8v9mj}^.4KU

HC_VTB_BANK_PARTNER_ID=16000
HC_VTB_BANK_PARTNER_SK=zseH5yf#/vaCrCG6\M&/
HC_VTB_BANK_OUR_SK =q<Q!p](Agr9]5fNjY$P3

ARCA_URL=https://arusapi.aeb.am:4444/ArcaOnlineServiceExternalAsmx.asmx?wsdl
ARCA_CERT_PASSWORD=1234

ARCA_CARD_NUMBER=****************
ARCA_CERTIFICATE_PATH=certificates/armeconom/arca_certificate.pem

IDRAM_BANK_ACCOUNT=***********
IDRAM_LOAN_BANK_ACCOUNT=***********
TELCELL_BANK_CASH_ACCOUNT=***********
TELCELL_BANK_WITHDRAWN_ACCOUNT=***********
TELCELL_BANK_WITHDRAWN_SECOND_ACCOUNT=***********
TELCELL_LOAN_BANK_ACCOUNT=***********
EASYPAY_LOAN_BANK_ACCOUNT=***********
WIRE_BANK_ACCOUNT=***********
ARMECONOM_CARD_BANK_ACCOUNT=***********
ARMECONOM_CARD_BANK_WITHDRAWN_ACCOUNT=***********
EVOCA_CARD_BANK_ACCOUNT=***********
EVOCA_CARD_BANK_WITHDRAWN_ACCOUNT=***********
EASYPAY_BANK_ACCOUNT=***********
GC_CASH_BANK_ACCOUNT=***********
GC_BANK_WITHDRAWN_ACCOUNT=***********
UPAY_CASH_BANK_ACCOUNT=***********
ARPI_SOLAR_BANK_ACCOUNT=***********
PRODUCT_PROVISION_BANK_ACCOUNT=***********
VELOX_BANK_ACCOUNT=***********
AMORTIZED_LOAN_BANK_ACCOUNT=***********
FASTSHIFT_LOAN_BANK_ACCOUNT=***********

NOVA_GUARD=web
NOVA_LOCALE=hy

KAIROS_APP_ID=756c9e79
KAIROS_APP_KEY=67f35deeb116e3d028d4b9c08da8d8bf

AWS_API_KEY=********************
AWS_API_SECRET=Z4+s4/4eEsck26mMOpzDBb5F55aQTFv5XloKKTtM
AWS_USER_REGION=eu-central-1
AWS_API_VERSION=latest

AWS_S3_REGION=eu-central-1
AWS_S3_RECOGNITION_BUCKET=gc-citizens-recognition
AWS_S3_VIDEO_BUCKET=gc-video-archives
AWS_S3_LOAN_DOCUMENTS_BUCKET=gc-loan-documents
AWS_S3_MORTGAGE_MEDIA_BUCKET=gc-mortgage-media
AWS_S3_QR_CODE_BUCKET=gc-loan-qr-code
AWS_S3_EKENG_BUCKET=gc-ekeng-photo

AWS_EKENG_PHOTO_URL=https://${AWS_S3_EKENG_BUCKET}.s3.eu-central-1.amazonaws.com

TOKBOX_KEY=********
TOKBOX_SECRET=0391884c806cf6e2f6f510bf31997ae7005ea700

REFERRAL_DOCUMENTS_URL=http://***********

DOCUMENTS_SUBDIR=/pdfs
VEHICLE_MEDIA_SUBDIR=/vehicle_media
ARPI_SOLAR_MEDIA_SUBDIR=/arpi_solar_media
REAL_ESTATE_MEDIA_SUBDIR=/real_estate_media
CITIZEN_PHOTO_SUBDIR=citizens-staging
QR_CODE_SUBDIR=/qr_codes
REFERRAL_CODE_PDFS_SUBDIR=/referral_code_pdfs
MERCHANT_MEDIA_DIR=/assets/pallaton/merchants/

JENKINS_BUILD_USER=BuildUser
JENKINS_BUILD_SECRET=11d86744908e21587ccc3695f20e593138
JENKINS_URL=http://192.168.11.220:8090/
JENKINS_BUILD_BRANCH=master
JENKINS_JOB_NAME=stable-deployment
GITHUB_WEBHOOK_SECRET=Bb7Vj6GK6jTMg5nWmH95PjUrSvF9KzzsjLtC

LIVENESS_STRATEGY=KAIROS_STRATEGY

NOVA_DOWNLOADS_PATH=/nova-downloads
KFW_DOWNLOADS_PATH=/kfw_excels

PACKAGE_START_NUMBER=1000

USD_CURRENCY=530
CBA_CURRENCY=http://api.cba.am/exchangerates.asmx?wsdl

PAY_LATER_BASE_URL=http://************:3000

GC_REFERRAL_PHONE=+***********

INTERNAL_AUTH_SECRET=
INTERNAL_AUTH_KEYS="x-example-api-key:XF1GxZMByfe2V251vhvfa02EOY9QBZJbqs21yBw,x-example2-api-key:ZJbqs21yBwq2RJHI1yCisOtaferzZwJl"

VELOX_BASE_URL=https://api.velox.am

CRM_URL=http://**************

ARARAT_BANK_ADMIN_URL=https://ipaytest.arca.am:8445/payment/admin
ARARAT_BANK_ADMIN_USERNAME=global_credit_app_web
ARARAT_BANK_ADMIN_PASSWORD=CRL`3\;4RhECHbn@
ARARAT_BANK_API_URL=https://ipaytest.arca.am:8445/payment/rest
ARARAT_BANK_API_USERNAME=global_credit_app_api
ARARAT_BANK_API_PASSWORD=CRL`3\;4RhECHbn@
ARARAT_BANK_BINDING_URL=https://ipaytest.arca.am:8445/payment/rest
ARARAT_BANK_BINDING_USERNAME=global_credit_app_binding
ARARAT_BANK_BINDING_PASSWORD=CRL`3\;4RhECHbn@

EVOCA_BANK_API_URL=https://ipaytest.arca.am:8445/payment/rest
EVOCA_BANK_API_USERNAME=global_credit_testatyin_api
EVOCA_BANK_API_PASSWORD=3pvwEeMW4AXibX2B
EVOCA_BANK_BINDING_URL=https://ipaytest.arca.am:8445/payment/rest
EVOCA_BANK_BINDING_USERNAME=global_credit_testatyin_binding
EVOCA_BANK_BINDING_PASSWORD=3pvwEeMW4AXibX2B

VPOS_RETURN_URL=http://***********/process-loan-repayment
VPOS_BANK=EVOCA

PALLATON_DEEP_LINK=com.globalcreditcjsc.cashme://app/main_navigator/payment_form_screen

LUYSER_API_URL=https://f.luyser.am/api
LUYSER_USERNAME=loan
LUYSER_PASSWORD=bvPavbMqavFyuF4q

JWT_REFRESH_TTL=43800

APP_REDIRECT_LINK=http://onelink.to/cktffd

BNPL_APP_REDIRECT_LINK=https://bit.ly/armed-gc
ARMED_CRYPT_PROTOCOL=AES-256-CBC
ARMED_SECRET_KEY=p+IdjFMUmyAoU/h2apeDfvoRHqa9pLELDaye7Qh3GMcCAt2YQa7GshfcUVFkgqxf
ARMED_BASE_URL=https://preprod.armed.am
ARMED_APPROVE_URI=payment/reply
ARMED_DENY_URI=payment/reply
ARMED_CHECK_URI=payment/checkstatus

MEDESEY_LOAN_BANK_ACCOUNT=***********
UIGMOR_LOAN_BANK_ACCOUNT=***********
MARGARYAN_LOAN_BANK_ACCOUNT=***********
TONOYAN_LOAN_BANK_ACCOUNT=***********
ELITMED_LOAN_BANK_ACCOUNT=***********
IMPLANTUM_LOAN_BANK_ACCOUNT=***********
ARTKOSMED_LOAN_BANK_ACCOUNT=***********
DALIMEDMC_LOAN_BANK_ACCOUNT=***********
CMS_LOAN_BANK_ACCOUNT=***********
LA_BELL_BANK_ACCOUNT=***********
LIFE_CLINIC_BANK_ACCOUNT=***********
MEDINES_BANK_ACCOUNT=***********
COSMOTIQUE_BANK_ACCOUNT=***********
DMC_BANK_ACCOUNT=***********
AKOSTA_BANK_ACCOUNT=***********
ANI_AESTHETICS_BANK_ACCOUNT=***********
CM_SHIPPING_BANK_ACCOUNT=***********
ECO_MOTORS_BANK_ACCOUNT=***********

NOTIFIER_API_URL=http://**************:8082
NOTIFIER_X_TENANT_ID=KEY
NOTIFIER_IN_APP_PROVIDER=CASHME

ALERTING_BASE_URL=http://**************/api/event-logs
ALERTING_ORIGIN_KEY=$2y$10$M6JRoFw6LxisNXpc4atUzOdAnvw9xTzsG0LQbpk87vvTfuQCVuW9m
SHOULD_ALERT=false

EVOCA_BANK_DOMAIN=https://is.evocabank.am
EVOCA_BANK_URL=Is/ASBankIntegrationService.svc
EVOCA_BANK_PARTNER_ID=63500
EVOCA_BANK_PARTNER_SK=*********
EVOCA_BANK_PARTNER_OUR_SK=*********

#MIX prefixed variables using for laravel-mix compiled environment
MIX_BROWSERSYNC_HOST_PORT=3005
MIX_BROWSERSYNC_UI_HOST_PORT=3006
MIX_WEB_SERVER_CONTAINER=nginx
MIX_REACT_APP_CUSTOM_ENV=development
MIX_REACT_APP_GOOGLE_MAP_KEY=AIzaSyBZOY77_HHa-tj6aaNZ91JPVfHoQNutNgo

TRUSTED_PROXIES=

SCHEDULE_DAILY_LOG_COMPRESS_ENABLE=false

SCHEDULE_LUYSER_PREDEFINED_REAL_ESTATES_TIME=12:30
SCHEDULE_CLEAN_UP_DIRECTORIES_TIME=20:00
SCHEDULE_DAILY_LOG_COMPRESS_TIME=01:00
SCHEDULE_DAILY_QUEUE_WORKERS_RESTART_TIME=02:00
SCHEDULE_DAILY_LOAN_PAYMENT_STATUS_CHECK_TIME=01:30
SCHEDULE_CRON_CREDIT_LIMIT_CALCULATION_TIME='0 6 * * *'
SCHEDULE_CRON_MONTHLY_HC_CREDIT_LIMIT_ADJUSTMENT='0 23 28 * *'
SCHEDULE_CRON_TOP_UP_PERIODIC_OFFER_TIME='0 6 * * *'
SCHEDULE_CRON_TOP_UP_PERIODIC_OFFER_REMINDER_TIME='0 6 * * *'
SCHEDULE_CRON_UPDATE_LOAN_REPAYMENT_STATUS_TIME='15 * * * *'

ELASTICSEARCH_HOST=http://elasticsearch:9200/
ELASTICSEARCH_USERNAME=
ELASTICSEARCH_PASSWORD=
ELASTICSEARCH_LOGS_INDEX=cashme_log
ELASTICSEARCH_LOGS_DOC_TYPE=_doc

OVIL_APP_REDIRECT_LINK=https://bit.ly/vehicle-import

APPLICATION_API_URL=https://gc-forms.studio-one.am/api
APPLICATION_USERNAME=<EMAIL>
APPLICATION_PASSWORD=+IA+acn+q(

FASTSHIFT_LOAN_URL=https://test-merchants.fastshift.am
FASTSHIFT_AUTH_TOKEN=qErZi8rze7myL5otSgXF1GxZMByfe2V251vhvfa02EOY9QBZJbqs21yBwq2RJHI1yCisOtaferzZwJl5bn6wQDT9pWBb9OxP2NUW2EfhTd4dgxZVMHZg7w1tEyu1xedX

TAX_SERVICE_LOGIN_WSDL=http://ews.taxservice.am/taxsystem-fe-ws/taxpayer/loginService?wsdl
E_INVOICING_BASE_URL=https://e-invoicing.taxservice.am
E_INVOICING_GC_TIN=********

HC_DEBIT_ACCOUNT_NODE=444

NOTIFIER_EMAIL_SENDER=<EMAIL>
ACRA_MONITORING_SUBJECT="ACRA MONITORING SYNC"
ACRA_MONITORING_TEMPLATE_ID=111
ACRA_MONITORING_EMAILS="<EMAIL>,<EMAIL>"
EMAIL_HC_CHANGED_SUBJECT="Էլ. փոստի փոփոխություն"
EMAIL_HC_CHANGED_TEMPLATE_ID=124

OVL_WITHDRAW_LOAN_SMS_TEMPLATE_ID=145
OVL_WITHDRAW_LOAN_PUSH_TEMPLATE_ID=145

IM_ID_PROVIDER_URL=https://yesem.am:8643/idp/yesem/oidc/mc
IM_ID_CLIENT_ID=globalcredittest
IM_ID_CLIENT_SECRET=37ZsP70-yA}[
IM_ID_REDIRECT_URL=https://test.cashme.am/auth/imid/callback
IM_ID_USER_INFO_URL=/idp/yesem/oidc/mc/userinfo
IM_ID_BASE_URL=https://yesem.am:8643/
