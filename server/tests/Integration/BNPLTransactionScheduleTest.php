<?php

namespace Tests\Integration;

use App\Models\Citizen;
use App\Models\CreditLine\TransactionSchedule;
use App\Models\Loan;
use App\TestCase;
use Carbon\Carbon;
use function Functional\first;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Uuid;

class BNPLTransactionScheduleTest extends TestCase
{
    use RefreshDatabase;

    protected $token;
    protected $purchase_service;
    protected $warehouse_service;

    protected function setUp(): void
    {
        parent::setUp();

        $this->withoutJobs();

        Carbon::setTestNow(Carbon::create(2022, 9, 10));

        $loan = Loan::create([
            'amount' => 200000,
            'loan_type_id' => 13,
            'status' => 'CONFIRMED',
            'service_fee_rate' => 0,
            'dsti' => 2274.42,
            'fico_score' => 600,
            'existing_fico_score' => 600,
            'osm' => 198911.97,
            'interest_rate' => 0,
            'contract_number' => 'M19-000004',
            'credit_code' => '635004000041L001',
        ]);

        $loan_security = $loan->loan_security()->create([
            'document_number' => '*********',
            'loan_type_id' => 13,
            'suuid' => Uuid::generate(4)->string,
            'suuid_exp' => Carbon::now()->addDays(1),
            'phone_number' => '000000000',
            'email' => '<EMAIL>',
            'ssn' => '1382589070',
            'loan_id' => $loan->id,
        ]);

        Citizen::create([
            'first_name' => 'Test',
            'last_name' => 'Test',
            'middle_name' => 'Test',
            'gender' => 2,
            'phone_number' => '000000000',
            'email' => '<EMAIL>',
            'loan_id' => $loan->id,
        ]);

        $this->seed('VendorsTableSeeder');

        request()->headers->set('suuid', $loan_security->suuid);
        request()->headers->set('Client-Identifier', 'telcell_id');
        request()->headers->set('Client-Secret', 'telcell_secret');

        $this->purchase_service = resolve('App\Interfaces\CreditLine\IPurchaseService');
        $this->warehouse_service = resolve('App\Interfaces\IWarehouseService');
    }

    // Make transaction for 3 month schedule - 9000
    // Make second transaction for 3 month schedule - 4000
    public function testTransactionSchedules()
    {
        [
            'first_transaction' => $first_transaction,
            'expected_first_transaction_current_schedule' => $expected_first_transaction_current_schedule,
            'expected_first_transaction_schedule' => $expected_first_transaction_schedule,
        ] = $this->makeFirstTransaction();

        $is_first_transaction_current_schedule_assert = $this->checkSchedulesAssertion(
            $first_transaction->current_schedule()->get(),
            $expected_first_transaction_current_schedule
        );
        $this->assertTrue($is_first_transaction_current_schedule_assert);

        $is_first_transaction_schedule_assert = $this->checkSchedulesAssertion(
            $first_transaction->schedule()->get(),
            $expected_first_transaction_schedule
        );
        $this->assertTrue($is_first_transaction_schedule_assert);

        [
            'second_transaction' => $second_transaction,
            'expected_second_transaction_current_schedule' => $expected_second_transaction_current_schedule,
            'expected_second_transaction_schedule' => $expected_second_transaction_schedule,
        ] = $this->makeSecondTransaction();

        $is_second_transaction_current_schedule_assert = $this->checkSchedulesAssertion(
            $second_transaction->current_schedule,
            $expected_second_transaction_current_schedule
        );
        $this->assertTrue($is_second_transaction_current_schedule_assert);

        $is_second_transaction_schedule_assert = $this->checkSchedulesAssertion(
            $second_transaction->schedule,
            $expected_second_transaction_schedule
        );
        $this->assertTrue($is_second_transaction_schedule_assert);

        $expected_schedule_count = 6;
        $actual_schedule_count = TransactionSchedule::get()->count();
        $this->assertEquals($expected_schedule_count, $actual_schedule_count);

        $expected_disabled_schedule_count = 3;
        $actual_disabled_schedule_count = $first_transaction->schedule()->get()->count();
        $this->assertEquals($expected_disabled_schedule_count, $actual_disabled_schedule_count);
    }

    private function makeFirstTransaction()
    {
        $this->warehouse_service->setSchedule([]);

        $amount = 9000;
        $payment_id = $this->createSession($amount)->payment_id;
        $first_transaction = $this->purchase_service->makePurchase($payment_id);

        $expected_first_transaction_current_schedule = [
            [
                'base' => '3000',
                'payment' => '3000',
                'date' => Carbon::now()->setDate(2022, 10, 07),
                'disabled' => false,
            ],
            [
                'base' => '3000',
                'payment' => '3000',
                'date' => Carbon::now()->setDate(2022, 11, 07),
                'disabled' => false,
            ],
            [
                'base' => '3000',
                'payment' => '3000',
                'date' => Carbon::now()->setDate(2022, 12, 07),
                'disabled' => false,
            ],
        ];

        $expected_first_transaction_schedule = [
            [
                'service_fee' => '0',
                'base' => '3000',
                'payment' => '3000',
                'total' => '3000',
                'date' => Carbon::now()->setDate(2022, 10, 07),
                'disabled' => false,
            ],
            [
                'service_fee' => '0',
                'base' => '3000',
                'payment' => '3000',
                'total' => '3000',
                'date' => Carbon::now()->setDate(2022, 11, 07),
                'disabled' => false,
            ],
            [
                'service_fee' => '0',
                'base' => '3000',
                'payment' => '3000',
                'total' => '3000',
                'date' => Carbon::now()->setDate(2022, 12, 07),
                'disabled' => false,
            ],
        ];

        return [
            'first_transaction' => $first_transaction,
            'expected_first_transaction_current_schedule' => $expected_first_transaction_current_schedule,
            'expected_first_transaction_schedule' => $expected_first_transaction_schedule,
        ];
    }

    private function makeSecondTransaction()
    {
        Carbon::setTestNow(Carbon::create(2022, 9, 15));

        // We need to create fake existing schedule, those we are getting from DW after first transaction
        $this->warehouse_service->setSchedule([
            [
                'fdate' => '2022-10-07 00:00:00.000',
                'princ' => '3000.00',
                'Intr' => '.00',
                'sfvan' => '.00',
                'mtot' => '3000.00',
            ],
            [
                'fdate' => '2022-11-07 00:00:00.000',
                'princ' => '3000.00',
                'Intr' => '.00',
                'sfvan' => '.00',
                'mtot' => '3000.00',
            ],
            [
                'fdate' => '2022-12-07 00:00:00.000',
                'princ' => '3000.00',
                'Intr' => '.00',
                'sfvan' => '.00',
                'mtot' => '3000.00',
            ],
        ]);

        $amount = 4000;
        $payment_id = $this->createSession($amount)->payment_id;

        $second_transaction = $this->purchase_service->makePurchase($payment_id);

        $expected_second_transaction_current_schedule = [
            [
                'base' => '1330',
                'payment' => '1330',
                'date' => Carbon::now()->setDate(2022, 10, 07),
                'disabled' => false,
            ],
            [
                'base' => '1330',
                'payment' => '1330',
                'date' => Carbon::now()->setDate(2022, 11, 07),
                'disabled' => false,
            ],
            [
                'base' => '1340',
                'payment' => '1340',
                'date' => Carbon::now()->setDate(2022, 12, 07),
                'disabled' => false,
            ],
        ];

        $expected_second_transaction_schedule = [
            [
                'service_fee' => '0',
                'base' => '1330',
                'payment' => '1330',
                'total' => '4330',
                'date' => Carbon::now()->setDate(2022, 10, 07),
                'disabled' => false,
            ],
            [
                'service_fee' => '0',
                'base' => '1330',
                'payment' => '1330',
                'total' => '4330',
                'date' => Carbon::now()->setDate(2022, 11, 07),
                'disabled' => false,
            ],
            [
                'service_fee' => '0',
                'base' => '1340',
                'payment' => '1340',
                'total' => '4340',
                'date' => Carbon::now()->setDate(2022, 12, 07),
                'disabled' => false,
            ],
        ];

        return [
            'second_transaction' => $second_transaction,
            'expected_second_transaction_current_schedule' => $expected_second_transaction_current_schedule,
            'expected_second_transaction_schedule' => $expected_second_transaction_schedule,
        ];
    }

    private function checkSchedulesAssertion($actual_schedule, $expected_schedule)
    {
        return $actual_schedule->every(function ($schedule) use ($expected_schedule) {
            $date = $schedule['date'];

            $expected_schedule_element = first($expected_schedule, function ($ex_schedule) use ($date) {
                return $ex_schedule['date']->setTimeZone(constants('ARM_TIMEZONE'))->isSameDay($date);
            });

            return !collect($expected_schedule_element)->diffAssoc($schedule)->count();
        });
    }

    private function createSession($amount)
    {
        $payload = [
            'order_id' => rand(),
            'amount' => $amount,
            'description' => 'test',
            'callback_url' => '',
            'currency' => 'AMD',
        ];

        $purchase_service = resolve('App\Interfaces\CreditLine\IPurchaseService');

        return $purchase_service->createSession($payload);
    }
}
