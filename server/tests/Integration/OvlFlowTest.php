<?php

namespace Tests\Integration;

use App\MigrateFreshSeedOnce;
use App\Models\LoanSecurity;
use App\Models\LoanSecurityMeta;
use App\TestCase;
use Carbon\Carbon;
use Illuminate\Support\Facades\Queue;
use Uuid;

class OvlFlowTest extends TestCase
{
    use MigrateFreshSeedOnce {
        setUp as protected traitSetUp;
    }

    protected function setUp(): void
    {
        $this->traitSetUp();

        // Prevent jobs queing
        Queue::fake();

        $fake_now = Carbon::create(2021, 10, 12, 0);
        Carbon::setTestNow($fake_now);

        LoanSecurity::create(
            [
                'document_number' => 'AA0000007',
                'loan_type_id' => 3,
                'suuid' => Uuid::generate(4)->string,
                'suuid_exp' => Carbon::now()->addDays(1),
                'phone_number' => '*********',
            ]
        );

        LoanSecurityMeta::create([
            'loan_security_id' => 1,
            'step' => 'FETCH_CITIZEN',
            'success' => 'true',
            'content' => null,
        ]);
    }

    public function testLoanInsert()
    {
        $suuid = LoanSecurity::first()->suuid;

        $payload = [
            'amount' => 1300000,
            'last_month_payment' => 102301.42,
            'monthly_payment' => 153000,
            'months' => 11,
            'total' => 1632301.42,
            'loan_type_id' => 2,
            'vehicle_number' => '34RA982',
            'pipe_type_id' => null,
        ];

        $response = $this->withHeaders([
            'suuid' => $suuid,
        ])->json(
            'POST',
            '/api/loans/approve',
            $payload
        );

        $response
            ->assertStatus(200)
            ->assertJson([
                'data' => [
                    'status' => 'PENDING',
                    'amount' => 1300000,
                    'interest_rate' => 48,
                    'last_month_payment' => 102301.42,
                    'monthly_payment' => 153000,
                    'months' => 11,
                    'total' => 1632301.42,
                    'withdrawal_fee' => 39000,
                    'real_interest_rate' => 49.49,
                    'is_offline' => false,
                    'loan_type_id' => 2,
                    'contract_number' => 'V19-000004',
                ],
                'error' => null,
            ]);

        $this->assertDatabaseHas('loans', [
            'public_id' => null,
            'user_id' => null,
            'loan_type_id' => 2,
            'payment_id' => null,
            'payment_type' => null,
            'amount' => '1300000',
            'months' => 11,
            'total' => ' 1632301.42',
            'monthly_payment' => '153000',
            'last_month_payment' => '102301.42',
            'interest_rate' => '48',
            'real_interest_rate' => '49.49',
            'status' => 'PENDING',
            'sign_date' => null,
            'confirmed_at' => null,
            'verified_at' => null,
            'contract_number' => 'V19-000004',
            'credit_code' => '635005000010L001',
            'notification_method' => null,
            'fico_score' => '575',
            'failed_payment' => null,
            'dispute_solution_method' => null,
            'withdrawal_fee' => '39000',
            'is_offline' => false,
            'service_fee_rate' => 12,
        ]);
    }

    public function testInsertedLoanSchedule()
    {
        $suuid = LoanSecurity::first()->suuid;

        $payload = [
            'amount' => 1300000,
            'last_month_payment' => 102301.42,
            'monthly_payment' => 153000,
            'months' => 11,
            'total' => 1632301.42,
            'loan_type_id' => 2,
            'vehicle_number' => '34RA982',
            'pipe_type_id' => null,
        ];

        $this->withHeaders([
            'suuid' => $suuid,
        ])->json(
            'POST',
            '/api/loans/approve',
            $payload
        );

        $this->assertDatabaseHas('loan_schedules', [
            'loan_id' => 1,
            'service_fee' => '52997.26',
            'base' => '100002.74',
            'balance' => '1199997.26',
            'payment' => '153000',
            'service_fee_interest' => '13249.32',
            'service_fee_plain' => '39747.94',
        ]);
        $this->assertDatabaseHas('loan_schedules', [
            'loan_id' => 1,
            'service_fee' => '47524.08',
            'base' => '105475.92',
            'balance' => '1094521.34',
            'payment' => '153000',
            'service_fee_interest' => '11835.59',
            'service_fee_plain' => '35688.49',
        ]);
        $this->assertDatabaseHas('loan_schedules', [
            'loan_id' => 1,
            'service_fee' => '44996.05',
            'base' => '108003.95',
            'balance' => '986517.39',
            'payment' => '153000',
            'service_fee_interest' => '11155.12',
            'service_fee_plain' => '33840.93',
        ]);
    }

    public function testRegenerateLoanSchedule()
    {
        $suuid = LoanSecurity::first()->suuid;

        $payload = [
            'amount' => 1300000,
            'last_month_payment' => 102301.42,
            'monthly_payment' => 153000,
            'months' => 11,
            'total' => 1632301.42,
            'loan_type_id' => 2,
            'vehicle_number' => '34RA982',
            'pipe_type_id' => null,
        ];

        // Approve loan
        $response = $this->withHeaders([
            'suuid' => $suuid,
        ])->json(
            'POST',
            '/api/loans/approve',
            $payload
        );
        $response->assertStatus(200);

        // Get personal info
        $response = $this->withHeaders([
          'suuid' => $suuid,
        ])->json(
            'GET',
            '/api/loans/personal-info'
        );
        $response->assertStatus(200);

        // Update personal info
        $response = $this->withHeaders([
          'suuid' => $suuid,
        ])->json(
            'PUT',
            '/api/loans/personal-info',
            [
                'notification_method' => 'email',
                'dispute_solution_method' => 'gnm_arbitration',
                'additional_phone_number' => '95000000',
                'email' => '<EMAIL>',
            ]
        );
        $response->assertStatus(200);

        // Set car verification info
        $response = $this->withHeaders([
            'suuid' => $suuid,
        ])->json(
            'POST',
            '/api/car-verification',
            [
                'checkup_date' => Carbon::now()->addDays(2)->format('Y-m-d'),
                'address' => '2 Սասնա Ծռերի փողոց, Երևան 0054, Հայաստան',
                'notes' => '',
                'latitude' => '40.1872',
                'longitude' => '44.5152',
            ]
        );
        $response->assertStatus(200);

        // Select payment type
        $response = $this->withHeaders([
          'suuid' => $suuid,
        ])->json(
            'POST',
            '/api/loans/cash-transfer',
            [
                'route' => 'cash-transfer',
            ]
        );
        $response->assertStatus(200);

        $this->assertDatabaseHas('loans', [
            'public_id' => null,
            'user_id' => null,
            'loan_type_id' => 2,
            'payment_id' => null,
            'payment_type' => null,
            'amount' => '1300000',
            'months' => 11,
            'total' => '1632301.42',
            'monthly_payment' => '153000',
            'last_month_payment' => '102301.42',
            'interest_rate' => '48',
            'real_interest_rate' => '49.49',
            'next_payment_date' => '2021-11-14',
            'status' => 'PENDING',
            'sign_date' => '2021-10-14 00:00:00',
            'confirmed_at' => null,
            'verified_at' => null,
            'contract_number' => 'V19-000004',
            'credit_code' => '635005000010L001',
            'notification_method' => 'email',
            'fico_score' => '575',
            'failed_payment' => null,
            'dispute_solution_method' => 'gnm_arbitration',
            'withdrawal_fee' => '39000',
            'is_offline' => false,
            'service_fee_rate' => '12',
        ]);

        $this->assertDatabaseHas('loan_schedules', [
            'loan_id' => 1,
            'service_fee' => '52997.26',
            'base' => '100002.74',
            'balance' => '1199997.26',
            'payment' => '153000',
            'service_fee_interest' => '13249.32',
            'service_fee_plain' => '39747.94',
        ]);
        $this->assertDatabaseHas('loan_schedules', [
            'loan_id' => 1,
            'service_fee' => '47524.08',
            'base' => '105475.92',
            'balance' => '1094521.34',
            'payment' => '153000',
            'service_fee_interest' => '11835.59',
            'service_fee_plain' => '35688.49',
        ]);
        $this->assertDatabaseHas('loan_schedules', [
            'loan_id' => 1,
            'service_fee' => '44996.05',
            'base' => '108003.95',
            'balance' => '986517.39',
            'payment' => '153000',
            'service_fee_interest' => '11155.12',
            'service_fee_plain' => '33840.93',
        ]);
    }

    public function testLoanInsertNotMarried()
    {
        $loan_security = LoanSecurity::create(
            [
                'document_number' => 'AA0000006',
                'loan_type_id' => 3,
                'suuid' => Uuid::generate(4)->string,
                'suuid_exp' => Carbon::now()->addDays(1),
                'phone_number' => '*********',
            ]
        );

        $loan_security->security_metas()->createMany([
            [
                'step' => 'FETCH_CITIZEN',
                'success' => 'true',
                'content' => null,
            ],
        ]);

        $payload = [
            'amount' => 1300000,
            'last_month_payment' => 102301.42,
            'monthly_payment' => 153000,
            'months' => 11,
            'total' => 1632301.42,
            'loan_type_id' => 2,
            'vehicle_number' => '34RA982',
            'pipe_type_id' => null,
        ];

        $response = $this->withHeaders([
            'suuid' => $loan_security->suuid,
        ])->json(
            'POST',
            '/api/loans/approve',
            $payload
        );

        $response
            ->assertStatus(200)
            ->assertJson([
                'data' => [
                    'status' => 'PENDING',
                    'amount' => 1300000,
                    'interest_rate' => 48,
                    'last_month_payment' => 102301.42,
                    'monthly_payment' => 153000,
                    'months' => 11,
                    'total' => 1632301.42,
                    'withdrawal_fee' => 39000,
                    'real_interest_rate' => 49.49,
                    'is_offline' => false,
                    'loan_type_id' => 2,
                    'contract_number' => 'V19-000004',
                ],
                'error' => null,
            ]);

        $this->assertDatabaseHas('loans', [
            'public_id' => null,
            'user_id' => null,
            'loan_type_id' => 2,
            'payment_id' => null,
            'payment_type' => null,
            'amount' => '1300000',
            'months' => 11,
            'total' => '1632301.42',
            'monthly_payment' => '153000',
            'last_month_payment' => '102301.42',
            'interest_rate' => '48',
            'real_interest_rate' => '49.49',
            'status' => 'PENDING',
            'sign_date' => null,
            'confirmed_at' => null,
            'verified_at' => null,
            'contract_number' => 'V19-000004',
            'credit_code' => '635005000010L001',
            'notification_method' => null,
            'fico_score' => '551',
            'failed_payment' => null,
            'dispute_solution_method' => null,
            'withdrawal_fee' => '39000',
            'is_offline' => false,
            'service_fee_rate' => 12,
        ]);
    }

    public function testLoanInsertPurchaseVehicleBeforeMerge()
    {
        $loan_security = LoanSecurity::create([
                'document_number' => 'AA0000020',
                'loan_type_id' => 3,
                'suuid' => Uuid::generate(4)->string,
                'suuid_exp' => Carbon::now()->addDays(1),
                'phone_number' => '*********',
            ]
        );

        $loan_security->security_metas()->createMany([
            [
                'step' => 'FETCH_CITIZEN',
                'success' => 'true',
                'content' => null,
            ],
        ]);

        $payload = [
            'amount' => 1300000,
            'last_month_payment' => 102301.42,
            'monthly_payment' => 153000.0,
            'months' => 11,
            'total' => 1632301.42,
            'loan_type_id' => 2,
            'vehicle_number' => '34RA982',
            'pipe_type_id' => null,
        ];

        $response = $this->withHeaders([
            'suuid' => $loan_security->suuid,
        ])->json(
            'POST',
            '/api/loans/approve',
            $payload
        );

        $response
            ->assertStatus(200)
            ->assertJson([
                'data' => [
                    'status' => 'PENDING',
                    'amount' => 1300000,
                    'withdrawal_fee' => 39000,
                    'real_interest_rate' => 49.49,
                    'last_month_payment' => 102301.42,
                    'monthly_payment' => 153000,
                    'months' => 11,
                    'total' => 1632301.42,
                    'is_offline' => false,
                    'loan_type_id' => 2,
                    'contract_number' => 'V19-000004',
                ],
                'error' => null,
            ]);

        $this->assertDatabaseHas('loans', [
            'public_id' => null,
            'user_id' => null,
            'loan_type_id' => 2,
            'payment_id' => null,
            'payment_type' => null,
            'amount' => '1300000',
            'last_month_payment' => 102301.42,
            'monthly_payment' => 153000.0,
            'months' => 11,
            'total' => 1632301.42,
            'interest_rate' => '48',
            'real_interest_rate' => '49.49',
            'status' => 'PENDING',
            'sign_date' => null,
            'confirmed_at' => null,
            'verified_at' => null,
            'contract_number' => 'V19-000004',
            'credit_code' => '635005000010L001',
            'notification_method' => null,
            'fico_score' => '525',
            'failed_payment' => null,
            'dispute_solution_method' => null,
            'withdrawal_fee' => '39000',
            'is_offline' => false,
            'service_fee_rate' => 12,
        ]);

        $this->assertDatabaseMissing('spouses', [
            'first_name' => 'ԶԱՐՈՒՀԻ',
            'last_name' => 'ՄՈՒՐԱԴՅԱՆ',
            'passport_number' => '*********',
        ]);
    }

    public function testLoanInsertPurchaseVehicleAfterMerge()
    {
        $loan_security = LoanSecurity::create([
                'document_number' => '*********',
                'loan_type_id' => 3,
                'suuid' => Uuid::generate(4)->string,
                'suuid_exp' => Carbon::now()->addDays(1),
                'phone_number' => '*********',
            ]
        );

        $loan_security->security_metas()->createMany([
            [
                'step' => 'FETCH_CITIZEN',
                'success' => 'true',
                'content' => null,
            ],
        ]);

        $payload = [
            'amount' => 1300000,
            'last_month_payment' => 102301.42,
            'monthly_payment' => 153000.0,
            'months' => 11,
            'total' => 1632301.42,
            'loan_type_id' => 2,
            'vehicle_number' => '34RA982',
            'pipe_type_id' => null,
        ];

        $response = $this->withHeaders([
            'suuid' => $loan_security->suuid,
        ])->json(
            'POST',
            '/api/loans/approve',
            $payload
        );

        $response
            ->assertStatus(200)
            ->assertJson([
                'data' => [
                    'status' => 'PENDING',
                    'amount' => 1300000,
                    'last_month_payment' => 102301.42,
                    'monthly_payment' => 153000.0,
                    'months' => 11,
                    'total' => 1632301.42,
                    'withdrawal_fee' => 39000,
                    'real_interest_rate' => 49.49,
                    'is_offline' => false,
                    'loan_type_id' => 2,
                    'contract_number' => 'V19-000004',
                ],
                'error' => null,
            ]);

        $this->assertDatabaseHas('loans', [
            'public_id' => null,
            'user_id' => null,
            'loan_type_id' => 2,
            'payment_id' => null,
            'payment_type' => null,
            'amount' => '1300000',
            'last_month_payment' => 102301.42,
            'monthly_payment' => 153000.0,
            'months' => 11,
            'total' => 1632301.42,
            'interest_rate' => '48',
            'real_interest_rate' => '49.49',
            'status' => 'PENDING',
            'sign_date' => null,
            'confirmed_at' => null,
            'verified_at' => null,
            'contract_number' => 'V19-000004',
            'credit_code' => '635005000010L001',
            'notification_method' => null,
            'fico_score' => '550',
            'failed_payment' => null,
            'dispute_solution_method' => null,
            'withdrawal_fee' => '39000',
            'is_offline' => false,
            'service_fee_rate' => 12,
        ]);

        $this->assertDatabaseHas('spouses', [
            'first_name' => 'ԶԱՐՈՒՀԻ',
            'last_name' => 'ՄՈՒՐԱԴՅԱՆ',
            'passport_number' => '*********',
        ]);
    }

    public function testLoanInsertDivorceSpouse()
    {
        $loan_security = LoanSecurity::create([
                'document_number' => '*********',
                'loan_type_id' => 3,
                'suuid' => Uuid::generate(4)->string,
                'suuid_exp' => Carbon::now()->addDays(1),
                'phone_number' => '*********',
            ]
        );

        $loan_security->security_metas()->createMany([
            [
                'step' => 'FETCH_CITIZEN',
                'success' => 'true',
                'content' => null,
            ],
        ]);

        $payload = [
            'amount' => 1300000,
            'last_month_payment' => 102301.42,
            'monthly_payment' => 153000.0,
            'months' => 11,
            'total' => 1632301.42,
            'loan_type_id' => 2,
            'vehicle_number' => '34RA982',
            'pipe_type_id' => null,
        ];

        $response = $this->withHeaders([
            'suuid' => $loan_security->suuid,
        ])->json(
            'POST',
            '/api/loans/approve',
            $payload
        );

        $response
            ->assertStatus(200)
            ->assertJson([
                'data' => [
                    'status' => 'PENDING',
                    'amount' => 1300000,
                    'last_month_payment' => 102301.42,
                    'monthly_payment' => 153000.0,
                    'months' => 11,
                    'total' => 1632301.42,
                    'withdrawal_fee' => 39000,
                    'real_interest_rate' => 49.49,
                    'is_offline' => false,
                    'loan_type_id' => 2,
                    'contract_number' => 'V19-000004',
                ],
                'error' => null,
            ]);

        $this->assertDatabaseHas('loans', [
            'public_id' => null,
            'user_id' => null,
            'loan_type_id' => 2,
            'payment_id' => null,
            'payment_type' => null,
            'amount' => '1300000',
            'last_month_payment' => 102301.42,
            'monthly_payment' => 153000,
            'months' => 11,
            'total' => 1632301.42,
            'interest_rate' => 48,
            'real_interest_rate' => '49.49',
            'status' => 'PENDING',
            'sign_date' => null,
            'confirmed_at' => null,
            'verified_at' => null,
            'contract_number' => 'V19-000004',
            'credit_code' => '635005000010L001',
            'notification_method' => null,
            'fico_score' => '550',
            'failed_payment' => null,
            'dispute_solution_method' => null,
            'withdrawal_fee' => '39000',
            'is_offline' => false,
            'service_fee_rate' => 12,
        ]);

        $this->assertDatabaseMissing('spouses', [
            'first_name' => 'ԶԱՐՈՒՀԻ',
            'last_name' => 'ՄՈՒՐԱԴՅԱՆ',
            'passport_number' => '*********',
        ]);
    }
}
