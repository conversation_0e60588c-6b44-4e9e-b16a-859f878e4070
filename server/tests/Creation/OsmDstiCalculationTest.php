<?php

namespace Tests\Unit;

use App\Helpers\ArrayHelper;
use App\Models\LoanSecurityMeta;
use App\TestCase;
use App\Utils\DSTICalculation;
use App\Utils\OSMCalculation;
use Carbon\Carbon;
use Config;
use function Functional\first;
use function Functional\pluck;
use function Functional\reduce_left;
use Illuminate\Support\Facades\Log;

class OsmDstiCalculationTest extends TestCase
{
    const LOAN_SECURITY_ID = '584';

    protected function setUp(): void
    {
        parent::setUp();

        // We need to use real database host and specified environment during this test
        config(['database.connections.pgsql.host' => 'postgres']);
    }

    // IMPORTANT NOTE!!!: We need to update appropriate CONSTANT keys before running this method,
    // this method will create full BNPL with appropriate transaction
    public function testOsmDstiCalculation()
    {
        $citizen = $this->prepareCitizen();

        $osm = $this->getOsm($citizen);
        $dstiIncome = $this->dstiIncome($citizen);
        $dstiRepayment = $this->getDstiRepayment($citizen);

        Log::info('OsmDstiCalculationTest', [
            'loan_security_id' => $citizen['loan_security_id'],
            'meta_step_id' => $citizen['meta_step_id'],
            'osm' => $osm,
            'dstiIncome' => $dstiIncome,
            'dstiRepayment' => $dstiRepayment['repayment'],
            'internal_active_loans' => $citizen['internal_active_loans'],
        ]);

        $this->resetTestingConfiguration();
    }

    public function prepareCitizen()
    {
        $meta_step = LoanSecurityMeta::where('loan_security_id', self::LOAN_SECURITY_ID)
            ->where('step', 'CITIZEN_INFO')->first();
        $content = json_decode($meta_step->content, true);

        return [
            'meta_step_id' => $meta_step->id ?? null,
            'loan_security_id' => $meta_step->loan_security_id ?? null,
            'acra' => $content['acra'] ?? [],
            'nork' => $content['nork'] ?? [],
            'internal_active_loans' => json_decode(json_encode($content['internal_active_loans'])) ?? [],
        ];
    }

    protected function dstiIncome($source)
    {
        ['repayment' => $repayment, 'income_repayment' => $income_repayment] = $this->getDstiRepayment($source);

        if ($repayment == 0) {
            return 0;
        }

        return round($this->getIncome($income_repayment, 'dsti'), 2);
    }

    public function getDstiRepayment($source): array
    {
        $acra_loans = $source['acra']['PARTICIPIENT']['Loans']['Loan'] ?? [];

        $dsti_calculation = new DSTICalculation($acra_loans, $source['internal_active_loans']);

        return $dsti_calculation->calculate();
    }

    public function getOsm($source): float
    {
        $acra_loans = $source['acra']['PARTICIPIENT']['Loans']['Loan'] ?? [];
        $work_data = $source['nork']['WorkData'] ?? [];

        $osm_calculation = new OSMCalculation($acra_loans, $source['internal_active_loans']);
        ['repayment' => $repayment, 'income_repayment' => $income_repayment] = $osm_calculation->calculate();

        $income = $this->getIncomeOsm($income_repayment, $work_data);

        return round($income - $repayment, 2);
    }

    protected function getIncomeOsm($repayment, $work_data)
    {
        $income = $this->getIncome($repayment, 'osm');

        if ($work_data != []) {
            $salary = $this->getTotalSalary($work_data);

            return max($salary, $income);
        }

        return $income;
    }

    protected function getIncome($repayment, $type)
    {
        if ($repayment == 0) {
            return 0;
        }

        $coefficient = first(constants('INCOME_COEFFICIENTS'), function ($income_coefficient) use ($repayment) {
            $rounded = round($repayment);

            return $rounded >= $income_coefficient['min'] && $rounded <= $income_coefficient['max'];
        }) ?? constants('INCOME_COEFFICIENTS')[0];

        return $repayment / $coefficient['coefficient'][$type];
    }

    public function getTotalSalary($workplaces)
    {
        $current_workplaces = $this->getCurrentWorkplaces($workplaces);
        $salaries = pluck($current_workplaces, 'Salary');

        return array_sum($salaries);
    }

    private function getCurrentWorkplaces($workplaces)
    {
        $workplaces = ArrayHelper::is_assoc($workplaces) ? [$workplaces] : $workplaces;
        $current_workplaces = [];

        foreach ($workplaces as $workplace) {
            $days_working = $workplace['EntryDate']
                ? Carbon::createFromFormat(constants('NORK_DATE_FORMAT'), $workplace['EntryDate'])->diffInDays()
                : 0;

            if ((!$workplace['ExpiryDate'] || $workplace['ExpiryDate'] == '') && $days_working > constants('DAYS_FROM_START')) {
                array_push($current_workplaces, $workplace);
            } elseif ((!$workplace['ExpiryDate'] || $workplace['ExpiryDate'] == '') && $days_working <= constants('DAYS_FROM_START')) {
                array_push($current_workplaces, $this->getPrevWorkplaceByDate($workplace, $workplaces));
            }
        }

        return reduce_left($current_workplaces, function ($current_workplace, $index, $collection, $unique_workplaces) {
            if ($this->isUniqueWorkplace($current_workplace, $unique_workplaces)) {
                array_push($unique_workplaces, $current_workplace);
            }

            return $unique_workplaces;
        }, []);
    }

    //We consider the workplace is unique if the following fields are not repeated at the same time [Hvhh,Pashton,Salary]
    private function isUniqueWorkplace($current_workplace, $unique_workplaces)
    {
        foreach ($unique_workplaces as $workplace) {
            if (($current_workplace['Hvhh'] === $workplace['Hvhh']) &&
                ($current_workplace['Pashton'] === $workplace['Pashton']) &&
                ($current_workplace['Salary'] === $workplace['Salary'])) {
                return false;
            }
        }

        return true;
    }

    // If current work contract has no EXPIRY_DATE and there are no SALARY get SALARY from previous contract with same HVHH
    // Also previous contract EXPIRY_DATE should be equal to current contract ENTRY_DATE - 1
    private function getPrevWorkplaceByDate($current, $workplaces)
    {
        if ($current['EntryDate'] && $current['EntryDate'] != '') {
            $last_day_of_year = Carbon::createFromFormat(constants('NORK_DATE_FORMAT'), $current['EntryDate'])->subDays(1);

            $workplace = first($workplaces, function ($item) use ($current, $last_day_of_year) {
                if ($item['ExpiryDate'] && $item['ExpiryDate'] != '') {
                    $prev_date = Carbon::createFromFormat(constants('NORK_DATE_FORMAT'), $item['ExpiryDate']);

                    return $item['Hvhh'] == $current['Hvhh'] && $last_day_of_year == $prev_date;
                }
            });

            return $workplace;
        }
    }

    private function resetTestingConfiguration()
    {
        app()->env = 'testing';
        config(['database.connections.pgsql.host' => 'test-postgres']);
    }
}
