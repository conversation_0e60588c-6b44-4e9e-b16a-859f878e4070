<?php

namespace Tests\Unit\OVL;

use Tests\Unit\RuleEngineExecutorTest;

class RuleEngineInterestRateOVLTest extends RuleEngineExecutorTest
{
    protected $loan_type = 'OVL';

    public function dataProvider(): array
    {
        return [
            [[
                'rule' => 'interestRate_fico_missing_age_21_25',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => null,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 48,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_missing_age_26_30',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => null,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 48,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_missing_age_31_40',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => null,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 48,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_missing_age_41_50',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => null,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 48,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_missing_age_51_65',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => null,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 48,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_300_525_age_21_25',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 300,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 48,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_300_525_age_26_30',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 300,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 48,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_300_525_age_31_40',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 300,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 48,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_300_525_age_41_50',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 300,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 48,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_300_525_age_51_65',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 300,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 48,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_526_550_age_21_25',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 526,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 48,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_526_550_age_26_30',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 526,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 32,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_526_550_age_31_40',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 526,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 44,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_526_550_age_41_50',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 526,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 38,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_526_550_age_51_65',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 526,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 48,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_551_575_age_21_25',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 551,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 32,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_551_575_age_26_30',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 551,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 32,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_551_575_age_31_40',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 551,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 44,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_551_575_age_41_50',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 551,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 48,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_551_575_age_51_65',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 551,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 48,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_576_600_age_21_25',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 576,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 38,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_576_600_age_26_30',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 576,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 32,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_576_600_age_31_40',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 576,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 38,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_576_600_age_41_50',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 576,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 38,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_576_600_age_51_65',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 576,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 44,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_601_625_age_21_25',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 601,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 30,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_601_625_age_26_30',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 601,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 30,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_601_625_age_31_40',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 601,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 30,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_601_625_age_41_50',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 601,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 32,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_601_625_age_51_65',
                'context' => [
                    'input' => [
                        'age' => 52,
                        'fico' => 601,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 38,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_626_650_age_21_25',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 626,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 30,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_626_650_age_26_30',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 626,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 30,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_626_650_age_31_40',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 626,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 30,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_626_650_age_41_50',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 626,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 30,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_626_650_age_51_65',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 626,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 48,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_650_age_21_25',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 651,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 22,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_650_age_26_30',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 651,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 20,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_650_age_31_40',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 651,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 22,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_650_age_41_50',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 651,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 28,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'interestRate_fico_650_age_51_65',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 651,
                    ],
                ],
                'output' => [
                    'interestRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => 28,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
        ];
    }
}
