<?php

namespace Tests\Unit;

use App\Exceptions\PaymentFailureException;
use App\MigrateFreshSeedOnce;
use App\Models\CardToCardPayment;
use App\Models\CashPayment;
use App\Models\Citizen;
use App\Models\Loan;
use App\Models\LoanSecurity;
use App\Models\LoanSecurityMeta;
use App\Models\Paymentable;
use App\Services\ArmeconomService;
use App\Services\EvocaService;
use App\Services\LoanServiceOCL;
use App\Services\PaymentService;
use App\TestCase;
use Carbon\Carbon;
use Mockery;
use ReflectionClass;
use Tests\Traits\TestHelper;

class PaymentFlowTest extends TestCase
{
    use TestHelper;
    use MigrateFreshSeedOnce {
        setUp as protected traitSetUp;
    }

    protected $approve_payload = [
        'amount' => 50000,
        'last_month_payment' => 4093.13,
        'monthly_payment' => 5000,
        'months' => 17,
        'total' => 84093.13,
        'pipe_type_id' => null,
    ];

    protected function setUp(): void
    {
        $this->traitSetUp();
        $now = now();
        $fake_now = Carbon::create(2021, 10, 13, 0);
        Carbon::setTestNow($fake_now);

        $this->loan_security = LoanSecurity::create(
            [
                'document_number' => 'AA0000007',
                'ssn' => '1382589070',
                'loan_type_id' => 1,
                'suuid' => generate_uuid(),
                'verification_code' => 1111,
                'verification_code_exp' => $now->subMinute(),
                'suuid_exp' => $now->addDays(1),
                'phone_number' => '000000000',
                'email' => '<EMAIL>',
                'identity_verification_code' => 1234,
                'get_identity_verification_code_attempts' => 1,
                'identity_verification_code_sent' => $now,
                'identity_verification_code_exp' => $now->addMinutes(constants('IDENTITY_VERIFICATION_CODE_EXP')),
            ]
        );

        LoanSecurityMeta::insert([
            [
                'loan_security_id' => $this->loan_security->id,
                'step' => 'GET_PERSONAL_INFO',
                'success' => true,
            ],
            [
                'loan_security_id' => $this->loan_security->id,
                'step' => 'LOAN_APPROVAL',
                'success' => true,
            ],
        ]);

        $this->loan = Loan::create([
            'loan_security_id' => $this->loan_security->id,
            'status' => 'PENDING',
            'amount' => 50000,
            'interest_rate' => '78',
            'last_month_payment' => 4093.13,
            'monthly_payment' => 5000,
            'months' => 17,
            'total' => 84093.13,
            'withdrawal_fee' => 0,
            'real_interest_rate' => 79.14,
            'is_offline' => false,
            'loan_type_id' => 1,
            'contract_number' => 'M19-000004',
            'sign_date' => $now,
        ]);

        $this->loan_security->update(['loan_id' => $this->loan->id]);

        Citizen::create([
            'first_name_en' => 'first',
            'last_name_en' => 'last',
            'email' => '<EMAIL>',
            'loan_id' => $this->loan->id,
        ]);
        $this->card = CardToCardPayment::create([
            'card_number' => '****************',
            'embossed_name' => 'YELENA SAHAKYAN',
            'year' => '2026',
            'month' => '07',
            'bank' => 'Evocabank',
        ]);

        Paymentable::create([
            'loan_id' => $this->loan->id,
            'paymentable_type' => 'App\Models\CardToCardPayment',
            'paymentable_id' => $this->card->id,
        ]);
    }

    public function testCardToCardTransferPaymentFailureWithFallback()
    {
        $mock = Mockery::mock(PaymentService::class)->makePartial();
        $mock->shouldReceive('makeTransfer')->andThrowExceptions([new PaymentFailureException()]);
        $this->app->bind(PaymentService::class, function () use ($mock) {
            return $mock;
        });

        $this->withoutJobs();

        $loan_service_ocl = new LoanServiceOCL();

        // with fallback
        putenv('FALLBACK_ALLOWED=true');

        $loan_service_ocl->processLoanPayment($this->loan);

        $this->assertDatabaseHas('cash_payments', [
            'paid' => null,
            'withdrawn' => null,
        ]);
        $this->assertDatabaseHas('paymentables', [
            'loan_id' => $this->loan->id,
            'paymentable_type' => CashPayment::class,
        ]);
        $this->assertDatabaseHas('paymentables', [
            'loan_id' => $this->loan->id,
            'paymentable_type' => CardToCardPayment::class,
        ]);
    }

    public function testCardToCardTransferPaymentFailureWithoutFallback()
    {
        $mock = Mockery::mock(PaymentService::class)->makePartial();
        $mock->shouldReceive('makeTransfer')->andThrowExceptions([new PaymentFailureException()]);
        $this->app->bind(PaymentService::class, function () use ($mock) {
            return $mock;
        });

        $loan_service_ocl = new LoanServiceOCL();

        // without fallback
        putenv('FALLBACK_ALLOWED=false');

        $loan_service_ocl->processLoanPayment($this->loan);

        $this->assertDatabaseMissing('cash_payments', [
            'paid' => null,
            'withdrawn' => null,
            'payment_status' => 'PAYMENT_PROCESSED',
        ]);
        $this->assertDatabaseMissing('paymentables', [
            'loan_id' => $this->loan->id,
            'paymentable_type' => CashPayment::class,
        ]);
        $this->assertDatabaseHas('loans', [
            'id' => $this->loan->id,
            'status' => Loan::FAILED,
        ]);

        putenv('FALLBACK_ALLOWED=true');
    }

    public function testCheckCardPrimaryBank()
    {
        $mock = Mockery::mock(EvocaService::class)->makePartial();
        $embossed_name = $this->loan->citizen->first_name_en.' '.$this->loan->citizen->last_name_en;
        $mock->shouldReceive('checkCard')->andReturn($embossed_name);
        $this->app->bind(EvocaService::class, function () use ($mock) {
            return $mock;
        });

        $payment_service = new PaymentService();
        $class = new ReflectionClass($payment_service);
        $method = $class->getMethod('checkCard');
        $method->setAccessible(true);
        $output = $method->invoke($payment_service, $this->loan, '****************', '29', '10');
        $embossed_name = $this->loan->citizen->first_name_en.' '.$this->loan->citizen->last_name_en;

        $this->assertEquals([
            'embossed_name' => $embossed_name,
            'bank' => constants('TRANSFER_BANKS.EVOCABANK'),
        ], $output);
    }

    public function testCheckCardSecondaryBank()
    {
        // mock arca services
        $mock = Mockery::mock(EvocaService::class)->makePartial();
        $mock->shouldReceive('checkCard')->andThrowExceptions([new \Exception()]);
        $this->app->bind(EvocaService::class, function () use ($mock) {
            return $mock;
        });

        $mock = Mockery::mock(ArmeconomService::class)->makePartial();
        $embossed_name = $this->loan->citizen->first_name_en.' '.$this->loan->citizen->last_name_en;
        $mock->shouldReceive('checkCard')->andReturn($embossed_name);
        $this->app->bind(ArmeconomService::class, function () use ($mock) {
            return $mock;
        });

        // use reflection to change method accessibility
        $payment_service = new PaymentService();
        $class = new ReflectionClass($payment_service);
        $method = $class->getMethod('checkCard');
        $method->setAccessible(true);
        $output = $method->invoke($payment_service, $this->loan, '****************', '29', '10');

        $this->assertEquals([
            'embossed_name' => $embossed_name,
            'bank' => constants('TRANSFER_BANKS.ARMECONOMBANK'),
        ], $output);
    }

    public function testCardToCardPaymentCreationPrimaryBank()
    {
        // mock arca services
        $mock = Mockery::mock(EvocaService::class)->makePartial();
        $embossed_name = $this->loan->citizen->first_name_en.' '.$this->loan->citizen->last_name_en;
        $mock->shouldReceive('checkCard')->andReturn($embossed_name);
        $this->app->bind(EvocaService::class, function () use ($mock) {
            return $mock;
        });

        $payment_service = new PaymentService();
        $payment_service->createCardToCardPayment($this->loan, '****************', '29', '10');

        $this->assertDatabaseHas('card_to_card_payments', [
            'card_number' => '****************',
            'embossed_name' => $embossed_name,
            'year' => '29',
            'month' => '10',
            'bank' => constants('TRANSFER_BANKS.EVOCABANK'),
        ]);
    }

    public function testCardToCardPaymentCreationSecondaryBank()
    {
        // mock arca services
        $mock = Mockery::mock(EvocaService::class)->makePartial();
        $mock->shouldReceive('checkCard')->andThrowExceptions([new \Exception()]);
        $this->app->bind(EvocaService::class, function () use ($mock) {
            return $mock;
        });

        $mock = Mockery::mock(ArmeconomService::class)->makePartial();
        $embossed_name = $this->loan->citizen->first_name_en.' '.$this->loan->citizen->last_name_en;
        $mock->shouldReceive('checkCard')->andReturn($embossed_name);
        $this->app->bind(ArmeconomService::class, function () use ($mock) {
            return $mock;
        });

        $payment_service = new PaymentService();
        $payment_service->createCardToCardPayment($this->loan, '****************', '29', '10');

        $this->assertDatabaseHas('card_to_card_payments', [
            'card_number' => '****************',
            'embossed_name' => $embossed_name,
            'year' => '29',
            'month' => '10',
            'bank' => constants('TRANSFER_BANKS.ARMECONOMBANK'),
        ]);
    }

    public function testCardToCardPaymentCreationPrimaryBankUnsupportedCards()
    {
        // mock arca services
        $mock = Mockery::mock(EvocaService::class)->makePartial();
        $embossed_name = $this->loan->citizen->first_name_en.' '.$this->loan->citizen->last_name_en;
        $mock->shouldReceive('checkCard')->andReturn($embossed_name);
        $this->app->bind(EvocaService::class, function () use ($mock) {
            return $mock;
        });

        $payment_service = new PaymentService();
        $payment_service->createCardToCardPayment($this->loan, '****************', '29', '10');

        $this->assertDatabaseHas('card_to_card_payments', [
            'card_number' => '****************',
            'embossed_name' => $embossed_name,
            'year' => '29',
            'month' => '10',
            'bank' => constants('TRANSFER_BANKS.EVOCABANK'),
        ]);
    }
}
