<?php

namespace Tests\Unit\OIWL;

use Tests\Unit\RuleEngineExecutorTest;

class RuleEngineScoreOIWLTest extends RuleEngineExecutorTest
{
    protected $loan_type = 'OIWL';

    public function dataProvider(): array
    {
        return [
            [[
                'rule' => 'dr_score_0_0.02_walletLoansCount_0',
                'context' => [
                    'input' => [
                        'drScore' => 0.01,
                        'walletLoansCount' => 0,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '40000',
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0_0.02_walletLoansCount_1_4',
                'context' => [
                    'input' => [
                        'drScore' => 0.01,
                        'walletLoansCount' => 1,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '150000',
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0_0.02_walletLoansCount_5',
                'context' => [
                    'input' => [
                        'drScore' => 0.01,
                        'walletLoansCount' => 5,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '150000',
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.02_0.05_walletLoansCount_0',
                'context' => [
                    'input' => [
                        'drScore' => 0.03,
                        'walletLoansCount' => 0,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '40000',
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.02_0.05_walletLoansCount_1_4',
                'context' => [
                    'input' => [
                        'drScore' => 0.03,
                        'walletLoansCount' => 2,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '100000',
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.02_0.05_walletLoansCount_5',
                'context' => [
                    'input' => [
                        'drScore' => 0.03,
                        'walletLoansCount' => 5,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '150000',
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.05_0.1_walletLoansCount_0',
                'context' => [
                    'input' => [
                        'drScore' => 0.06,
                        'walletLoansCount' => 0,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '20000',
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.05_0.1_walletLoansCount_1_4',
                'context' => [
                    'input' => [
                        'drScore' => 0.06,
                        'walletLoansCount' => 1,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '80000',
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.05_0.1_walletLoansCount_5',
                'context' => [
                    'input' => [
                        'drScore' => 0.06,
                        'walletLoansCount' => 5,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '100000',
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.1_0.165_walletLoansCount_0',
                'context' => [
                    'input' => [
                        'drScore' => 0.12,
                        'walletLoansCount' => 0,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '10000',
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.1_0.165_walletLoansCount_1_4',
                'context' => [
                    'input' => [
                        'drScore' => 0.12,
                        'walletLoansCount' => 1,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '80000',
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.1_0.165_walletLoansCount_5',
                'context' => [
                    'input' => [
                        'drScore' => 0.12,
                        'walletLoansCount' => 5,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '100000',
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.165_0.4_walletLoansCount_0',
                'context' => [
                    'input' => [
                        'drScore' => 0.166,
                        'walletLoansCount' => 0,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '10000',
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_lower_0_greater_0.4_newCustomer',
                'context' => [
                    'input' => [
                        'drScore' => 0.5,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '0',
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_lower_0_greater_0.165_oldCustomer',
                'context' => [
                    'input' => [
                        'drScore' => 0.166,
                        'walletLoansCount' => 1,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '0',
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => null,
                    ],
                ],
            ]],
        ];
    }
}
