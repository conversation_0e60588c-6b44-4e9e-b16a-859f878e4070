<?php

namespace Tests\Unit\VLX;

use Tests\Unit\RuleEngineExecutorTest;

class RuleEngineBaseRulesVLXTest extends RuleEngineExecutorTest
{
    protected $loan_type = 'VLX';

    public function dataProvider(): array
    {
        return [
            [[
                'rule' => 'osmAmount',
                'context' => [
                    'input' => [
                        'osmAmount' => 1,
                    ],
                ],
                'output' => [
                    'osmAmount' => [
                        'amount' => 1,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dstiAmount',
                'context' => [
                    'input' => [
                        'dstiAmount' => 1,
                    ],
                ],
                'output' => [
                    'dstiAmount' => [
                        'amount' => 1,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'isDead',
                'context' => [
                    'input' => [
                        'isDead' => true,
                    ],
                ],
                'output' => [
                    'isDead' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'age_salary_0',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'salary' => 0,
                    ],
                ],
                'output' => [
                    'age' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'age_salary_0',
                'context' => [
                    'input' => [
                        'age' => 20,
                        'salary' => 0,
                    ],
                ],
                'output' => [
                    'age' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'age',
                'context' => [
                    'input' => [
                        'age' => 20,
                        'salary' => 150000,
                    ],
                ],
                'output' => [
                    'age' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'age',
                'context' => [
                    'input' => [
                        'age' => 66,
                        'salary' => 150000,
                    ],
                ],
                'output' => [
                    'age' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'citizenship',
                'context' => [
                    'input' => [
                        'citizenship' => 'not ARM',
                    ],
                ],
                'output' => [
                    'citizenship' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'hasAddress',
                'context' => [
                    'input' => [
                        'hasAddress' => false,
                    ],
                ],
                'output' => [
                    'hasAddress' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'loanClass',
                'context' => [
                    'input' => [
                        'loanClass' => 'not Ստանդարտ',
                    ],
                ],
                'output' => [
                    'loanClass' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'hasOverdueLoans',
                'context' => [
                    'input' => [
                        'hasOverdueLoans' => true,
                    ],
                ],
                'output' => [
                    'hasOverdueLoans' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'acraActiveLoans',
                'context' => [
                    'input' => [
                        'isNewOclCustomer' => true,
                        'acraActiveLoans' => 1000,
                    ],
                ],
                'output' => [
                    'acraActiveLoans' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'isValidLoanClass12',
                'context' => [
                    'input' => [
                        'salary' => 0,
                        'isValidLoanClass12' => false,
                    ],
                ],
                'output' => [
                    'isValidLoanClass12' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'acraStatus',
                'context' => [
                    'input' => [
                        'salary' => 0,
                        'acraStatus' => 2,
                    ],
                ],
                'output' => [
                    'acraStatus' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'hasCredit',
                'context' => [
                    'input' => [
                        'hasCredit' => true,
                    ],
                ],
                'output' => [
                    'hasCredit' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
        ];
    }
}
