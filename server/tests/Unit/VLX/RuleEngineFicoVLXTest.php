<?php

namespace Tests\Unit\VLX;

use Tests\Unit\RuleEngineExecutorTest;

class RuleEngineFicoVLXTest extends RuleEngineExecutorTest
{
    protected $loan_type = 'VLX';

    public function dataProvider(): array
    {
        return [
            // start values
            [[
                'rule' => 'fico_501_549_age_21_25_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 501,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_26_30_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 501,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_31_40_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 501,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '500000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_41_50_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 501,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_51_65_oldOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 501,
                        'salary' => 15000,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '300000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_51_65_oldOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 501,
                        'salary' => 0,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_21_25_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 550,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_26_30_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 550,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '500000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_31_40_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 550,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '600000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_41_50_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 550,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '500000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_51_65_oldOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 550,
                        'salary' => 150000,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '300000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_51_65_oldOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 550,
                        'salary' => 0,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_21_25_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 600,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '500000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_26_30_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 600,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '650000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_31_40_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 600,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '700000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_41_50_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 600,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '500000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_51_65_oldOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 600,
                        'salary' => 150000,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '300000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_51_65_oldOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 600,
                        'salary' => 0,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_21_25_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 650,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '500000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_26_30_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 650,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '800000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_31_40_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 650,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '800000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_41_50_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 650,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '700000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_51_65_oldOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 650,
                        'salary' => 150000,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_51_65_oldOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 650,
                        'salary' => 0,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_21_25_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 700,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '600000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_26_30_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 700,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '1000000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_31_40_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 700,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '900000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_41_50_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 700,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '800000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_51_65_oldOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 700,
                        'salary' => 150000,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_51_65_oldOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 700,
                        'salary' => 0,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_21_25_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 750,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '700000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_26_30_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 750,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '1400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_31_40_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 750,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '1000000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_41_50_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 42,
                        'fico' => 750,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '900000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_51_65_oldOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 750,
                        'salary' => 150000,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '500000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_51_65_oldOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 750,
                        'salary' => 0,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_21_25_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 501,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '300000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_26_30_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 501,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '300000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_31_40_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 501,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_41_50_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 501,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '300000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 501,
                        'salary' => 150000,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '200000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 501,
                        'salary' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_21_25_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 550,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '300000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_26_30_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 550,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_31_40_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 550,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '500000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_41_50_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 550,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 550,
                        'salary' => 150000,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '200000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 550,
                        'salary' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_21_25_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 600,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_26_30_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 600,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_31_40_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 600,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '600000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_41_50_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 600,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 600,
                        'salary' => 150000,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '200000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 600,
                        'salary' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 600,
                        'salary' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_21_25_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 650,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_26_30_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 650,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '500000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_31_40_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 650,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '700000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_41_50_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 650,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '500000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 650,
                        'salary' => 150000,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '300000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 650,
                        'salary' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_21_25_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 700,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '500000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_26_30_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 700,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '600000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_31_40_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 700,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '800000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_41_50_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 700,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '600000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 700,
                        'salary' => 150000,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '300000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 700,
                        'salary' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_21_25_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 750,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '600000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_26_30_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 750,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '700000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_31_40_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 750,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '900000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_41_50_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 750,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '700000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 750,
                        'salary' => 150000,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 750,
                        'salary' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],

            // end values
            [[
                'rule' => 'fico_501_549_age_21_25_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'fico' => 549,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_26_30_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'fico' => 549,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_31_40_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'fico' => 549,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '500000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_41_50_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'fico' => 549,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_51_65_oldOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 549,
                        'salary' => 15000,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '300000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_51_65_oldOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 549,
                        'salary' => 0,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_21_25_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'fico' => 599,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_26_30_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'fico' => 599,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '500000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_31_40_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'fico' => 599,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '600000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_41_50_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'fico' => 599,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '500000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_51_65_oldOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 599,
                        'salary' => 150000,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '300000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_51_65_oldOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 599,
                        'salary' => 0,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_21_25_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'fico' => 649,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '500000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_26_30_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'fico' => 649,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '650000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_31_40_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'fico' => 649,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '700000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_41_50_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'fico' => 649,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '500000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_51_65_oldOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 649,
                        'salary' => 150000,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '300000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_51_65_oldOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 649,
                        'salary' => 0,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_21_25_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'fico' => 699,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '500000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_26_30_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'fico' => 699,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '800000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_31_40_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'fico' => 699,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '800000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_41_50_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'fico' => 699,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '700000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_51_65_oldOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 699,
                        'salary' => 150000,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_51_65_oldOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 699,
                        'salary' => 0,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_21_25_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'fico' => 749,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '600000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_26_30_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'fico' => 749,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '1000000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_31_40_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'fico' => 749,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '900000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_41_50_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'fico' => 749,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '800000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_51_65_oldOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 56,
                        'fico' => 749,
                        'salary' => 150000,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_51_65_oldOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 749,
                        'salary' => 0,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_21_25_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'fico' => 750,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '700000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_26_30_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'fico' => 750,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '1400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_31_40_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'fico' => 750,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '1000000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_41_50_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'fico' => 750,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '900000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_51_65_oldOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 750,
                        'salary' => 150000,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '500000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_51_65_oldOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 750,
                        'salary' => 0,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_21_25_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'fico' => 549,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '300000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_26_30_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'fico' => 549,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '300000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_31_40_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'fico' => 549,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_41_50_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'fico' => 549,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '300000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 549,
                        'salary' => 150000,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '200000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 549,
                        'salary' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_21_25_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'fico' => 599,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '300000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_26_30_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'fico' => 599,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_31_40_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'fico' => 599,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '500000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_41_50_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'fico' => 599,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 599,
                        'salary' => 150000,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '200000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 599,
                        'salary' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_21_25_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'fico' => 649,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_26_30_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'fico' => 649,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_31_40_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'fico' => 649,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '600000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_41_50_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'fico' => 649,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 649,
                        'salary' => 150000,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '200000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 649,
                        'salary' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_21_25_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'fico' => 699,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_26_30_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'fico' => 699,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '500000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_31_40_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'fico' => 699,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '700000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_41_50_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'fico' => 699,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '500000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 699,
                        'salary' => 150000,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '300000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 699,
                        'salary' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_21_25_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'fico' => 749,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '500000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_26_30_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'fico' => 749,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '600000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_31_40_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'fico' => 749,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '800000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_41_50_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'fico' => 749,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '600000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 749,
                        'salary' => 15000,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '300000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 749,
                        'salary' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_21_25_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'fico' => 750,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '600000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_26_30_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'fico' => 750,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '700000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_31_40_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'fico' => 750,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '900000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_41_50_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'fico' => 750,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '700000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 750,
                        'salary' => 150000,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 36,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 750,
                        'salary' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
        ];
    }
}
