<?php

namespace Tests\Unit\Calculator;

use App\Calculator\LoanCalculatorOASLForgive;
use App\Helpers\ArrayHelper;
use App\TestCase;
use Carbon\Carbon;

class LoanCalculatorOASLForgiveTest extends TestCase
{
    public function testForgiveFees()
    {
        $fake_now = Carbon::create(2020, 7, 21, 0);
        Carbon::setTestNow($fake_now);

        $calculator = new LoanCalculatorOASLForgive(420000, 11, 0, null);
        $actual = $calculator->generateSchedule();

        // Check first item in the schedule
        $first = ArrayHelper::pick($actual[0], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($first, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 0,
            'service_fee' => 0,
            'base' => 1861.66,
            'balance' => 418138.34,
            'payment' => 1861.66,
        ]);

        $second = ArrayHelper::pick($actual[1], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($second, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 0,
            'service_fee' => 0,
            'base' => 1879.06,
            'balance' => 416259.28,
            'payment' => 1879.06,
        ]);

        $third = ArrayHelper::pick($actual[2], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($third, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 0,
            'service_fee' => 0,
            'base' => 2022.06,
            'balance' => 414237.22,
            'payment' => 2022.06,
        ]);

        $fourth = ArrayHelper::pick($actual[3], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($fourth, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 0,
            'service_fee' => 0,
            'base' => 1915.5,
            'balance' => 412321.72,
            'payment' => 1915.5,
        ]);

        $fifth = ArrayHelper::pick($actual[4], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($fifth, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 0,
            'service_fee' => 0,
            'base' => 2057.66,
            'balance' => 410264.06,
            'payment' => 2057.66,
        ]);

        $sixth = ArrayHelper::pick($actual[5], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($sixth, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 0,
            'service_fee' => 0,
            'base' => 1952.62,
            'balance' => 408311.44,
            'payment' => 1952.62,
        ]);

        $seventh = ArrayHelper::pick($actual[6], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($seventh, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3814.64,
            'service_fee' => 3814.64,
            'base' => 1973.24,
            'balance' => 406338.2,
            'payment' => 5787.88,
        ]);

        // Check last item in the schedule
        $last = ArrayHelper::pick($actual[119], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($last, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 52.23,
            'service_fee' => 52.23,
            'base' => 5777.5,
            'balance' => 0,
            'payment' => 5829.73,
        ]);
    }

    public function testForgiveFeesKfw()
    {
        $fake_now = Carbon::create(2020, 7, 21, 0);
        Carbon::setTestNow($fake_now);

        $calculator = new LoanCalculatorOASLForgive(420000, 9.5, 0, null);
        $actual = $calculator->generateSchedule();

        // Check first item in the schedule
        $first = ArrayHelper::pick($actual[0], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($first, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 0,
            'service_fee' => 0,
            'base' => 2047.9,
            'balance' => 417952.1,
            'payment' => 2047.9,
        ]);

        $second = ArrayHelper::pick($actual[1], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($second, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 0,
            'service_fee' => 0,
            'base' => 2064.43,
            'balance' => 415887.67,
            'payment' => 2064.43,
        ]);

        // Check last item in the schedule
        $last = ArrayHelper::pick($actual[119], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($last, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 42.12,
            'service_fee' => 42.12,
            'base' => 5394.67,
            'balance' => 0,
            'payment' => 5436.79,
        ]);
    }
}
