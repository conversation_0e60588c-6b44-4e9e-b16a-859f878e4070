<?php

namespace Tests\Unit\Calculator;

use App\Calculator\LoanCalculatorOASL;
use App\Helpers\ArrayHelper;
use App\PHPUnitUtil;
use App\TestCase;
use Carbon\Carbon;

class LoanCalculatorOASLTest extends TestCase
{
    public function testCalculateUnadjustedScheduleForFor420000ProductWith11Rate()
    {
        $fake_now = Carbon::create(2020, 9, 13, 0);
        Carbon::setTestNow($fake_now);

        $calculator = new LoanCalculatorOASL(420000, 11, 0, null);
        $generateScheduleWithOffset = PHPUnitUtil::getPrivateMethod($calculator, 'generateScheduleWithOffset');
        $actual = $generateScheduleWithOffset->invoke($calculator);

        // Check first item in the schedule
        $first = ArrayHelper::pick($actual[0], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($first, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3797.26,
            'service_fee' => 3797.26,
            'base' => 0.0,
            'balance' => 420000.0,
            'payment' => 3797.26,
        ]);

        $second = ArrayHelper::pick($actual[1], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($second, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3923.84,
            'service_fee' => 3923.84,
            'base' => 0.0,
            'balance' => 420000.0,
            'payment' => 3923.84,
        ]);

        $third = ArrayHelper::pick($actual[2], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($third, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3797.26,
            'service_fee' => 3797.26,
            'base' => 0.0,
            'balance' => 420000.0,
            'payment' => 3797.26,
        ]);

        $fourth = ArrayHelper::pick($actual[3], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($fourth, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3923.84,
            'service_fee' => 3923.84,
            'base' => 0.0,
            'balance' => 420000.0,
            'payment' => 3923.84,
        ]);

        $fifth = ArrayHelper::pick($actual[4], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($fifth, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3923.84,
            'service_fee' => 3923.84,
            'base' => 0.0,
            'balance' => 420000.0,
            'payment' => 3923.84,
        ]);

        $sixth = ArrayHelper::pick($actual[5], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($sixth, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3544.11,
            'service_fee' => 3544.11,
            'base' => 0.0,
            'balance' => 420000.0,
            'payment' => 3544.11,
        ]);

        $seventh = ArrayHelper::pick($actual[6], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($seventh, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3923.84,
            'service_fee' => 3923.84,
            'base' => 2030.11,
            'balance' => 417969.89,
            'payment' => 5953.95,
        ]);

        // Check last item in the schedule
        $last = ArrayHelper::pick($actual[119], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($last, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 60.93,
            'service_fee' => 60.93,
            'base' => 5893.02,
            'balance' => 628.79,
            'payment' => 5953.95,
        ]);
    }

    public function testAdjustScheduleForPositiveLastMonthBalances()
    {
        $fake_now = Carbon::create(2020, 9, 30, 0);
        Carbon::setTestNow($fake_now);

        $calculator = new LoanCalculatorOASL(420000, 11, 0, null);
        $actual = $calculator->generateSchedule();

        // Check first item in the schedule
        $first = ArrayHelper::pick($actual[0], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($first, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3797.26,
            'service_fee' => 3797.26,
            'base' => 0,
            'balance' => 420000.0,
            'payment' => 3797.26,
        ]);

        $second = ArrayHelper::pick($actual[1], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($second, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3923.84,
            'service_fee' => 3923.84,
            'base' => 0.0,
            'balance' => 420000.0,
            'payment' => 3923.84,
        ]);

        $third = ArrayHelper::pick($actual[2], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($third, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3797.26,
            'service_fee' => 3797.26,
            'base' => 0.0,
            'balance' => 420000.0,
            'payment' => 3797.26,
        ]);

        $fourth = ArrayHelper::pick($actual[3], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($fourth, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3923.84,
            'service_fee' => 3923.84,
            'base' => 0.0,
            'balance' => 420000.0,
            'payment' => 3923.84,
        ]);

        $fifth = ArrayHelper::pick($actual[4], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($fifth, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3670.68,
            'service_fee' => 3670.68,
            'base' => 0.0,
            'balance' => 420000.0,
            'payment' => 3670.68,
        ]);

        $sixth = ArrayHelper::pick($actual[5], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($sixth, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3797.26,
            'service_fee' => 3797.26,
            'base' => 0.0,
            'balance' => 420000.0,
            'payment' => 3797.26,
        ]);

        $seventh = ArrayHelper::pick($actual[6], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($seventh, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3923.84,
            'service_fee' => 3923.84,
            'base' => 2033.02,
            'balance' => 417966.98,
            'payment' => 5956.86,
        ]);

        $eighth = ArrayHelper::pick($actual[7], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($eighth, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3778.88,
            'service_fee' => 3778.88,
            'base' => 2177.98,
            'balance' => 415789.0,
            'payment' => 5956.86,
        ]);

        $last = ArrayHelper::pick($actual[119], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($last, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 55.13,
            'service_fee' => 55.13,
            'base' => 5900.9,
            'balance' => 0,
            'payment' => 5956.03,
        ]);
    }

    public function testAdjustScheduleForNegativeLastMonthBalances()
    {
        $fake_now = Carbon::create(2020, 7, 21, 0);
        Carbon::setTestNow($fake_now);

        $calculator = new LoanCalculatorOASL(420000, 11, 0, null);
        $actual = $calculator->generateSchedule();

        // Check first item in the schedule
        $first = ArrayHelper::pick($actual[0], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($first, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3923.84,
            'service_fee' => 3923.84,
            'base' => 0.0,
            'balance' => 420000.0,
            'payment' => 3923.84,
        ]);

        $second = ArrayHelper::pick($actual[1], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($second, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3923.84,
            'service_fee' => 3923.84,
            'base' => 0.0,
            'balance' => 420000.0,
            'payment' => 3923.84,
        ]);

        $third = ArrayHelper::pick($actual[2], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($third, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3797.26,
            'service_fee' => 3797.26,
            'base' => 0.0,
            'balance' => 420000.0,
            'payment' => 3797.26,
        ]);

        $fourth = ArrayHelper::pick($actual[3], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($fourth, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3923.84,
            'service_fee' => 3923.84,
            'base' => 0.0,
            'balance' => 420000.0,
            'payment' => 3923.84,
        ]);

        $fifth = ArrayHelper::pick($actual[4], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($fifth, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3797.26,
            'service_fee' => 3797.26,
            'base' => 0.0,
            'balance' => 420000.0,
            'payment' => 3797.26,
        ]);

        $sixth = ArrayHelper::pick($actual[5], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($sixth, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3923.84,
            'service_fee' => 3923.84,
            'base' => 0.0,
            'balance' => 420000.0,
            'payment' => 3923.84,
        ]);

        $seventh = ArrayHelper::pick($actual[6], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($seventh, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3923.84,
            'service_fee' => 3923.84,
            'base' => 2029.94,
            'balance' => 417970.06,
            'payment' => 5953.78,
        ]);

        $eighth = ArrayHelper::pick($actual[7], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($eighth, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 3526.98,
            'service_fee' => 3526.98,
            'base' => 2426.8,
            'balance' => 415543.26,
            'payment' => 5953.78,
        ]);

        $last = ArrayHelper::pick($actual[119], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($last, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 53.35,
            'service_fee' => 53.35,
            'base' => 5901.12,
            'balance' => 0,
            'payment' => 5954.47,
        ]);
    }

    public function testCalculateUnadjustedScheduleForForKfw()
    {
        $fake_now = Carbon::create(2020, 7, 21, 0);
        Carbon::setTestNow($fake_now);

        $calculator = new LoanCalculatorOASL(420000, 12, 0, null);
        $generateScheduleWithOffset = PHPUnitUtil::getPrivateMethod($calculator, 'generateScheduleWithOffset');
        $actual = $generateScheduleWithOffset->invoke($calculator);

        // Check first item in the schedule
        $first = ArrayHelper::pick($actual[0], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($first, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 4280.55,
            'service_fee' => 4280.55,
            'base' => 0.0,
            'balance' => 420000.0,
            'payment' => 4280.55,
        ]);

        $second = ArrayHelper::pick($actual[1], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($second, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 4280.55,
            'service_fee' => 4280.55,
            'base' => 0.0,
            'balance' => 420000.0,
            'payment' => 4280.55,
        ]);

        // Check last item in the schedule
        $last = ArrayHelper::pick($actual[119], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($last, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 60.01,
            'service_fee' => 60.01,
            'base' => 6131.35,
            'balance' => 0,
            'payment' => 6191.36,
        ]);
    }

    public function testCalculateAdjustedScheduleForForKfw()
    {
        $fake_now = Carbon::create(2020, 7, 21, 0);
        Carbon::setTestNow($fake_now);

        $calculator = new LoanCalculatorOASL(420000, 12, 0, null);
        $actual = $calculator->generateSchedule();

        // Check first item in the schedule
        $first = ArrayHelper::pick($actual[0], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($first, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 4280.55,
            'service_fee' => 4280.55,
            'base' => 0.0,
            'balance' => 420000.0,
            'payment' => 4280.55,
        ]);

        $second = ArrayHelper::pick($actual[1], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($second, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 4280.55,
            'service_fee' => 4280.55,
            'base' => 0.0,
            'balance' => 420000.0,
            'payment' => 4280.55,
        ]);

        // Check last item in the schedule
        $last = ArrayHelper::pick($actual[119], ['service_fee_plain', 'service_fee_interest', 'service_fee', 'base', 'balance', 'payment']);
        $this->assertEquals($last, [
            'service_fee_plain' => 0,
            'service_fee_interest' => 60.46,
            'service_fee' => 60.46,
            'base' => 6129.78,
            'balance' => 0,
            'payment' => 6190.24,
        ]);
    }
}
