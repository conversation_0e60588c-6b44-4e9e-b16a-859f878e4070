<?php

namespace Tests\Unit\OEPL;

use Tests\Unit\RuleEngineExecutorTest;

class RuleEngineBaseRulesOEPLTest extends RuleEngineExecutorTest
{
    protected $loan_type = 'OEPL';

    public function dataProvider(): array
    {
        return [
            [[
                'rule' => 'isDead',
                'context' => [
                    'input' => [
                        'isDead' => true,
                    ],
                ],
                'output' => [
                    'isDead' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'age',
                'context' => [
                    'input' => [
                        'age' => 99,
                        'walletLoansCount' => 0,
                    ],
                ],
                'output' => [
                    'age' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'citizenship',
                'context' => [
                    'input' => [
                        'citizenship' => 'not ARM',
                    ],
                ],
                'output' => [
                    'citizenship' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'hasAddress',
                'context' => [
                    'input' => [
                        'hasAddress' => false,
                    ],
                ],
                'output' => [
                    'hasAddress' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'loanClass',
                'context' => [
                    'input' => [
                        'loanClass' => 'not Ստանդարտ',
                    ],
                ],
                'output' => [
                    'loanClass' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'hasOverdueLoans',
                'context' => [
                    'input' => [
                        'hasOverdueLoans' => true,
                    ],
                ],
                'output' => [
                    'hasOverdueLoans' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'acraStatus',
                'context' => [
                    'input' => [
                        'salary' => 0,
                        'acraStatus' => 2,
                    ],
                ],
                'output' => [
                    'acraStatus' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'hasCredit',
                'context' => [
                    'input' => [
                        'hasCredit' => true,
                    ],
                ],
                'output' => [
                    'hasCredit' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => null,
                    ],
                ],
            ]],
        ];
    }
}
