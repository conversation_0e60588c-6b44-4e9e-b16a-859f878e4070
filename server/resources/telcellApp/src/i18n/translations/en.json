{"header": {"title": "GLOBAL CREDIT", "menu": {"item": "Personal credits"}}, "renovation_screen": {"title": "The site is being updated, please try again later."}, "document_number": {"mortgage_loan": "mortgage loan"}, "loan": {"internal_server_error_text": "There is a problem at the moment. Please try again later.", "steps": {"title": "Step {{0}}", "loan_amount": {"passport": "Passport/ID", "born": "Born", "social_card": "Social card", "id_card": "ID card", "vehicle_pledge_text": "To get more money you can pledge your vehicle", "vehicle_available_amount": "Your available amount will be", "pledge": "<PERSON><PERSON>", "available": "Your available amount is", "until": "until", "loan_value": "<PERSON><PERSON>", "loan_time": "Term of Loan", "monthly_payment": "Monthly Payment", "last_month_payment": "Last month's payment", "payment_value": "Full amount", "payment_day": "Next payment date", "description": "Loan amount", "description_ovl": "Loan amount OVL", "rejection_text": "Your loan application has not been approved by the automated system.", "rejection_text_reason": "Reasons for rejection may be", "rejection_text_0": "Loan overload", "rejection_text_1": "Insufficient credit history", "rejection_text_2": "Age mismatch", "rejection_text_3": "Non-compliance with other lending criteria.", "next": "Approve", "previous": "return", "months": "months", "interest_rate": "Interest rate", "annual_rate": "Annual rate", "withdrawal_fee": "Providing amount", "vehicle": "Vehicle", "vehicle_number": "Vehicle number", "product_name": "Product", "product_amount": "Amount", "term_name": "Term", "term_rate": "Term Rate", "term_duration": "Term Duration", "address": "Address", "loan_unavailable": "We are sorry to inform you that we are not able to provide you a loan ...", "apr_approval_checkbox": "I realize the annual interest rate.", "apr_approval_input": "Please enter {{0}}, which is your annual interest rate.", "apr_rate": "Annual rate", "pledge_vehicle_title": "Pledge vehicle title", "loan_info_title": "Loan info title", "available_max_amount": "Available max amount"}, "personal_info": {"phone_number": "Phone Number", "additional_phone_number": "Additional Phone Number", "email": "Email", "email_example": "<EMAIL>", "first_name": "Name", "country": "Country", "last_name": "Last name", "birthdate": "Birthdate", "city": "City", "social_card": "Social card number", "evaluation_company": "Evaluation company", "address": "Address", "confirmation_text": "I acknowledge that the above mentioned personal information, the mobile phone number and e-mail address are my personal data and I acknowledge that GLOBAL CREDIT CJSC has the right to provide me with information that does not correspond to my reality or use third-party information to be subjected to liability in accordance with the established procedure.", "description": "Personal information", "next": "Next", "previous": "return", "notification": {"email": "Email", "in_person": "In person", "post": "Postal service", "method": "Notification mode"}, "dispute_solution": {"gnm_arbitration": "GNM arbitration court", "optimum": "Optimus Lex LTD", "court": "<PERSON><PERSON> in court", "arbitration": "UBA Financial arbitration", "method": "Dispute solution method"}}, "sms_validation": {"description": "Confirmation", "confirmation_text": "I acknowledge that the requested information is relevant to me and I am aware that in the absence of appropriate grounds, I may be liable for legally binding obligations and will not disclose to third parties, except to the extent that they are covered by the Bureau's rules of insurance compensation or when applying for CIVL history review, as well as for the person (as regards the information) for other purposes authorized in writing.", "agreement_text": "By this, I give my consent that This agreement is concluded remotely by exchanging information (messages). The contract is considered signed in absentia by me and CJSC Global Credit UCO by accepting this condition from me and confirming the provision of a loan with the contract. I accept the terms of the loan agreement completely and irreversibly, as well as I agree that, by signing this loan agreement in the manner defined in the contract, this loan agreement is considered to be a valid contract in writing between me and Global Credit UCO CJSC", "next": "Approve", "previous": "return", "middle_text": "Enter Confirmation Text", "phone_number": "An SMS with a verification code will be send to your <0>{{phoneNumber}}", "button_text": "Get code", "resend_button_text": "Resend Code", "timer_text": "Session ends in $$timer minutes", "timer_expired_text": "Code expired. Please click Resend Code to get new one.", "get_code_error": "Can't get code. Please try again.", "code_validation_error": "Invalid Code", "ovl_pdf_confirmation_text": "I accept <0>personal sheet</0>, <1>loan contract</1>, <2>mortgage contract</2> and <3>what to do</3>"}, "car_verification": {"description": "Car Verification", "next": "Next", "previous": "return", "info": "Our specialist will gladly arrive at your preferred time and place for the car inspection", "first_section_title": "Select your preferred time", "first_section_sub_title": "Preferred time", "second_section_title": "Location of Inspection", "third_section_title": "Notes", "today": "Today", "tomorrow": "Tomorrow", "day_after_tomorrow": "In 2 days", "address": "Address", "time": "Available hours", "wrong_address": "Please choose an address in Yerevan"}, "confirmation": {"title": "Confirmations", "end_of_payment": "loan payment is over", "selected_amount": "Dedicated amount", "days": "days", "months": "months", "procents": "Percentage sum", "show_more": "Show more", "show_less": "Show less", "offices": "Global Credit Offices", "contract": "loan identification document.pdf", "cash_payment": "You can get the amount from the {{cashOffices}} branch by submitting a personal identification document. Please note that the amount should be deducted within 7 business days.", "idram_wallet": "Money successfully transfered to your IDram wallet.", "easypay_wallet": "Money successfully transfered to your EasyPay wallet.", "card_to_card": "Money successfully transfered to your card.", "wire_transfer": "Your money will be credited within one business day and will be available for the next two business days. You can get the money from any branch of 'Ararat' bank.", "moderation": "Your money will be transfered within 15 minutes", "rejection": "We are sorry to inform you that currently we are not able to provide you a loan.", "product_provision": "ԻՔՈՍդ բարով մաշես", "documents_loading_text": "Your loan approved. We are generating documents. Please wait.", "documents_error_text": "Document generation failed.", "confirmed_failed_payment": "Your loan approved, but we not able to provide money in your selected way. You can get the amount from the 'UPay' branch by submitting a personal identification document.", "ovl_confirmed": "Dear customer, when signing contracts, please ensure the availability of the car, and also carry the following documents. Passport, Social Card, Vehicle Technical Passport, Certificate of car ownership", "ovl_trade_confirmed": "Dear customer, when signing contracts, please ensure the availability of the car, and also carry the following documents. Passport, Social Card, Vehicle Technical Passport, Certificate of car ownership", "loan_documents": {"personal_sheet_private": "Personal sheet", "personal_sheet": "Personal sheet", "contract_ovl_private": "Loan contract", "contract_ovl": "Loan contract", "contract_ovl_trade_private": "Loan contract", "contract_ovl_trade": "Loan contract", "application_private": "Application", "application": "Application", "mortgage_contract_private": "Mortgage contract", "mortgage_contract": "Mortgage contract", "mortgage_contract_trade_private": "Mortgage contract", "mortgage_contract_trade": "Mortgage contract", "power_of_attorney": "Power of attorney", "statement": "Statement", "statement_oasl": "Statement", "agreement": "Agreement", "what_to_do": "What to do", "arbitration": "Arbitration", "gnm_arbitration": "GNM arbitration", "optimuslex": "Optimus Lex arbitration", "uba_arbitration": "UBA arbitration", "agreement_oasl": "Agreement 1", "acra_agreement_oasl": "Agreement 2"}, "office_open": "Open", "office_closed": "Closed", "office_working_hours": "Working hours", "offices_header": "You can get the amount from the Global Credit branch, or from more than 50 branches of our partners", "office_names": {"GlobalCredit": "GlobalCredit", "UPay": "UCom", "EasyPay": "EasyPay"}}}, "trade_vehicle": {"info_text": "Add vehicle", "buttons": {"own_vehicle": "Own name"}, "add": "Add", "cancel": "<PERSON><PERSON>", "amount": "Vehicle amount", "passport": "Vehicle tech passport"}}, "security_question": {"select_address": "Select your address"}, "identity_verification_failure": {"title": "Your identity verification failed", "go_back": "Go Back"}, "loan_types": {"OVL": "OVL"}, "validator": {"passport": "Invalid passport", "input_length": "Invalid input length", "terms": "Agree to the terms", "select_office": "Please select office", "captcha": "This field is required to complete", "code": "Code length must be 4", "apr_approval_input": "Invalid percentage", "apr_approval_input_required": "This field is required"}, "validation_errors": {"0001": "This field is required", "0002": "Invalid email", "0003": "Passport Number should start with 2 letters and 7 digits", "0004": "Only alphanumeric characters allowed", "0005": "The length must be {{0}}", "4001": "Can not download the citizen's data", "4002": "Your request has failed, please try again later", "4003": "Payment failed", "4004": "Invalid Code", "4005": "Invalid IDram wallet", "4006": "Invalid Card", "4007": "Invalid notification method", "4008": "Your request has failed, please try again later", "4009": "Invalid Easypay wallet", "4010": "Payment already done", "4011": "Can not download vehicle data", "4012": "Identification failed", "4013": "Your request has failed, please try again later", "4014": "Wrong email or password", "4015": "Can not download the citizen's data", "4016": "Credit card is not allowed", "4017": "Trade amount less then crediting amount", "4018": "Can not download the citizen's data", "4019": "Double action taken", "4020": "The camera could not identify your face. Please try again", "4021": "Face authentication is failed.", "4022": "There should be only one person behind the camera. Please try again.", "4023": "Please go through authentication again", "4024": "Please go through authentication again", "4025": "The camera could not identify your face.Please keep the camera close to your face", "4026": "Please go through authentication again", "4028": "Unable to process the request", "4029": "Please go through authentication again", "4031": "Please go through authentication again", "4032": "Unable to process the request", "4044": "The camera could not identify your face.Please keep the camera close to your face", "4052": "Phone numbers are the same", "4053": "Invalid document", "4054": "Unable to pledge this vehicle", "4057": "Something went wrong. Please check the url", "4058": "Check referral code", "4059": "You can not use Your Own referral code", "4065": "Please check passport validity date", "4066": "The loan application cannot be considered due to the lack of a social card", "4067": "Your application could not be processed by an automated system.", "4072": "Please check the availability of documents or their validity period", "4089": "Please select a new inspection time.", "4101": "Please select a new inspection time.", "4102": "Please select a new inspection time."}, "suggestions": {"title": "Suggestions", "interest_rate": "Interest Rate", "monthly_fee_2": "Amount to be paid monthly", "monthly_fee": "Monthly Fee", "description": "This is simply dummy text of the printing and typesetting industry. Lorem ipsum dolor sit amet", "button": "take", "month": "month", "terms": "Term of Loan"}, "weekdays": {"1": "Monday", "2": "Tuesday", "3": "Wednesday", "4": "Thursday", "5": "Friday", "6": "Saturday", "0": "Sunday"}, "terms_and_conditions": {"title": "Terms and conditions", "borrower": "<PERSON><PERSON><PERSON>", "age": "Borrower age", "currency": "Loan currency", "delivery_procedure": "Delivery procedure", "loan_amount": "Loan amount", "loan_term": "Loan term", "annual_rate": "Annual nominal interest rate", "apr": "Annual factual interest rate", "service_fee": "Loan service fee (monthly)", "loan_provision_fee": "Loan service fee (monthly)", "loan_payment": "Loan repayment", "loan_security": "Loan security", "loan_collateral_ratio": "Loan / collateral ratio", "arm_currency": "AMD", "withdrawal_fee": "Withdrawal fee", "penalties_fines": "Penalties, fines", "early_loan_repayment_penalty": "Early loan repayment penalty", "required_documents": "Required documents and necessary conditions", "loan_decision": "Making a loan decision", "decision_rejection_factors": "Making a loan decision"}, "terms_confirmation": {"button": "Approved", "warning_text": "Dear Customer, we inform you that this type of loan has a high actual interest rate, so first consider other similar services available in the market and evaluate your possibilities to repay the loan.", "agreement_checkbox_one": "I am informed that the number of credit applications can have a negative impact on the credit summary score\n", "agreement_checkbox_two": "I hereby give my consent for \"Global Credit\" UOC CJSC to request the information of the state and local self-governments, including the information containing personal data. I agree that the material obtained as a result of lending also contains personal data and can be processed by \"Global Credit\" UWC CJSC. Subject to <0>attached survey agreement</0>"}, "amount_selection_modal": {"description": "Please enter the required amount\n", "cancel": "Cancel", "confirm": "Confirm"}}