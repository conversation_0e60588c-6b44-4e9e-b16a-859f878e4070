import i18n from '../i18n';
import _ from 'lodash';
import moment from 'moment';

export const TOKEN = 'token';

export const SUUID = 'SUUID';

export const LOAN_TYPE = 'LOAN_TYPE';

export const LATEST_STEP = 'LATEST_STEP';

export const TIMER_EXP = 900; // 15 minutes in seconds

export const DOCUMENT_POLLING_INTERVAL = 5000; // 5 seconds

export const DOCUMENT_POLLING_TIMEOUT = 180000; // 3 minutes

export const CHECKUP_DATE_FORMAT = 'YYYY-MM-DD HH:mm:ss';

export const HOUR = 'hour';

export const APP_DATE_FORMAT = 'YYYY-MM-DD';

export const MAP_ZOOM = 12;
export const YEREVAN_INITIAL_LAT = 40.1792;
export const YEREVAN_INITIAL_LNG = 44.4991;

export const GLOBALCREDIT_LAT = 40.214106;
export const GLOBALCREDIT_LNG = 44.491185;

export const BANK_CODE_LENGTH = 3;
export const BANK_ACOUNT_NUM_MIN_LEN = 12;
export const BANK_ACOUNT_NUM_MAX_LEN = 16;

export const BIRTH_DATE_FORMAT = 'DD/MM/YYYY';

export const FORBIDDEN_ERROR_CODE = 403;

export const ARM_TIMEZONE = '+0400';

export const TIME_FORMAT = 'HH:mm';

export const VEHICLE_NUMBER_MASK = '99 aa 999';
export const TECH_PASSPORT_MASK = 'aa 999999';
export const PHONE_NUMBER_MASK = '(99) 999 999';

export const HIDDEN_PHONE_NUMBER = '+xxx xx xx xx xx';

export const TRANSFER_METHODS = [
  {
    id: 2,
    method: 'cash_payment',
    type: 'cash',
    serverType: 'App\\Models\\CashPayment',
    route: 'cash-transfer',
  },
];

export const CONFIRMED_NO_PAY = 'CONFIRMED_NO_PAY';

export const FINISHED = 'FINISHED';

export const REJECTED = 'REJECTED';

export const FINISHED_WITHOUT_MODERATION = 'FINISHED_WITHOUT_MODERATION';

export const FAILED_PAYMENT = 'FAILED';

export const LOAN_TYPES = {
  OVL: {
    id: 2,
    name: 'ovl',
    documents_count: 4,
    documents: {
      PERSONAL_SHEET: 'personal_sheet_private',
      CONTRACT: 'contract_ovl_private',
      CONTRACT_TRADE: 'contract_ovl_trade_private',
      MORTGAGE_CONTRACT: 'mortgage_contract_private',
      MORTGAGE_CONTRACT_TRADE: 'mortgage_contract_trade_private',
      WHAT_TO_DO: 'what_to_do',
    },
  },
};

export const NOTIFICATION_METHODS = [
  {
    id: 0,
    text: i18n.t('loan.steps.personal_info.notification.email'),
    value: 'email',
  },
  {
    id: 1,
    text: i18n.t('loan.steps.personal_info.notification.in_person'),
    value: 'in_person',
  },
  {
    id: 2,
    text: i18n.t('loan.steps.personal_info.notification.post'),
    value: 'post',
  },
];

export const DISPUTE_SOLUTION = [
  {
    id: 0,
    text: i18n.t('loan.steps.personal_info.dispute_solution.gnm_arbitration'),
    value: 'gnm_arbitration',
  },
  {
    id: 1,
    text: i18n.t('loan.steps.personal_info.dispute_solution.optimum'),
    value: 'optimum',
  },
  {
    id: 2,
    text: i18n.t('loan.steps.personal_info.dispute_solution.court'),
    value: 'court',
  },
  {
    id: 3,
    text: i18n.t('loan.steps.personal_info.dispute_solution.arbitration'),
    value: 'arbitration',
  },
];

export const ARM_PHONE_CODES = [
  '33',
  '41',
  '43',
  '44',
  '47',
  '49',
  '55',
  '77',
  '91',
  '93',
  '94',
  '95',
  '96',
  '97',
  '98',
  '99',
];

export const NUMERIC_MONTHS = _.times(12, index => {
  const month = _.padStart(index + 1, 2, '0');

  return {
    id: index,
    text: month,
    value: month,
  };
});

export const YEARS = _.times(21, index => {
  const year = moment().year() + index;

  return {
    id: index,
    text: year,
    value: year,
  };
});

export const EXPIRED_SUUID_ERROR_CODE = 4008;

export const FRACTION_NO = 0;

export const OFFICE_TIME_FORMAT = 'HH:mm:ss';

export const WORK_TIME_FORMAT = 'HH:mm';

export const UNSUPPORTED_BROWSERS = ['MIUI Browser'];

export const YEREVAN_COORDINATES = [
  { lng: 44.417859, lat: 40.242194 },
  { lng: 44.417859, lat: 40.24221 },
  { lng: 44.43333, lat: 40.252021 },
  { lng: 44.440175, lat: 40.256295 },
  { lng: 44.445325, lat: 40.25433 },
  { lng: 44.469872, lat: 40.24529 },
  { lng: 44.478455, lat: 40.246862 },
  { lng: 44.496995, lat: 40.250662 },
  { lng: 44.530125, lat: 40.257605 },
  { lng: 44.55021, lat: 40.259439 },
  { lng: 44.576302, lat: 40.261535 },
  { lng: 44.578706, lat: 40.255771 },
  { lng: 44.582997, lat: 40.246469 },
  { lng: 44.585744, lat: 40.238214 },
  { lng: 44.648287, lat: 40.198369 },
  { lng: 44.656193, lat: 40.193144 },
  { lng: 44.659792, lat: 40.190825 },
  { lng: 44.666981, lat: 40.186235 },
  { lng: 44.665809, lat: 40.183221 },
  { lng: 44.664291, lat: 40.178838 },
  { lng: 44.661985, lat: 40.172264 },
  { lng: 44.659117, lat: 40.164486 },
  { lng: 44.657296, lat: 40.159067 },
  { lng: 44.6504402160644, lat: 40.1523818969728 },
  { lng: 44.626014, lat: 40.125515 },
  { lng: 44.609688, lat: 40.118275 },
  { lng: 44.53437, lat: 40.084433 },
  { lng: 44.490794, lat: 40.11136 },
  { lng: 44.4611434936523, lat: 40.111338470459 },
  { lng: 44.440298, lat: 40.129738 },
  { lng: 44.425848, lat: 40.157997 },
  { lng: 44.431624, lat: 40.173799 },
  { lng: 44.436774, lat: 40.187758 },
  { lng: 44.437353, lat: 40.189373 },
  { lng: 44.438751, lat: 40.192963 },
  { lng: 44.416904, lat: 40.218741 },
];

export const PHONE_NUMBER = '(011) 700 100';
export const PHONE_NUMBER_2 = '(011) 700 200';

export const HOME_SCREEN_PDFS = {
  AGREEMENT: 'http://cashme.am/home_screen_pdfs/request_agreement.pdf',
};

export const ENTER_KEY = {
  NAME: 'Enter',
  CODE: 13,
};

export const REQUESTED_LOAN_TYPE = {
  FAST_MONEY: 'FAST_MONEY',
  PLEDGE: 'OVL_PLEDGE',
  TRADE: 'OVL_TRADE',
};
