import { ofType } from 'redux-observable';
import { mergeMap } from 'rxjs/operators';
import axios from '../../config/axios';

export const FETCH_APP_STATUS = 'globalcredit/app_configs/FETCH_APP_STATUS';
export const FETCH_APP_STATUS_SUCCESS =
  'globalcredit/app_configs/FETCH_APP_STATUS_SUCCESS';
export const FETCH_APP_STATUS_ERROR =
  'globalcredit/app_configs/FETCH_APP_STATUS_ERROR';

export const FETCH_SERVER_TIME = 'globalcredit/app_configs/FETCH_SERVER_TIME';
export const FETCH_SERVER_TIME_SUCCESS =
  'globalcredit/app_configs/FETCH_SERVER_TIME_SUCCESS';
export const FETCH_SERVER_TIME_ERROR =
  'globalcredit/app_configs/FETCH_SERVER_TIME_ERROR';

export const fetchAppStatus = payload => ({
  type: FETCH_APP_STATUS,
  payload,
});

export const fetchAppStatusFulfilled = payload => ({
  type: FETCH_APP_STATUS_SUCCESS,
  payload,
});

export const fetchAppStatusError = payload => ({
  type: FETCH_APP_STATUS_ERROR,
  payload,
});

export const fetchServerTime = () => ({
  type: FETCH_SERVER_TIME,
});

export const fetchServerTimeFulfilled = payload => ({
  type: FETCH_SERVER_TIME_SUCCESS,
  payload,
});

export const fetchServerTimeError = payload => ({
  type: FETCH_SERVER_TIME_ERROR,
  payload,
});

const initialState = {
  data: {},
  loading: false,
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case FETCH_APP_STATUS:
      return {
        ...state,
        appStatusLoading: true,
      };
    case FETCH_APP_STATUS_SUCCESS:
      return {
        ...state,
        data: {
          ...state.data,
          ...action.payload.data,
        },
        appStatusLoading: false,
      };
    case FETCH_APP_STATUS_ERROR:
      return {
        ...state,
        error: action.payload,
        appStatusLoading: false,
      };
    case FETCH_SERVER_TIME:
      return {
        ...state,
        loading: true,
      };
    case FETCH_SERVER_TIME_SUCCESS:
      return {
        ...state,
        data: {
          ...state.data,
          ...action.payload.data,
        },
        loading: false,
      };
    case FETCH_SERVER_TIME_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    default:
      return state;
  }
}

export const fetchAppStatusEpic = action$ =>
  action$.pipe(
    ofType(FETCH_APP_STATUS),
    mergeMap(action =>
      axios
        .get(`settings/app-status`, action.payload)
        .then(response => fetchAppStatusFulfilled(response.data))
        .catch(error =>
          fetchAppStatusError(error.response && error.response.data.error)
        )
    )
  );

export const fetchServerTimeEpic = action$ =>
  action$.pipe(
    ofType(FETCH_SERVER_TIME),
    mergeMap(() =>
      axios
        .get(`settings/server-time`)
        .then(response => fetchServerTimeFulfilled(response.data))
        .catch(error =>
          fetchServerTimeError(error.response && error.response.data.error)
        )
    )
  );
