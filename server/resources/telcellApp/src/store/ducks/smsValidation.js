import { ofType } from 'redux-observable';
import { mergeMap, catchError } from 'rxjs/operators';
import { of, from } from 'rxjs';
import { getSuuidHeader, removeSuuid } from '../../helpers/auth';
import { removeLatestStep, removeLoanType } from '../../helpers';
import axios from '../../config/axios';
import { wizardError, wizardErrorClear } from './wizard';

export const CODE_VALIDATION = 'globalcredit/sms/CODE_VALIDATION';
export const CODE_VALIDATED = 'globalcredit/sms/CODE_VALIDATED';
export const CODE_VALIDATION_ERROR = 'globalcredit/sms/CODE_VALIDATION_ERROR';

export const GET_CODE = 'globalcredit/sms/GET_CODE';
export const CODE_RECEIVED = 'globalcredit/sms/CODE_RECEIVED';
export const GET_CODE_ERROR = 'globalcredit/sms/GET_CODE_ERROR';

export const EXPIRE_CODE = 'globalcredit/sms/EXPIRE_CODE';
export const CODE_EXPIRED = 'globalcredit/sms/CODE_EXPIRED';
export const EXPIRE_CODE_ERROR = 'globalcredit/sms/EXPIRE_CODE_ERROR';

export const expireCode = () => ({
  type: EXPIRE_CODE,
});
export const expireCodeFulfilled = payload => ({
  type: CODE_EXPIRED,
  payload,
});
export const expireCodeError = payload => ({
  type: EXPIRE_CODE_ERROR,
  payload,
});

export const getCode = () => ({
  type: GET_CODE,
});
export const getCodeFulfilled = payload => ({
  type: CODE_RECEIVED,
  payload,
});
export const getCodeError = payload => ({
  type: GET_CODE_ERROR,
  payload,
});

export const validateCode = payload => ({
  type: CODE_VALIDATION,
  payload,
});
export const validateCodeFulfilled = payload => ({
  type: CODE_VALIDATED,
  payload,
});
export const validateCodeError = payload => ({
  type: CODE_VALIDATION_ERROR,
  payload,
});

const initialState = {
  loading: false,
  publicId: null,
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case GET_CODE:
      return {
        ...state,
        loading: true,
      };
    case CODE_RECEIVED:
      return {
        ...state,
        getError: null,
        validationError: null,
        data: action.payload.data,
        loading: false,
      };
    case GET_CODE_ERROR:
      return {
        ...state,
        getError: action.payload,
        loading: false,
      };

    case EXPIRE_CODE:
      return {
        ...state,
        loading: true,
      };
    case CODE_EXPIRED:
      return {
        ...state,
        data: null,
        getError: null,
        validationError: null,
        expirationError: null,
        loading: false,
      };
    case EXPIRE_CODE_ERROR:
      return {
        ...state,
        expirationError: action.payload,
        loading: false,
      };

    case CODE_VALIDATION:
      return {
        ...state,
        loading: true,
      };
    case CODE_VALIDATED:
      return {
        ...state,
        getError: null,
        validationError: null,
        publicId: action.payload.data.loan.publicId,
        loading: false,
      };
    case CODE_VALIDATION_ERROR:
      return {
        ...state,
        validationError: action.payload,
        loading: false,
      };
    default:
      return state;
  }
}

export const validateCodeEpic = action$ =>
  action$.pipe(
    ofType(CODE_VALIDATION),
    mergeMap(action =>
      from(
        axios.post('validate-code', action.payload, {
          headers: getSuuidHeader(),
        })
      ).pipe(
        mergeMap(response => {
          removeLoanType();
          removeLatestStep();
          return of(validateCodeFulfilled(response.data), wizardErrorClear());
        }),
        catchError(error =>
          of(
            wizardError(error.response.data.error),
            validateCodeError(error.response && error.response.data.error)
          )
        )
      )
    )
  );

export const getCodeEpic = action$ =>
  action$.pipe(
    ofType(GET_CODE),
    mergeMap(action =>
      from(
        axios.get('get-code', {
          headers: getSuuidHeader(),
        })
      ).pipe(
        mergeMap(response => {
          return of(getCodeFulfilled(response.data), wizardErrorClear());
        }),
        catchError(error =>
          of(
            wizardError(error.response.data.error),
            getCodeError(error.response && error.response.data.error)
          )
        )
      )
    )
  );

export const expireCodeEpic = action$ =>
  action$.pipe(
    ofType(EXPIRE_CODE),
    mergeMap(() =>
      from(
        axios.get('expire-validation-code', {
          headers: getSuuidHeader(),
        })
      ).pipe(
        mergeMap(response => {
          return of(expireCodeFulfilled(response.data), wizardErrorClear());
        }),
        catchError(error =>
          of(
            wizardError(error.response.data.error),
            expireCodeError(error.response && error.response.data.error)
          )
        )
      )
    )
  );
