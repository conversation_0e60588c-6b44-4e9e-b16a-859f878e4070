@import '../../styles/variables';
@import '../../styles/sizes';

.internal_server_error_container {
  text-align: center;
  margin-top: 35px;

  .image {
    width: 170px;
    height: 170px;
  }

  .error_msg {
    text-align: center;
    font-size: 17px;
    margin-top: 20px;
    padding: 10px;
    color: $pink;
  }
}

@media screen and (max-width: $tablet-width) and (min-width: $lowers-tablet-width) {
  .internal_server_error_container {
    margin-bottom: 30px;
  }
}

@media screen and (max-width: $lowers-tablet-width) {
  .internal_server_error_container {
    margin-top: 60px;

    .image {
      width: 150px;
      height: 150px;
    }

    .error_msg {
      margin-top: 0;
      margin-bottom: 50px;
    }
  }
}

@media screen and (max-width: $mobile-width) {
  .internal_server_error_container {
    .image {
      width: 140px;
      height: 140px;
    }
  }
}
