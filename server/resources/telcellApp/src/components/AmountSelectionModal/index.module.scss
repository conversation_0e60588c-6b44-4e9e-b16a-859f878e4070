@import '../../styles/variables';
@import '../../styles/sizes';

#amount_selection_modal {
  width: 40%;
  height: max-content;
  color: $wet-asphalt;
  padding: 25px 20px;
  border-radius: 16px;

  .modal_container {
    width: 100%;
    max-width: 100%;
    text-align: left;
    background-color: $white;
    border-radius: 16px;

    .currency_icon {
      display: flex;
      margin: 0 auto;
      width: 90px;
      height: 90px;
    }

    .description {
      font-size: 18px;
      color: $black;
      text-transform: uppercase;
      padding: 10px 20px 20px 20px;
      text-align: center;
    }

    :global .amount_selection_input_container {
      border-bottom: 1px solid $gray;
      margin: 10px 0 20px 0;
    }

    .amount_section {
      display: flex;
      flex-direction: column;

      input {
        font-size: 20px;
        font-weight: normal;
        height: 36px;
        border: none !important;
        font-family: $main-font-family;
      }

      input:focus {
        outline: none;
        border: none;
      }

      .amount_input {
        width: 100%;

        div:first-child {
          width: 45px;
          padding: 0 12px 0 0;
          border: none;
          position: relative;
        }
      }
    }
  }

  .confirm_btn {
    button {
      background-color: $telcell-orange;
    }
  }

  .radio_container {
    display: flex;
    flex-direction: column;
    margin-bottom: 14px;
  }

  .radio_title {
    color: $secondary !important;
    margin-bottom: 10px;
  }

  .radio_section {
    margin-bottom: 10px;
    input[type='radio'] ~ label::before {
      border-color: $web-orange !important;
    }
  }

  .input_title {
    color: $secondary !important;
  }

  .cancel_btn {
    button {
      background-color: transparent;
      border: 1px solid $main;
      margin-top: 10px;
      color: $main;
    }
  }

  @media screen and (max-width: $laptop-width) {
    width: 40%;
  }

  @media screen and (max-width: $lowers-tablet-width) {
    width: 100%;
    overflow: hidden;
    padding-bottom: 0;

    .modal_container {
      height: 100vh;
      min-height: unset;
    }
  }

  @media (orientation: landscape) {
    @media screen and (max-height: 600px) {
      padding-bottom: 25px !important;
    }
  }
}
