@import '../../styles/variables';
@import '../../styles/sizes';

#sms_validation {
  text-align: center;
  display: inline-block;
  position: relative;

  .input_container {
    display: inline-block;

    .input_cell {
      height: 44px;
      width: 35px;
      font-size: 20px;
      text-align: center;
      border: none;
      border-bottom: 1px solid $gray;
      border-radius: 0;
      text-transform: uppercase;
      -webkit-appearance: none;
      margin-left: 3.5px;
      margin-right: 3.5px;
      padding: 0;

      &::placeholder {
        color: $dark-gray;
        font-size: 16px;
      }
    }

    .input_cell:focus {
      outline: none;
    }

    .input_cell::-webkit-inner-spin-button,
    .input_cell::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
  }
}
