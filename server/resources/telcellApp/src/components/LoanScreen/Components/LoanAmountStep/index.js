import React, { Component } from 'react';
import { connect } from 'react-redux';
import { withNamespaces } from 'react-i18next';
import { withRouter } from 'react-router-dom';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import querystring from 'query-string';

import i18n from '../../../../i18n';
import CitizenInfo from './CitizenInfo';
import LoanRejection from '../LoanRejection';
import { LOAN_TYPES } from '../../../../constants';
import InternalServerError from '../../../InternalServerError';

import { fetchCitizen } from '../../../../store/ducks/citizen';
import { fetchLoanConfigsForCitizen } from '../../../../store/ducks/loanConfigs';
import LoansContent from './LoansContent';
import Loader from '../../../Loader';
import { getLoanType } from '../../../../helpers';

import styles from './index.module.scss';

class LoanAmountStep extends Component {
  constructor(props) {
    super(props);

    window.recaptchaOptions = {
      lang: i18n.language,
    };
  }

  componentDidMount() {
    window.scroll(0, 0);

    const { fetchCitizen, fetchLoanConfigsForCitizen, history } = this.props;
    const loanTypeId = getLoanType();
    const { gcva, gcvn, gcvp } = querystring.parse(history.location.search);

    fetchCitizen({
      loan_type_id: loanTypeId,
      vehicle_number: gcvn,
      tech_passport: gcvp,
      trade_amount: gcva,
    });

    fetchLoanConfigsForCitizen(loanTypeId);
  }

  isLoanUnavailable = () => {
    const {
      citizen: { data: citizen = { credit: {} } },
    } = this.props;
    const OvlCredits =
      citizen.credit &&
      citizen.credit.length &&
      citizen.credit.filter(credit => !credit.rejected);

    return !OvlCredits || !OvlCredits.length;
  };

  isLoading() {
    const {
      citizen: { loading },
      loanConfigs,
    } = this.props;

    return loading || loanConfigs.loading;
  }

  isCitizenUnavailable = () => {
    return !!this.props.citizen.error;
  };

  renderLoanDetails() {
    const {
      citizen: { data: citizen = {} },
    } = this.props;

    if (this.isLoanUnavailable() && !citizen.allowTrade) {
      return;
    }

    return (
      <LoansContent
        {...this.props}
        osm={citizen.osm}
        dstiRepayment={citizen.dsti}
        dstiIncome={Math.max(citizen.dstiIncome, citizen.salary)}
        isNewOvlCustomer={citizen.isNewOvlCustomer}
        loading={this.isLoading()}
        isLoanUnavailable={this.isLoanUnavailable}
        loanName={{
          key: LOAN_TYPES.OVL.name,
        }}
      />
    );
  }

  render() {
    const {
      citizen: { data: citizen = { credit: {} } },
    } = this.props;

    if (this.isLoading()) {
      return <Loader loading={true} />;
    }

    return (
      <div id={styles.loan_amount_form}>
        <>
          {this.isCitizenUnavailable() ? (
            <InternalServerError />
          ) : (
            <>
              <CitizenInfo citizen={citizen} />
              {this.isLoanUnavailable() && !citizen.allowTrade && (
                <LoanRejection />
              )}
              {this.renderLoanDetails()}
            </>
          )}
        </>
      </div>
    );
  }
}

LoanAmountStep.propTypes = {
  wizardBag: PropTypes.object,
};

function mapStateToProps(state) {
  const { citizen, loanConfigs, loan } = state;

  return { citizen, loanConfigs, loan };
}

const mapDispatchToProps = dispatch => {
  return {
    fetchCitizen: loanTypeId => dispatch(fetchCitizen(loanTypeId)),
    fetchLoanConfigsForCitizen: loanTypeId =>
      dispatch(fetchLoanConfigsForCitizen(loanTypeId)),
  };
};

export default compose(
  withRouter,
  withNamespaces('translations'),
  connect(mapStateToProps, mapDispatchToProps)
)(LoanAmountStep);
