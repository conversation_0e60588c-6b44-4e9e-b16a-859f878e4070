import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withNamespaces } from 'react-i18next';
import { Formik, Form } from 'formik';
import querystring from 'query-string';
import { compose } from 'redux';
import { connect } from 'react-redux';

import { wizardLoanAmountSchema } from '../../../../../validation/schemas/wizardLoanSchema';
import { getLoanType, setLoanType } from '../../../../../helpers/';
import VehicleInfo from '../VehicleInfo';
import LoanInfo from '../LoanInfo';
import { LOAN_TYPES } from '../../../../../constants';
import Currency from '../../../../Currency';
import LoanAmount from '../../../../LoanAmount';
import LoanMonthlyPayment from '../../../../LoanMonthlyPayment';
import { handlePreventEnterKey } from '../../../../../helpers/input';
import Loader from '../../../../Loader';
import { InternalServerError } from '../../../../InternalServerError';

import {
  getLoanTermsCalculation,
  getLoanSchedule,
  approveLoan,
} from '../../../../../store/ducks/loan';

import styles from './index.module.scss';

const MONTHLY_PAYMENT_STEP = 1000;

class LoansContent extends Component {
  constructor(props) {
    super(props);

    this.state = {
      currentOvl: null,
      initialAmount: 0,
      monthlyPayment: 0,
      selectedAmount: 0,
      dynamicMonthlyPayment: 0,
    };
  }

  componentDidMount() {
    const {
      citizen: { data: citizen = {} },
      loanName,
      getLoanTermsCalculation,
    } = this.props;

    let { currentOvl, initialAmount } = this.state;

    if (loanName.key === LOAN_TYPES.OVL.name) {
      if (citizen.credit) {
        const { gcvn } = querystring.parse(window.location.search);

        currentOvl = citizen.credit.find(c =>
          gcvn
            ? c.vehicleInfo && c.vehicleInfo.number === gcvn
            : c.rejected === false
        );
      }

      const currentOvlCreditAmount = currentOvl && +currentOvl.amount;
      if (currentOvl) {
        const roundedRequestedLoanAmount = +currentOvl.roundedRequestedLoanAmount;

        // We need to prevent the case when roundedRequestedLoanAmount
        // can be greater than calculated maximum credit amount
        if (roundedRequestedLoanAmount > currentOvlCreditAmount) {
          initialAmount = currentOvlCreditAmount;
        } else {
          initialAmount = roundedRequestedLoanAmount;
        }
      } else {
        initialAmount = 0;
      }
    }

    const { selectedAmount } = this.getSavedAmountAndMonthlyPayment();

    const { number, tradeAmount, techPassport } =
      (currentOvl && currentOvl.vehicleInfo) || {};

    if (initialAmount !== 0) {
      getLoanTermsCalculation({
        amount: selectedAmount || initialAmount,
        loanTypeId: getLoanType(),
        vehicleNumber: number,
        tradeAmount,
        techPassport,
        offeredCredit: currentOvl,
        isNewOvlCustomer: citizen.isNewOvlCustomer,
        osm: citizen.osm,
      });
    }

    this.setState({
      currentOvl,
      initialAmount: selectedAmount || initialAmount,
      selectedAmount,
    });
  }

  componentDidUpdate(prevProps, prevState) {
    const {
      termsCalculation,
      termsCalculationLoading,
      getLoanSchedule,
    } = this.props;

    if (
      !termsCalculationLoading &&
      prevProps.termsCalculation &&
      prevProps.termsCalculation !== termsCalculation
    ) {
      const { initialAmount, currentOvl } = this.state;
      const {
        number,
        tradeAmount,
        techPassport,
      } = this.getCheckedVehicleInfo();

      const monthlyPayment = this.getMonthlyPayment();
      getLoanSchedule({
        amount: initialAmount,
        monthlyPayment,
        loanTypeId: getLoanType(),
        vehicleNumber: number,
        tradeAmount,
        techPassport,
        offeredCredit: currentOvl,
      });

      this.setState({ monthlyPayment, dynamicMonthlyPayment: monthlyPayment });
    }
  }

  changeCurrentOvl = currentOvl => {
    const {
      getLoanTermsCalculation,
      citizen: { data: citizen = {} },
    } = this.props;
    const { initialAmount } = this.state;
    const {
      amount,
      vehicleInfo: { number, tradeAmount, techPassport },
    } = currentOvl;

    if (initialAmount === 0) {
      getLoanTermsCalculation({
        amount,
        loanTypeId: getLoanType(),
        vehicleNumber: number,
        tradeAmount,
        techPassport,
        offeredCredit: currentOvl,
        isNewOvlCustomer: citizen.isNewOvlCustomer,
        osm: citizen.osm,
      });
    }

    this.setState({
      currentOvl,
      initialAmount: currentOvl.amount,
    });
  };

  getCheckedVehicleInfo() {
    const { currentOvl } = this.state;

    return (currentOvl && currentOvl.vehicleInfo) || {};
  }

  handleRadioChange = value => {
    const {
      citizen: { data: citizen = { credit: {} } },
    } = this.props;

    const currentOvl = citizen.credit.find(
      c => c.vehicleInfo && c.vehicleInfo.number === value
    );

    this.setState({
      currentOvl,
    });
  };

  getServiceFeeRate = () => {
    const {
      citizen: { data: citizen = { credit: {} } },
      loanConfigs: { data: config = {} },
      loanName,
    } = this.props;

    switch (loanName.key) {
      case LOAN_TYPES.OVL.name:
        return +config.serviceFeeRate - citizen.qrDiscount;
      default:
        return 0;
    }
  };

  withdrawalFeeAmount = () => {
    const {
      loanConfigs: { data: config = {} },
      loanName,
    } = this.props;
    const { initialAmount } = this.state;

    switch (loanName.key) {
      case LOAN_TYPES.OVL.name:
        return +config.withdrawalFeeRate * initialAmount;
      default:
        return 0;
    }
  };

  getMinAmount = () => {
    const {
      loanConfigs: { data: config = {} },
    } = this.props;

    return +config.minAmount;
  };

  getMaxAvailableAmount = () => {
    const { loanName } = this.props;

    switch (loanName.key) {
      case LOAN_TYPES.OVL.name:
        const { currentOvl } = this.state;
        return currentOvl ? +currentOvl.amount : 0;
      default:
        return 0;
    }
  };

  handleSubmit = values => {
    const {
      approveLoan,
      wizardBag: { goNext },
      schedule,
    } = this.props;
    const { initialAmount, monthlyPayment } = this.state;

    const { months, lastMonthPayment, total } = schedule;

    const {
      number,
      tradeAmount,
      techPassport,
      vin,
    } = this.getCheckedVehicleInfo();

    const loanTypeId = getLoanType();

    approveLoan(
      {
        amount: initialAmount,
        monthlyPayment,
        total,
        months,
        lastMonthPayment,
        loanTypeId,
        vehicleNumber: number,
        tradeAmount: tradeAmount,
        techPassport: techPassport,
        submittedApr: values.apr,
        vin,
      },
      () => {
        setLoanType(loanTypeId);
        goNext({
          gca: parseInt(initialAmount, 10),
          gcm: parseInt(monthlyPayment, 10),
          gcvn: number,
          gcva: tradeAmount,
          gcvp: techPassport,
        });
      }
    );
  };

  getMonthlyPayment = () => {
    const { monthlyPayment } = this.state;
    const { termsCalculation } = this.props;

    const { selectedMonthlyPayment } = this.getSavedAmountAndMonthlyPayment();
    if (
      selectedMonthlyPayment &&
      selectedMonthlyPayment >= termsCalculation.monthlyMin &&
      selectedMonthlyPayment <= termsCalculation.monthlyMax
    ) {
      return selectedMonthlyPayment;
    }

    if (monthlyPayment < termsCalculation.monthlyMin) {
      return termsCalculation.monthlyMin;
    } else if (monthlyPayment > termsCalculation.monthlyMax) {
      return termsCalculation.monthlyMax;
    }

    return monthlyPayment;
  };

  onUpdateAmount = amount => {
    const {
      getLoanTermsCalculation,
      citizen: { data: citizen = {} },
    } = this.props;
    const { currentOvl } = this.state;
    const { number, tradeAmount, techPassport } = this.getCheckedVehicleInfo();

    getLoanTermsCalculation({
      amount,
      loanTypeId: getLoanType(),
      vehicleNumber: number,
      tradeAmount,
      techPassport,
      offeredCredit: currentOvl,
      isNewOvlCustomer: citizen.isNewOvlCustomer,
      osm: citizen.osm,
    });

    this.setState({
      selectedAmount: amount,
      initialAmount: amount,
    });
  };

  onUpdateMonthly = monthlyPayment => {
    const { getLoanSchedule } = this.props;
    const { initialAmount, selectedAmount, currentOvl } = this.state;

    const { number, tradeAmount, techPassport } = this.getCheckedVehicleInfo();

    getLoanSchedule({
      amount: selectedAmount || initialAmount,
      monthlyPayment,
      loanTypeId: getLoanType(),
      vehicleNumber: number,
      tradeAmount,
      techPassport,
      offeredCredit: currentOvl,
    });

    this.setState({ monthlyPayment });
  };

  onUpdateMonthlyDynamically = monthlyPayment => {
    this.setState({ dynamicMonthlyPayment: monthlyPayment });
  };

  shouldComponentUpdate(nextProps, nextState, nextContext) {
    // do not re-render component if dynamicMonthlyPayment is changed
    return this.state.dynamicMonthlyPayment === nextState.dynamicMonthlyPayment;
  }

  getSavedAmountAndMonthlyPayment() {
    const { loanName } = this.props;

    const loanTypeId = getLoanType();

    let selectedAmount = null;
    let selectedMonthlyPayment = null;

    if (LOAN_TYPES[loanName.key.toUpperCase()].id === loanTypeId) {
      const { gca, gcm } = querystring.parse(window.location.search);

      selectedAmount = Number(gca);
      selectedMonthlyPayment = Number(gcm);
    }

    return {
      selectedAmount: selectedAmount,
      selectedMonthlyPayment,
    };
  }

  renderTermsCalculation = formik => {
    const {
      t,
      loanConfigs: { data: config = {} },
      termsCalculation,
      schedule,
      scheduleLoading,
    } = this.props;
    const { selectedAmount, initialAmount, currentOvl } = this.state;

    const maxAvailableAmount = this.getMaxAvailableAmount();
    const minAvailableAmount = this.getMinAmount();

    const { monthlyPayment, dynamicMonthlyPayment } = this.state;
    const loanStep = +config.amountStep;
    const { setFieldValue } = formik;

    if (!maxAvailableAmount) {
      return null;
    }

    return (
      <>
        <div className={styles.loan_amount}>
          <div className={styles.loan_info_title}>
            {t('loan.steps.loan_amount.loan_info_title')}
          </div>
          <LoanAmount
            onUpdateAmount={value => {
              this.onUpdateAmount(value, setFieldValue);
            }}
            min={minAvailableAmount}
            max={maxAvailableAmount}
            step={loanStep}
            selectedAmount={selectedAmount || initialAmount}
            currentOvl={currentOvl}
            disabled={scheduleLoading}
          />
        </div>
        <div className={styles.loan_amount_info_section}>
          <div className={styles.label}>{t('suggestions.monthly_fee_2')}</div>
          <div className={styles.value}>
            <Currency
              hasSign={true}
              value={dynamicMonthlyPayment}
              precision={false}
            />
          </div>
        </div>
        <LoanMonthlyPayment
          onUpdateWhenBluring={value =>
            this.onUpdateMonthly(value, setFieldValue)
          }
          onUpdateDynamically={value => this.onUpdateMonthlyDynamically(value)}
          min={termsCalculation.monthlyMin}
          max={termsCalculation.monthlyMax}
          step={MONTHLY_PAYMENT_STEP}
          selectedAmount={selectedAmount || initialAmount}
          currentOvl={currentOvl}
          disabled={scheduleLoading}
          initialValue={schedule.monthlyPayment || monthlyPayment}
        />
      </>
    );
  };

  renderLoanInfo = (loanName, formik) => {
    const { initialAmount, monthlyPayment } = this.state;
    const { isLoanUnavailable, scheduleLoading } = this.props;
    const maxAvailableAmount = this.getMaxAvailableAmount();

    if (isLoanUnavailable() || !maxAvailableAmount) {
      return;
    }

    const {
      wizardBag: { step },
      citizen: { data: citizen = {} },
      schedule,
    } = this.props;

    const withdrawalFeeAmount = this.withdrawalFeeAmount();
    const serviceFeeRate = this.getServiceFeeRate();

    const { setFieldValue } = formik;

    return (
      <LoanInfo
        schedule={schedule}
        step={step}
        amount={initialAmount}
        citizen={citizen}
        loanName={loanName.key}
        monthlyPayment={monthlyPayment}
        isLoanUnavailable={isLoanUnavailable}
        serviceFeeRate={serviceFeeRate}
        withdrawalFeeAmount={withdrawalFeeAmount}
        loading={scheduleLoading}
        setFieldValue={setFieldValue}
      />
    );
  };

  getInitialValues = key => {
    const values = {
      isHuman: false,
      vehicleNumber: '',
      aprEnabled: true,
      apr: '',
      actualApr: '',
    };

    values[`${key}AprApprovalCheckbox`] = false;

    return values;
  };

  isLoading() {
    const { loading, termsCalculationLoading } = this.props;

    return loading || termsCalculationLoading;
  }

  renderVehicleInfo = formik => {
    const { currentOvl } = this.state;

    return (
      <VehicleInfo
        {...this.props}
        formik={formik}
        currentOvl={currentOvl}
        changeCurrentOvl={this.changeCurrentOvl}
        handleRadioChange={this.handleRadioChange}
      />
    );
  };

  render() {
    const { loanName, error, t } = this.props;

    if (this.isLoading()) {
      return <Loader loading={true} />;
    }

    if (!!error) {
      return <InternalServerError t={t} />;
    }

    return (
      <>
        <div className={styles.loan_content}>
          <Formik
            initialValues={this.getInitialValues(loanName.key)}
            onSubmit={this.handleSubmit}
            validationSchema={() =>
              wizardLoanAmountSchema(`${loanName.key}AprApprovalCheckbox`)
            }
            validateOnBlur={true}
            validateOnChange={false}
          >
            {formik => {
              return (
                <Form onKeyDown={handlePreventEnterKey}>
                  {this.renderVehicleInfo(formik)}

                  {this.renderTermsCalculation(formik)}

                  {this.renderLoanInfo(loanName, formik)}
                </Form>
              );
            }}
          </Formik>
        </div>
      </>
    );
  }
}

LoansContent.propTypes = {
  onChange: PropTypes.func,
};

LoansContent.defaultProps = {
  onChange: () => {},
};

function mapStateToProps(state) {
  const {
    loan: {
      termsCalculation = {},
      schedule = {},
      scheduleLoading,
      termsCalculationLoading,
      loading,
      error,
    },
  } = state;

  return {
    termsCalculation,
    schedule,
    scheduleLoading,
    termsCalculationLoading,
    loading,
    error,
  };
}

const mapDispatchToProps = dispatch => {
  return {
    getLoanTermsCalculation: data => dispatch(getLoanTermsCalculation(data)),
    getLoanSchedule: data => dispatch(getLoanSchedule(data)),
    approveLoan: (data, cb) => dispatch(approveLoan(data, cb)),
  };
};

export default compose(
  withNamespaces('translations'),
  connect(mapStateToProps, mapDispatchToProps)
)(LoansContent);
