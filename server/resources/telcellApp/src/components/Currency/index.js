import React, { Component } from 'react';
import classnames from 'classnames';

import { numberToDecimalString } from '../../helpers/numberHelpers';
import SvgComponent from '../SvgComponent';

import styles from './index.module.scss';

class Currency extends Component {
  render() {
    const {
      hasSign,
      value,
      className,
      precision,
      signPosition = 'start',
    } = this.props;

    return (
      <div className={classnames(className, styles.currency)}>
        {hasSign && signPosition === 'start' && (
          <SvgComponent name="amd" className={styles.dram_icon} />
        )}
        <span className={styles.currency_price}>
          {isNaN(value) ? value : numberToDecimalString(value, precision)}
        </span>
        {hasSign && signPosition === 'end' && (
          <SvgComponent name="amd" className={styles.dram_icon} />
        )}
      </div>
    );
  }
}

export default Currency;
