@import '../../styles/variables';
@import '../../styles/sizes';

.wizard_container {
  background: white;
  width: 100%;
  z-index: 1100;
  -webkit-overflow-scrolling: touch;

  .wizard {
    width: 100%;
    background-color: $dark-gray;
    padding: 20px;

    .title_description {
      font-size: 22px;
      color: $white;
      display: flex;
      justify-content: space-between;
      text-transform: uppercase;
    }

    .counts {
      color: $lighter-gray;
      font-weight: bold;
    }

    .active {
      span:first-child {
        color: $white;
      }
    }
  }

  .server_error {
    position: absolute;
    width: 100%;
    background-color: $pink;
    text-align: center;
    z-index: 1;

    & > div {
      color: $white;
      margin: 15px 55px;
      font-size: 14px;
    }

    .icon {
      position: absolute;
      right: 15px;
      top: 30%;
      color: $white;
      cursor: pointer;
    }
  }
}

.wizard_page {
  padding: 0;
}

#wizard_navigation {
  text-align: center;
  padding-bottom: 25px;
  max-width: 400px;
  margin: 0 auto;
  display: flex;

  .previous_step_button {
    color: $main;
    font-weight: normal;
    padding: 0 10px;
  }

  :global(.gc_primary) {
    background-color: $telcell-orange;
  }
}

@media screen and (max-width: $tablet-width) {
  .wizard_container {
    .wizard {
      a {
        &:first-child {
          flex-grow: 1.3;
        }

        &:first-child {
          @supports (-webkit-overflow-scrolling: touch) {
            flex-grow: 1.3;
          }
        }
      }
    }
  }
}

@media screen and (max-width: $lowers-tablet-width) {
  .wizard_container {
    .wizard {
      a {
        &:first-child {
          flex-grow: 1.2;
        }

        &:first-child {
          @supports (-webkit-overflow-scrolling: touch) {
            flex-grow: 1.2;
          }
        }

        .title {
          margin-top: 10px;
        }
      }
    }
  }
}

@media screen and (max-width: $mobile-width) {
  .wizard_container {
    .wizard {
      a {
        padding-left: 5px;
        padding-right: 0;

        &:first-child {
          flex-grow: 5;
        }

        &:last-child {
          flex-grow: 3;
        }

        &:first-child {
          @supports (-webkit-overflow-scrolling: touch) {
            flex-grow: 8;
          }

          .inner_content {
            right: 9px;
          }

          .title {
            margin-right: -9px;
          }
        }

        .inner_content {
          margin-left: 15px;
        }

        .title {
          margin-left: 10px;
          font-size: 10px;
        }
      }
    }
  }
}
