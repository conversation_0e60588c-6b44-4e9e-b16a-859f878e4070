import React, { Component } from 'react';
import { Icon } from 'semantic-ui-react';
import ServerError from '../Validation/ServerError';

import styles from './index.module.scss';

class WizardError extends Component {
  render() {
    const { error, onClose } = this.props;

    if (!error) {
      return null;
    }

    return (
      <div className={styles.server_error}>
        <ServerError className={styles.error_text} error={error} />
        <Icon
          onClick={onClose}
          className={styles.icon}
          name="close"
          size="large"
        />
      </div>
    );
  }
}

export default WizardError;
