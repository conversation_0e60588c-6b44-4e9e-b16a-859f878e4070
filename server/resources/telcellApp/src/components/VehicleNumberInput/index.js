import React, { Component } from 'react';
import InputMask from 'react-input-mask';
import { Input } from 'semantic-ui-react';

import { VEHICLE_NUMBER_MASK } from '../../constants';
import { getLetterInputType } from '../../helpers/input';

import styles from './index.module.scss';

class VehicleNumberInput extends Component {
  getInputType = length => {
    if (length > 1 && length < 4) {
      return getLetterInputType();
    }

    return 'tel';
  };

  render() {
    const { values, handleChange } = this.props;

    return (
      <div id={styles.vehicle_number_input_component}>
        <InputMask
          id="vehicle"
          key={values.vehicleNumber}
          autoFocus={!!values.vehicleNumber.length}
          type={this.getInputType(values.vehicleNumber.length)}
          mask={VEHICLE_NUMBER_MASK}
          maskChar={null}
          value={values.vehicleNumber}
          autoComplete="off"
          onChange={handleChange}
        >
          {props => (
            <Input
              {...props}
              label={{
                basic: true,
                content: (
                  <div className={styles.vehicle_number_input_label}>
                    <div className={styles.flag_container}>
                      <div className={styles.vehicle_flag_label}>
                        <div className={styles.flag}>
                          <div className={styles.flag_red} />
                          <div className={styles.flag_blue} />
                          <div className={styles.flag_orange} />
                        </div>
                        <div className={styles.text}>AM</div>
                      </div>
                      <div
                        className={styles.vehicle_number_input_label_seperator}
                      />
                    </div>
                  </div>
                ),
              }}
              labelPosition="left"
              placeholder="11 XX 111"
              className={styles.vehicle_number_input}
            />
          )}
        </InputMask>
      </div>
    );
  }
}

export default VehicleNumberInput;
