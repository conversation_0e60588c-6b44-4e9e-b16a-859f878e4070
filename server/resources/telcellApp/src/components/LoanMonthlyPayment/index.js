import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Dropdown, Icon } from 'semantic-ui-react';
import { withNamespaces } from 'react-i18next';

import Currency from '../Currency';

import styles from './index.module.scss';

class LoanMonthlyPayment extends Component {
  constructor(props) {
    super(props);

    const { min, max, initialValue } = props;

    let values = [min, max];

    if (initialValue && initialValue <= max) {
      values = [initialValue];
    }

    this.state = { values };
  }

  componentDidUpdate(prevProps) {
    const { min, max, initialValue } = this.props;

    if (
        min !== prevProps.min ||
        max !== prevProps.max ||
        initialValue !== prevProps.initialValue
    ) {
      this.setMonthlySliderValues();
    }
  }

  setMonthlySliderValues = () => {
    const { min, max, initialValue } = this.props;

    let values = [min, max];

    if (initialValue && initialValue <= max && initialValue >= min) {
      values = [initialValue];
    }

    this.setState({ values });
  };

  onUpdateMonthlyAmount = (e, target) => {
    this.props.onUpdateWhenBluring(target.value);
    this.props.onUpdateDynamically(target.value);
  };

  render() {
    const { t, min, max, step, disabled } = this.props;
    const { values } = this.state;
    const value = values[0];

    let stateOptions = [];
    let i = 0;

    for (let amount = min; amount <= max; amount += step) {
      stateOptions.push({
        key: i++,
        text: (
            <span className={styles.loan_data}>
            <Currency hasSign={true} value={amount} precision={false} />
          </span>
        ),
        value: amount,
      });
    }

    if (
        stateOptions.length &&
        stateOptions[stateOptions.length - 1].value < max
    ) {
      stateOptions.push({
        key: i++,
        text: (
            <span className={styles.loan_data}>
            <Currency hasSign={true} value={max} precision={false} />
          </span>
        ),
        value: max,
      });
    }

    return (
      <div className={styles.loan_monthly_payment_container}>
        <div className={styles.loan_monthly_payment_dropdown}>
          <div className={styles.loan_monthly_payment_info_section}>
            <div className={styles.label}>
              {t('suggestions.monthly_fee')}
            </div>
            <div className={styles.value}>
              <Dropdown
                placeholder={t('loan.steps.loan_amount.select_loan_amount')}
                fluid
                selection
                value={value}
                icon={<Icon name="chevron down" className={styles.icon} />}
                options={stateOptions}
                onChange={this.onUpdateMonthlyAmount}
                selectOnBlur={false}
                disabled={disabled}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }
}

LoanMonthlyPayment.propTypes = {
  onChange: PropTypes.func,
};

LoanMonthlyPayment.defaultProps = {
  onChange: () => {},
};

export default withNamespaces('translations')(LoanMonthlyPayment);
