import * as Yup from 'yup';
import i18n from '../../i18n';
import './validators';

const wizardPersonalDataSchema = Yup.object().shape({
  email: Yup.string()
    .required()
    .email(),
  additionalPhoneNumber: Yup.string()
    .required()
    .phone(),
  isChecked: Yup.bool()
    .test('isChecked', i18n.t('validator.terms'), value => value === true)
    .required(i18n.t('validator.terms')),
});

export default wizardPersonalDataSchema;
