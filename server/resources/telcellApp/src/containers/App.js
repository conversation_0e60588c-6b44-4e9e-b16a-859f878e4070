import React from 'react';
import { Redirect, Route, Switch } from 'react-router-dom';
import LoanScreen from '../components/LoanScreen';
import TermsAndConditions from '../components/TermsAndConditions';
import ConfirmTerms from '../components/ConfirmTerms';
import Confirmation from '../components/LoanScreen/Components/Confirmation';

const App = props => {
  return (
    <Switch>
      <Route
        path="/terms-and-conditions"
        render={() => <TermsAndConditions {...props} />}
      />

      <Route path="/confirm-terms" render={() => <ConfirmTerms {...props} />} />
      {props.apiParams.allow_open_confirm_terms && (
        <Redirect to="/confirm-terms" />
      )}

      <Route
        exact
        path="/loan/confirmation"
        render={() => <Confirmation {...props} />}
      />

      <Route path="/loan" render={() => <LoanScreen {...props} />} />

      <Redirect from="*" to="/terms-and-conditions" />
    </Switch>
  );
};

export default App;
