@extends('wallet.layout')

@section('content')
    <main id="content" class="confirmation-content" role="main">
        <div>
            <div class="row">

                <div class="col-md-12 header"><img src="/assets/wallet_assets/images/logo.svg" /></div>

                <div class="col-md-12">
                    @if($status == 'success')
                        <p>
                            Վարկային գումարը փոխանցվել է Ձեր՝ Fast Shift wallet-ին։ Վարկային պայմանագրի էլեկտրոնային տարբերակը ուղարկվել է Ձեր էլ. փոստին։
                        </p>
                    @else
                        <p>
                            Փոխանցումը ձախողվեց։ Լրացուցիչ տեղեկությունների համար խնդրում ենք կապնվել մեզ հետ <a target="_blank" href="tel:+37411700100">+(374 11) 700 100</a>:
                        </p>
                    @endif
                </div>
            </div>
        </div>

    </main>
    <script defer>
      // This is an event that one is needed FastShift to build some functionality
      // in their APP based on this event call
      // Android call
      if (typeof JSInterface != 'undefined') {

        JSInterface.onComplete()
      }
      // IOS call
      if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.jsHandler) {
        window.webkit.messageHandlers.jsHandler.postMessage("OnComplete");
      }
    </script>
@endsection