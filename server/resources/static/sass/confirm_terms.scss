.confirm-terms-content {
  min-height: 100%;
  padding-top: 10px;

  .warning-section {
    display: flex;
    max-width: 390px;
    align-items: flex-start;
    margin: 0 auto;
    padding: 0;
    width: 100%;

    img {
      width: 40px;
      margin-right: 14px;
      margin-top: 7px;
    }

    .text {
      text-align: left;
      font-size: 14px;
    }
  }

  .agreement-checkbox {
    width: 18px;
    height: 18px;
  }

  .agreement-checkbox-text {
    font-weight: 100;
  }

  .checkboxes-section {
    margin: auto;
    max-width: 390px;
    padding: 10px 0;
    width: 100%;

    .according-to {
      color: $dark-gray;
      font-size: 12px;
      display: inline-block;
      padding-left: 40px;
      cursor: pointer;
    }

    .terms-modal-button {
      color: $dark-gray;
      text-decoration: underline;
      font-size: 12px;
      display: inline-block;
      cursor: pointer;
    }

    label {
      display: block;
      font-size: 12px;
    }
  }

  hr {
    border: 0.5px solid $border-color;
    width: 100%;
  }

  .btn.btn-primary {
    margin-top: 20px;
  }
}

#the_modal {
  .modal-dialog {
    margin: 2.75rem auto !important;
    max-width: 400px;
  }

  .modal-body {
    padding: 5px 0 5px 0;
    border-radius: 10px;
    max-width: calc(100% - 10px);
    left: unset !important;
    height: 400px;
    margin-top: 30px;
    overflow: unset;
    overflow-y: scroll;
    overflow-x: hidden;

    .the-canvas {
      width: 100% !important;
      height: auto !important;
    }
  }

  .actions {
    padding: 0 !important;
    text-align: center;
    background: white;

    button {
      position: relative;
      z-index: 9;
      margin: 10px 0 20px 0 !important;
    }
  }

  .modal-header .close {
    position: absolute;
    right: 16px;
    top: 16px;
  }

  @media screen and (max-width: 375px) {
    .modal-header .modal-title {
      font-size: 14px !important;
    }
  }
}
