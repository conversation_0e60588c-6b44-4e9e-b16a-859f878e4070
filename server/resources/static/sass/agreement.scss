.agreement-content {
  min-height: 100%;
  padding-top: 10px;

    .heading-1 {
      padding-left: 30px;
      padding-top: 10px;
      color: #39587F
    }

    .heading-2 {
      padding-left: 30px;
      color: #F89E54;
      margin: 0
    }

    .header-point {
      color:#39587F;
      background-image: url('assets/wallet_assets/images/big-semicircle.svg');
      background-repeat: no-repeat;
      background-size: 35px;
      padding: 5px 11px;
      margin-top: 20px;
      line-height: 35px;
    }

    .big-semicircle-box {
      background-image: url('assets/wallet_assets/images/big-semicircle.svg');
      background-position: top left;
      background-repeat: no-repeat;
      background-size: 116px 136px;
      padding: 25px;
      margin: 2%;
      min-height: 150px;
    }

    .circle {
      font-weight: bold;
      border: 1px solid;
      border-radius: 30px;
      margin: 5px;
      width: 30px;
      height: 30px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      line-height: 30px;
    }

    .border-blue {
      border-color: #00FFFF;
    }

    .border-orange {
      border-color: orange;
    }
    .small-text {
      font-size:10px;
    }

    button[type=submit] {
      margin-top: 30px;
    }
}

@media screen and (max-width: 450px) {
  .agreement-content {
      .heading-1, .heading-2 {
        font-size: 14px;
      }
  }
}