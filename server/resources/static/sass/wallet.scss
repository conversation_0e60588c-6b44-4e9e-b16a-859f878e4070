@import '../../telcellApp/src/styles/variables';
@import './fonts';

body {
  color: $dark-gray;
  font-size: 14px;
  font-family: $main-font-family;
  background-color: white !important;
}

.header {
  background-color: white;
  text-align: center;
  padding: 10px 0;
  margin: 0 10px 20px 10px;

  img {
    height: auto;
    width: 40%;
    max-width: 220px;
    min-width: 160px;
  }
}

.container {
  padding-bottom: 20px;
}

.hor-scroll {
  width: 100%;
  overflow: hidden;
  overflow-x: auto;
}

.error-box {
  width: 100%;
  color: red;
  font-size: 10px;
}

.error-message {
  display: flex;
  width: 100%;
  color: red;
  font-size: 14px;
  padding: 10px;
  text-align: center;
  justify-content: center;
}

.btn.btn-primary {
  margin-top: 30px;
  margin-bottom: 10px;
  background-color: $button-color !important;
  border-radius: 18px;
  border: none !important;
  min-width: 150px !important;

  &.easypay-btn {
    background-color: $easypay-button-color !important;
  }

  &.upay-btn {
    background-color: $upay-button-color !important;
  }

  &.fastshift-btn {
    background-color: $fastshift-button-color !important;
  }

  &:disabled {
    background-color: $dark-gray !important;
    cursor: not-allowed;
  }

  &:enabled {
    span {
      display: none;
    }
  }
}

.checkbox {
  width: 100%;
  margin: 15px auto;
  position: relative;
  display: block;

  input[type='checkbox'] {
    display: none;
  }
  input[type='checkbox']:checked ~ label::before {
    background-color: $gc-red-color;
  }
  input[type='checkbox']:checked ~ label::after {
    -webkit-transform: rotate(-45deg) scale(1);
    transform: rotate(-45deg) scale(1);
  }
  input[type='checkbox']:focus + label::before {
    outline: 0;
  }

  label {
    min-height: 34px;
    display: block;
    padding-left: 40px;
    margin-bottom: 0;
    font-weight: normal;
    cursor: pointer;
    vertical-align: sub;
    position: relative;

    &:before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      margin: 4px;
      width: 22px;
      height: 22px;
      transition: transform 0.28s ease;
      border-radius: 3px;
      border: 2px solid $gc-red-color;
      background: white;
    }
    &:after {
      content: '';
      display: block;
      width: 10px;
      height: 5px;
      border-bottom: 2px solid white;
      border-left: 2px solid white;
      -webkit-transform: rotate(-45deg) scale(0);
      transform: rotate(-45deg) scale(0);
      transition: transform ease 0.25s;
      will-change: transform;
      position: absolute;
      top: 12px;
      left: 10px;
    }

    span {
      position: absolute;
      top: 50%;
      -webkit-transform: translateY(-50%);
      transform: translateY(-50%);
    }
  }
}

.apr_information {
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 99;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
  padding: 15px;
  background-color: $main-transparent;
  box-shadow: 0px -2px 8px 0 $black-transparent;
  transition: all 1s ease-in-out;

  &.hide {
    bottom: -200px;
    opacity: 0;
  }

  .info_text {
    font-size: 12px;
    line-height: 15px;
    text-align: center;
  }

  .btn_outline {
    background: none;
    margin-top: 10px;
    cursor: pointer;
    border: 1px solid #fff;
    color: #fff;
    line-height: 17px;
    border-radius: 5px;
    width: 80%;
    padding: 15px;
    font-size: 15px;
    outline: none;
  }

  @media screen and (max-width: 480px) {
    .info_text {
      font-size: 8px;
    }
  }
}
