<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Http\Request;

Route::get('/gc-view', function () {
    return view('gc-view');
});

// This route for Analytics requested by Levon
Route::get('/get', function () {
    return redirect('https://cashme.am/home?utm_source=sms&utm_medium=sms&utm_campaign=sms_1');
});

// PL nike testing route
Route::get('/gc-view/pay-later/{order_id?}', function (Request $request) {
    $status = null;
    $order_id = $request->order_id;

    try {
        if ($order_id) {
            $client = new Client();

            $response = $client->post('nginx/api/pl/get-payment', [
                'headers' => [
                    'Client-Identifier' => 'nike_id',
                    'Client-Secret' => 'nike_secret',
                ],
                'form_params' => [
                    'order_id' => $order_id,
                ],
            ]);
            $result = json_decode((string) $response->getBody(), 1);

            $status = count($result['data']);
        }
    } catch (\Exception $e) {
    }

    return view('pay-later', ['order_id' => $order_id, 'status' => $status]);
});

// PL nike testing route
Route::post('/gc-view/pay-later', function () {
    $payload = [
        'order_id' => rand(),
        'amount' => 10000,
        'description' => 'test',
        'callback_url' => env('APP_URL').'/api/pl/pay-later-callback',
        'currency' => 'AMD',
    ];

    $purchase_service = resolve('App\Services\CreditLine\PurchaseServicePL');

    $client_identifier = 'nike_id';

    $payment_id = $purchase_service->createSession($payload, $client_identifier)->payment_id;

    return view('pay-later', ['url' => env('PAY_LATER_BASE_URL')."/payment?payment_id=$payment_id"]);
});

// PL menu.am testing route
Route::get('/gc-view/pay-later/menu/{order_id?}', function (Request $request) {
    $status = null;
    $order_id = $request->order_id;

    try {
        if ($order_id) {
            $client = new Client();

            $response = $client->post('nginx/api/pl/get-payment', [
                'headers' => [
                    'Client-Identifier' => 'menu_id',
                    'Client-Secret' => 'menu_secret',
                ],
                'form_params' => [
                    'order_id' => $order_id,
                ],
            ]);
            $result = json_decode((string) $response->getBody(), 1);

            $status = count($result['data']);
        }
    } catch (\Exception $e) {
    }

    return view('pay-later', ['order_id' => $order_id, 'status' => $status]);
});

// PL menu.am testing route
Route::post('/gc-view/pay-later/menu', function () {
    $payload = [
        'order_id' => rand(),
        'amount' => 5000,
        'description' => 'menu test',
        'callback_url' => env('APP_URL').'/api/pl/pay-later-callback/menu',
        'currency' => 'AMD',
    ];

    $purchase_service = resolve('App\Services\CreditLine\PurchaseServicePL');

    $client_identifier = 'menu_id';

    $payment_id = $purchase_service->createSession($payload, $client_identifier)->payment_id;

    return view('pay-later', ['url' => env('PAY_LATER_BASE_URL')."/payment?payment_id=$payment_id"]);
});

Route::get('/gc-view/recognition', function (Request $request) {
    $payload = $request->only([
        'from',
        'to',
        'status',
        'ls',
        'sort',
    ]);

    $gc_view_service = resolve('App\Interfaces\IGCViewService');

    $recognitions = $gc_view_service->loanInfo(
        empty($payload['from']) ? Carbon::now()->toDateString() : $payload['from'],
        empty($payload['to']) ? Carbon::now()->addDays(1)->toDateString() : $payload['to'],
        $payload['status'] ?? [],
        $payload['ls'] ?? null,
        $payload['sort'] ?? 'desc'
    );

    $payment_types = [
        'App\Models\CardToCardPayment' => 'Card',
        'App\Models\CashPayment' => 'Cash',
    ];

    $docs = [];

    return view('gc-photos', [
        'recognitions' => $recognitions,
        'payment_types' => $payment_types,
        'docs' => $docs,
    ]);
});

Route::get('/gc-view/recognition-testing', function (Request $request) {
    $payload = $request->only([
        'from',
        'to',
        'status',
        'ls',
        'sort',
    ]);

    $gc_view_service = resolve('App\Interfaces\IGCViewService');

    $available_ids = [];

    $recognitions = $gc_view_service->loanInfo(
        empty($payload['from']) ? Carbon::now()->toDateString() : $payload['from'],
        empty($payload['to']) ? Carbon::now()->addDays(1)->toDateString() : $payload['to'],
        $payload['status'] ?? null,
        $payload['ls'] ?? null,
        $payload['sort'] ?? 'desc',
        $available_ids
    );

    $payment_types = [
        'App\Models\CardToCardPayment' => 'Card',
        'App\Models\CashPayment' => 'Cash',
    ];

    $docs = [];

    return view('gc-photos-test', [
        'recognitions' => $recognitions,
        'payment_types' => $payment_types,
        'docs' => $docs,
    ]);
});

Route::get('/gc-view/recognition-debugging', function (Request $request) {
    $payload = $request->only([
        'from',
        'to',
        'status',
        'ls',
        'sort',
    ]);

    $gc_view_service = resolve('App\Interfaces\IGCViewService');

    $available_ids = [];

    $recognitions = $gc_view_service->loanInfo(
        empty($payload['from']) ? Carbon::now()->toDateString() : $payload['from'],
        empty($payload['to']) ? Carbon::now()->addDays(1)->toDateString() : $payload['to'],
        $payload['status'] ?? null,
        $payload['ls'] ?? null,
        $payload['sort'] ?? 'asc'
    );

    $payment_types = [
        'App\Models\CardToCardPayment' => 'Card',
        'App\Models\CashPayment' => 'Cash',
    ];

    $docs = [];

    return view('gc-photos-debugging', [
        'recognitions' => $recognitions,
        'payment_types' => $payment_types,
        'docs' => $docs,
    ]);
});

Route::get('/gc-view/moderator', function (Request $request) {
    $payload = $request->only([
        'from',
        'to',
        'status',
        'ls',
        'sort',
    ]);

    $gc_view_service = resolve('App\Interfaces\IGCViewService');

    $loan_info = $gc_view_service->moderatorLoanInfo(
        $payload['from'] ?? Carbon::now()->toDateString(),
        $payload['to'] ?? Carbon::now()->addDays(1)->toDateString(),
        $payload['status'] ?? null,
        $payload['ls'] ?? null,
        $payload['sort'] ?? 'asc'
    );

    $payment_types = [
        'App\Models\CardToCardPayment' => 'Card',
        'App\Models\CashPayment' => 'Cash',
    ];

    return view('gc-moderator', [
        'loans' => $loan_info['loans'],
        'confirmed' => $loan_info['confirmed'],
        'rejected' => $loan_info['rejected'],
        'payment_types' => $payment_types,
    ]);
});

Route::get('/gc-view/moderator/videos', function (Request $request) {
    $payload = $request->only([
        'loan_id',
    ]);

    $moderatorService = resolve('App\Interfaces\IModeratorService');

    $urls = $moderatorService->getPresignedUrlsByLoanId($payload['loan_id']);

    return response()->json([
        'urls' => $urls,
    ]);
});

Route::post('/gc-view', function (Request $request) {
    try {
        $payload = $request->only([
            'gcd',
        ]);

        $gc_view_service = resolve('App\Interfaces\IGCViewService');

        $document_number = strtoupper($payload['gcd']);

        $code = $gc_view_service->getIdentityVerificationCode($document_number);

        if (!isset($code)) {
            throw new Exception();
        }

        $gc_view_service->markAsOffline($code);

        $documents = $gc_view_service->getExposedDocuments($code);
    } catch (Exception $e) {
        return view('gc-view', ['error' => 'Տվյալ անձնագրի համար տվյալներ գտնված չեն']);
    }

    return view('gc-view', ['code' => $code, 'documents' => $documents]);
});

// TODO: Make it available in staging only
// Route::get('logs', '\Rap2hpoutre\LaravelLogViewer\LogViewerController@index');

Route::get('process-loan-repayment', '\\App\\Api\\V2\\Controllers\\LoanRepaymentController@processLoanRepaymentWebhook');
