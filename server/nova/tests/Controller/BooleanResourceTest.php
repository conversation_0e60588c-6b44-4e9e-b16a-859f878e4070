<?php

namespace <PERSON><PERSON>\Nova\Tests\Controller;

use <PERSON><PERSON>\Nova\Tests\IntegrationTest;
use <PERSON><PERSON>\Nova\Tests\Fixtures\Boolean;

class BooleanResourceTest extends IntegrationTest
{
    public function setUp()
    {
        parent::setUp();

        $this->authenticate();
    }

    public function test_can_create_boolean_resource_with_true_value()
    {
        $response = $this->withExceptionHandling()
                        ->postJson('/nova-api/booleans', [
                            'active' => true,
                        ]);

        $response->assertStatus(201);

        $boolean = Boolean::first();
        $this->assertEquals('Yes', $boolean->active);

        $response = $this->withExceptionHandling()
                        ->getJson('/nova-api/booleans/1');

        $response->assertStatus(200);
        $fields = $response->original['resource']['fields'];
        $this->assertTrue(collect($fields)->where('attribute', 'active')->first()->value);
    }

    public function test_can_create_boolean_resource_with_false_value()
    {
        $response = $this->withExceptionHandling()
                        ->postJson('/nova-api/booleans', [
                            'active' => false,
                        ]);

        $response->assertStatus(201);

        $boolean = Boolean::first();
        $this->assertEquals('No', $boolean->active);
    }
}
