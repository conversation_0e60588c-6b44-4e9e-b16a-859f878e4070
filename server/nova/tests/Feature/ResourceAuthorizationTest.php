<?php

namespace <PERSON><PERSON>\Nova\Tests\Feature;

use <PERSON><PERSON>\Nova\Nova;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use <PERSON>vel\Nova\Tests\Fixtures\Tag;
use <PERSON><PERSON>\Nova\Tests\IntegrationTest;
use <PERSON>vel\Nova\Tests\Fixtures\TagPolicy;
use <PERSON><PERSON>\Nova\Tests\Fixtures\TagResource;
use <PERSON>vel\Nova\Tests\Fixtures\ForbiddenUserResource;

class ResourceAuthorizationTest extends IntegrationTest
{
    public function setUp()
    {
        parent::setUp();
    }

    public function test_resources_are_not_available_if_not_authorized()
    {
        $available = Nova::availableResources(Request::create('/'));

        $this->assertFalse(in_array(ForbiddenUserResource::class, $available));
    }

    public function test_resource_is_automatically_authorizable_if_it_has_policy()
    {
        $this->assertFalse(TagResource::authorizable());

        Gate::policy(Tag::class, TagPolicy::class);

        $this->assertTrue(TagResource::authorizable());
    }
}
