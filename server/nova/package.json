{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --watch --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "test": "jest", "watch:test": "jest --watch"}, "devDependencies": {"@vue/test-utils": "^1.0.0-beta.16", "axios": "^0.17", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-jest": "^23.0.1", "babel-plugin-syntax-dynamic-import": "^6.18.0", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^3.7.0", "babel-preset-env": "^1.6.1", "cross-env": "^5.0.0", "flush-promises": "^1.0.0", "jest": "^23.0.1", "jest-mock-axios": "^2.1.11", "jest-serializer-vue": "^2.0.0", "jquery": "^3.2.0", "laravel-mix": "^1.0", "lodash": "^4.17.0", "tailwindcss": "^0.6.0", "vue": "^2.5.0", "vue-jest": "^2.6.0", "vue-router": "^3.0.1", "vue-server-renderer": "^2.5.16"}, "dependencies": {"chartist": "^0.11.0", "chartist-plugin-tooltips": "^0.0.17", "codemirror": "^5.36.0", "flatpickr": "^4.4.4", "form-backend-validation": "^2.3.3", "inflector-js": "^1.0.1", "laravel-nova": "^1.0.6", "markdown-it": "^8.4.1", "marked": "^0.3.19", "moment": "^2.22.2", "moment-timezone": "^0.5.21", "numeral": "^2.0.6", "places.js": "^1.7.3", "popper.js": "^1.14.3", "portal-vue": "^1.3.0", "trix": "^1.0.0", "vue-async-computed": "^3.3.1", "vue-clickaway": "^2.1.0", "vue-toasted": "^1.1.24", "vuex": "^3.0.1"}, "jest": {"setupFiles": ["./resources/js/test-setup/bootstrap.js"], "testRegex": "(/resources/js/__tests__/.*|(\\.|/)(test|spec))\\.jsx?$", "moduleFileExtensions": ["js", "json", "vue"], "transform": {"^.+\\.js$": "<rootDir>/node_modules/babel-jest", ".*\\.(vue)$": "<rootDir>/node_modules/vue-jest"}, "snapshotSerializers": ["<rootDir>/node_modules/jest-serializer-vue"], "collectCoverage": false, "collectCoverageFrom": ["**/*.{js,vue}", "!**/node_modules/**"], "moduleNameMapper": {"^@/(.*)$": "<rootDir>/resources/js/$1", "\\.(css|jpg|png)$": "<rootDir>/resources/js/util/empty.js"}}}