<?php

namespace {{ namespace }};

use Lara<PERSON>\Nova\Nova;
use <PERSON><PERSON>\Nova\Events\ServingNova;
use Illuminate\Support\ServiceProvider;

class FilterServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Nova::serving(function (ServingNova $event) {
            Nova::script('{{ component }}', __DIR__.'/../dist/js/filter.js');
            Nova::style('{{ component }}', __DIR__.'/../dist/css/filter.css');
        });
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }
}
