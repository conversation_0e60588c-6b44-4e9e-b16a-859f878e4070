<?php

namespace <PERSON><PERSON>\Nova\Console;

use <PERSON><PERSON>\Nova\Nova;
use Illuminate\Console\Command;

class UserCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'nova:user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a new user';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        Nova::createUser($this);

        $this->info('User created successfully.');
    }
}
