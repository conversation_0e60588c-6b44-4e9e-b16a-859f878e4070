<template>
  <div class="main-container">
    <div class="upload-section">
      <div v-if="processing">
        <span class="form-file mr-4">
          <input
            type="file"
            v-on:click="clearErrors()"
            :id="getDocumentType()"
            multiple
            :accept="getFileType()"
            ref="file"
            class="form-file-input"
            v-on:change="handleFileUpload()"
          />
          <label
            :for="getDocumentType()"
            class="form-file-btn btn btn-default btn-primary"
          >
            {{ __('Choose File(s)') }}
          </label>
          <span class="text-gray-50"> {{ fileName }} </span>
        </span>
        <button
          class="btn btn-default btn-primary"
          v-on:click="uploadFile($event)"
          :disabled="uploadDisabled()"
        >
          {{ __('Upload') }}
        </button>
        <p v-if="hasError" class="text-xs mt-2 text-danger">
          {{ firstError.code }}
        </p>
      </div>
    </div>
    <loading v-if="uploadLoading" large="true" />
    <gallery
      :id="'blueimp-gallery-' + getDocumentType()"
      :images="images"
      :index="index"
      @close="index = null"
    ></gallery>
    <div class="gallery-section">
      <span v-for="image in images" v-bind:key="image.id">
        <a
          v-if="isPdf(image.type)"
          class="image"
          target="_blank"
          :style="{ backgroundImage: 'url(' + image.poster + ')' }"
          :href="image.href"
        >
          <span
            v-if="processing"
            class="remove"
            v-on:click="removeMedia(image, $event)"
          >
            X
          </span>
        </a>
        <div
          v-else
          class="image"
          v-bind:key="image.id"
          @click="index = image.id"
          :style="{ backgroundImage: 'url(' + image.poster + ')' }"
        >
          <span
            v-if="processing"
            class="remove"
            v-on:click="removeMedia(image, $event)"
          >
            X
          </span>
        </div>
      </span>
    </div>
  </div>
</template>

<script>
import _ from 'lodash';
import { Errors } from 'laravel-nova';
import { PDF_ICON, VIDEO_ICON } from '../constants';

export default {
  props: {
    resource: Object,
    resourceName: String,
    resourceId: [String, Number],
    field: Object,
    processing: Boolean,
    documentType: String,
    fileType: String,
    preventParentSubmit: {
      type: Boolean,
      default: false,
    },
  },

  async mounted() {
    this.fileName = this.__('no file(s) selected');
    this.images = await this.getMedia();
  },

  data: () => ({
    files: [],
    images: [],
    index: null,
    fileName: '',
    uploadErrors: new Errors(),
    uploadLoading: false,
    defaultDocumentType: 'media',
    defaultFileType: 'image/*,video/*',
  }),

  methods: {
    uploadDisabled() {
      return this.uploadLoading || !this.files.length;
    },

    clearErrors() {
      this.uploadErrors = new Errors();
    },

    getDocumentType() {
      if (this.documentType) {
        return this.documentType;
      }

      return this.defaultDocumentType;
    },

    getFileType() {
      if (this.fileType) {
        return this.fileType;
      }

      return this.defaultFileType;
    },

    handleFileUpload() {
      if (this.$refs.file.files.length > 1) {
        this.fileName = `${this.$refs.file.files.length} files`;
      } else {
        this.fileName = this.$refs.file.files[0].name;
      }

      this.files = this.$refs.file.files;
    },

    async removeMedia(media, event) {
      if (event) {
        event.stopPropagation();
        event.preventDefault();
      }

      try {
        await Nova.request().post(
          `/nova-vendor/media-view/loan/${this.resourceId}/media/remove`,
          { media }
        );
      } finally {
        // Calling getMedia to refresh mediaUploaded params and reset confirm button if needed
        this.getMedia();
        this.clearErrors();

        this.images = this.images.filter(image => image.id !== media.id);
      }
    },

    uploadFile(event) {
      if (this.preventParentSubmit && event) {
        event.stopPropagation();
        event.preventDefault();
      }

      this.uploadLoading = true;

      _.forEach(this.files, async file => {
        const formData = new FormData();
        formData.set('document_type', this.getDocumentType());
        formData.append('media', file);

        try {
          await Nova.request().post(
            `/nova-vendor/media-view/loan/${this.resourceId}/media`,
            formData,
            {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
            }
          );

          this.uploadLoading = false;
        } catch (error) {
          this.uploadLoading = false;
          this.uploadErrors = new Errors(error.response.data.errors);
        } finally {
          this.images = await this.getMedia();
          this.resetFileInput();
        }
      });
    },

    resetFileInput() {
      this.$refs.file.value = null;
      this.fileName = this.__('no file(s) selected');
    },

    async getMedia() {
      const resp = await Nova.request().get(
        `/nova-vendor/media-view/loan/${
          this.resourceId
        }/media?document_type=${this.getDocumentType()}`
      );

      this.$emit('mediaUploaded', resp.data.length);

      return resp.data.map((image, index) => {
        return this.composeGalleryItem(image, index);
      });
    },

    composeGalleryItem(item, index) {
      return {
        id: index,
        href: `${item.full_path}`,
        type: item.type,
        poster: this.getPoster(item),
        key: item.id,
      };
    },

    getPoster(item) {
      if (item.type.includes('video')) {
        return VIDEO_ICON;
      } else if (item.type.includes('pdf')) {
        return PDF_ICON;
      }

      return `${item.full_path}`;
    },

    isPdf(type) {
      return type.includes('pdf');
    },
  },

  computed: {
    hasError() {
      return this.uploadErrors.has('media');
    },

    firstError() {
      if (this.hasError) {
        return this.uploadErrors.first('media');
      }
    },
  },
};
</script>
