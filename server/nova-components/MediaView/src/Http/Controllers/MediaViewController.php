<?php

namespace Globalcredit\MediaView\Http\Controllers;

use App\Models\Loan;
use Globalcredit\MediaView\Services\MediaViewServiceFactory;
use Illuminate\Routing\Controller;
use Laravel\Nova\Http\Requests\NovaRequest;

class MediaViewController extends Controller
{
    public function getMedia(NovaRequest $request)
    {
        $loan = Loan::find($request->id);

        $service = MediaViewServiceFactory::build($loan->loan_type_id);

        return $service->getMedia($request->id, $request->document_type);
    }

    public function storeMedia(NovaRequest $request)
    {
        $request->validate([
            'media' => 'required|mimes:mp4,png,jpg,jpeg,pdf',
        ]);

        $loan = Loan::find($request->id);

        $service = MediaViewServiceFactory::build($loan->loan_type_id);

        return $service->storeMedia($request->id, $request->media, $request->document_type);
    }

    public function removeMedia(NovaRequest $request)
    {
        $loan = Loan::find($request->id);

        $service = MediaViewServiceFactory::build($loan->loan_type_id);

        return $service->removeMedia($request->id, $request->media);
    }
}
