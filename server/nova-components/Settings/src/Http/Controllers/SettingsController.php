<?php

namespace Globalcredit\Settings\Http\Controllers;

use App\Interfaces\ISettingsService;
use App\Models\LoanSubType;
use App\Models\LoanType;
use App\Models\SettingsStatus;
use Globalcredit\Settings\Http\Requests\SettingsRequest;

class SettingsController
{
    private $settings_service;

    public function __construct(ISettingsService $settings_service)
    {
        $this->settings_service = $settings_service;
    }

    public function index()
    {
        return response()->json($this->settings_service->getAllSettings());
    }

    public function update(SettingsRequest $request)
    {
        $payload = $request->only([
            'name',
            'disabled',
            'sectionId',
        ]);

        if ($payload['sectionId'] == SettingsStatus::GENERAL_SECTION_TYPE) {
            $model = SettingsStatus::whereName($payload['name'])->first();
        } else {
            $model = LoanType::whereName($payload['name'])->first() ?? LoanSubtype::whereName($payload['name'])->first();
        }
        $this->settings_service->updateStatus($payload, $model);

        return response()->json([
            'success' => true,
        ]);
    }
}
