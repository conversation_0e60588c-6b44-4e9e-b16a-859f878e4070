Nova.booting((Vue, router, store) => {
    router.addRoutes([
        {
            name: 'settings',
            path: '/settings',
            component: require('./components/Tool'),
        },
    ])
    Vue.component('confirm-modal', require('./components/ConfirmModal'));
    Vue.component('loading', require('./components/Loading'));
    Vue.component('loading', require('./components/Switch'));

})
