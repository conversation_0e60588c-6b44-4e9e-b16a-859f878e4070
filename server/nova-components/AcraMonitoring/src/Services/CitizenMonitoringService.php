<?php

namespace Globalcredit\AcraMonitoring\Services;

use App\Exceptions\InvalidExcelFormatException;
use App\Factory\AcraServiceFactory;
use App\Helpers\ArrayHelper;
use App\Helpers\PassportHelper;
use App\Models\CitizenMonitoring;
use Carbon\Carbon;
use Exception;
use function Functional\first;
use function Functional\reduce_left;
use Log;
use Rap2hpoutre\FastExcel\Facades\FastExcel;

class CitizenMonitoringService
{
    protected $redis_service;

    public function __construct()
    {
        $this->redis_service = resolve('App\Interfaces\IRedisService');
    }

    public function parseCitizensExcel($file)
    {
        $citizens = FastExcel::import($file);
        $excel_keys = array_keys($citizens[0]);

        Log::info('Parse Citizens Excels', ['excel_keys' => $excel_keys]);

        if (count(array_diff(constants('ACRA_MONITORING.EXCEL_KEYS'), $excel_keys))) {
            throw new InvalidExcelFormatException();
        }

        $file_name = Carbon::now()->format(constants('FILE_MONITORING_FORMAT')).'xlsx';
        $file->storeAs('/', $file_name, 'monitoring_uploaded_files');

        return $citizens;
    }

    public function composeAcraCitizensList($citizens)
    {
        $acra_citizens = reduce_left($citizens, function ($acra_citizen, $key, $collection, $reduction) {
            if (!empty($acra_citizen[constants('ACRA_MONITORING.EXCEL_KEYS.CONTRACT_NUMBER')])) {
                array_push($reduction, [
                    'ContractNumber' => $acra_citizen[constants('ACRA_MONITORING.EXCEL_KEYS.CONTRACT_NUMBER')],
                    'PassportNumber' => $acra_citizen[constants('ACRA_MONITORING.EXCEL_KEYS.DOCUMENT_NUMBER')],
                    'IdCardNumber' => PassportHelper::isIdCard($acra_citizen[constants('ACRA_MONITORING.EXCEL_KEYS.DOCUMENT_NUMBER')]) ?
                        $acra_citizen[constants('ACRA_MONITORING.EXCEL_KEYS.DOCUMENT_NUMBER')] : null,
                    'SocCardNumber' => $acra_citizen[constants('ACRA_MONITORING.EXCEL_KEYS.SSN')],
                    'FirstName' => $acra_citizen[constants('ACRA_MONITORING.EXCEL_KEYS.FIRST_NAME')],
                    'LastName' => $acra_citizen[constants('ACRA_MONITORING.EXCEL_KEYS.LAST_NAME')],
                    'DateofBirth' => $this->composeDateOfBirth($acra_citizen[constants('ACRA_MONITORING.EXCEL_KEYS.BIRTH_DATE')]),
                ]);
            }

            return $reduction;
        }, []);

        Log::info('Get Citizens From List', ['citizens', $acra_citizens]);

        return $acra_citizens;
    }

    private function composeDateOfBirth($birth_date)
    {
        if (gettype($birth_date) === 'string') {
            return Carbon::parse($birth_date)->format(constants('STANDARD_DASHED_DATE_FORMAT'));
        }

        return $birth_date->format(constants('STANDARD_DASHED_DATE_FORMAT'));
    }

    public function monitoring($citizens, $report_type, $batch_id)
    {
        $monitoring_service = AcraServiceFactory::build(
            constants('ACRA.TYPE.MONITORING'),
            $report_type
        );

        $type = $this->resolveMonitoringType($report_type);

        // TODO: collect data in excel and show in case of failed monitoring
        try {
            $acra_citizens = $monitoring_service->getCitizens($citizens);
        } catch (Exception $e) {
            Log::info('Monitoring failed for citizens chunk', ['message' => $e->getMessage(), 'citizens' => $citizens]);

            return;
        }

        if (ArrayHelper::is_assoc($acra_citizens['PARTICIPIENT'])) {
            $acra_citizens['PARTICIPIENT'] = [$acra_citizens['PARTICIPIENT']];
        }

        foreach ($acra_citizens['PARTICIPIENT'] ?? [] as $acra_citizen) {
            try {
                if (!isset($acra_citizen['SocCardNumber']) || empty($acra_citizen['SocCardNumber'])) {
                    Log::info("Monitoring $type Acra Error", ['acra_citizen' => $acra_citizen]);

                    continue;
                }

                $ssn = $acra_citizen['SocCardNumber'];
                $citizen = first($citizens, function ($citizen) use ($ssn) {
                    return str_contains($ssn, $citizen['SocCardNumber']);
                });

                $result = [
                    'request_target' => constants('ACRA_LOGIN.MONITORING_VALUES.RequestTarget'),
                    'contract_number' => $citizen['ContractNumber'],
                    'content' => json_encode(array_merge(['acra' => $acra_citizen], [
                        'timestamp' => Carbon::now()->timestamp,
                    ]), JSON_UNESCAPED_UNICODE),
                    'batch_id' => $batch_id,
                ];
            } catch (Exception $e) {
                Log::error("Monitoring $type Exception", ['acra_citizens' => $acra_citizens, 'message' => $e->getMessage()]);
                // TODO: collect data in excel and show in case of failed monitoring
                continue;
            }

            Log::info('Citizen Monitoring Service Monitoring', ['type' => $type]);

            CitizenMonitoring::create($result);
        }
    }

    public function startMonitoring($file_name)
    {
        Log::info('Start monitoring', ['file name' => $file_name]);

        $this->redis_service->update(constants('ACRA_MONITORING.REDIS_KEY'), [
            'status' => constants('NOVA.MONITORING_STATUSES.PENDING'),
            'name' => $file_name,
            ],
            constants('ONE_MONTH_IN_MINUTES')
        );
    }

    public function stopMonitoring($status)
    {
        $monitoring = $this->getMonitoringState();

        $this->redis_service->update(constants('ACRA_MONITORING.REDIS_KEY'), array_merge($monitoring, ['status' => $status]), constants('ONE_MONTH_IN_MINUTES'));

        Log::info('Stop monitoring', ['status' => $status]);
    }

    public function getMonitoringState()
    {
        return $this->redis_service->get(constants('ACRA_MONITORING.REDIS_KEY'));
    }

    private function resolveMonitoringType($type)
    {
        switch ($type) {
            case '01':
                return 'MONITORING_WITHOUT_FICO';

            case '02':
                return 'MONITORING_RETRO';

            default:
                return 'MONITORING_ONLY_FICO';
        }
    }
}
