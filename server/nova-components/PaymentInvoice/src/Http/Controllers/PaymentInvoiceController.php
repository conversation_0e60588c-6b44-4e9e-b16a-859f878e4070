<?php

namespace Globalcredit\PaymentInvoice\Http\Controllers;

use App\Exceptions\InternalErrorException;
use App\Exceptions\TaxService\TaxServiceGetInvoicesException;
use App\Exceptions\TaxService\TaxServiceLoginFailureException;
use App\Helpers\DateHelper;
use App\Helpers\NumberHelper;
use App\Helpers\StringHelper;
use App\Helpers\UnicodeToAnsi;
use App\Http\Controllers\Controller;
use App\Services\TaxService\Common\ConditionBuilder;
use App\Services\TaxService\Dto\Entities\InvoiceDto;
use App\Services\TaxService\Dto\Entities\TaxServiceSoapClientDto;
use App\Services\TaxService\Dto\Requests\InvoiceDetailsByIdRequestDto;
use App\Services\TaxService\Dto\Requests\InvoiceListRequestDto;
use App\Services\TaxService\Dto\Requests\InvoiceProductDetailsByIdRequestDto;
use App\Services\TaxService\Dto\Responses\InvoiceListResponseDto;
use App\Services\TaxService\EInvoicingService;
use Carbon\Carbon;
use Exception;
use function Functional\first;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Http\Requests\NovaRequest;
use Throwable;

class PaymentInvoiceController extends Controller
{
    public function export(NovaRequest $request)
    {
        try {
            $request->validate([
                'username' => 'required',
                'password' => 'required',
                'dates' => 'required',
            ]);

            $e_invoicing_service = new EInvoicingService($this->createTaxServiceSoapClientDto($request));

            $invoice_list = $e_invoicing_service->getInvoiceList($this->createInvoiceListRequestDto($request));

            $warehouse_service = resolve('App\Interfaces\IWarehouseService');
            $debit_accounts = $warehouse_service->getDebitAccounts();

            foreach ($invoice_list->getPayload() as $invoice) {
                $invoice_details = $e_invoicing_service->getInvoiceDetailsById($this->createInvoiceDetailsByIdRequestDto($invoice), $invoice);

                $invoice_product_details = $e_invoicing_service->getInvoiceProductDetailsById($this->createInvoiceProductDetailsByIdRequestDto($invoice), $invoice);

                $invoice->setValue('supplierDebitAccount', $this->resolveSupplierDebitAccount($debit_accounts, $invoice->getValue('supplierTin')));
                $invoice->setValue('details', $invoice_details->getPayload());
                $invoice->setValue('productDetails', $invoice_product_details->getPayload());
            }

            $download_link = $this->processDownloadAction($invoice_list);

            return response()->json($download_link);
        } catch (ValidationException | TaxServiceLoginFailureException | TaxServiceGetInvoicesException $e) {
            Log::warning('Payment Invoice Export, '.get_class($e), ['message' => $e->getMessage()]);

            throw $e;
        } catch (Throwable $e) {
            Log::error('Payment Invoice Export, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw new InternalErrorException();
        }
    }

    private function resolveSupplierDebitAccount($debit_accounts, $supplier_tin)
    {
        $found_debit_account = first($debit_accounts, function ($el) use ($supplier_tin) {
            return trim($el['TAXCOD']) == trim($supplier_tin);
        });

        return $found_debit_account['account'];
    }

    private function createTaxServiceSoapClientDto(NovaRequest $request): TaxServiceSoapClientDto
    {
        $dto = new TaxServiceSoapClientDto();
        $dto->setValue('username', $request->username);
        $dto->setValue('password', $request->password);

        return $dto;
    }

    private function createInvoiceListRequestDto(NovaRequest $request): InvoiceListRequestDto
    {
        // Because E-invoicing using UTC, that's why we need to subtract 4 hours to match with it
        $start_date = Carbon::parse($request->dates['start'], constants('ARM_TIMEZONE'))->tz(constants('UTC_TIME'));
        $end_date = Carbon::parse($request->dates['end'], constants('ARM_TIMEZONE'))->endOfDay()->tz(constants('UTC_TIME'));

        $dto = new InvoiceListRequestDto();
        $builder = ConditionBuilder::make();
        $builder->where('approvedAt', '>=', $start_date);
        $builder->where('approvedAt', '<=', $end_date);
        $builder->where('status', '=', 'APPROVED');

        $dto->setValue('condition', $builder->build());

        return $dto;
    }

    private function createInvoiceDetailsByIdRequestDto(InvoiceDto $invoice_dto): InvoiceDetailsByIdRequestDto
    {
        $dto = new InvoiceDetailsByIdRequestDto();

        $dto->setValue('id', $invoice_dto->getValue('id'));

        return $dto;
    }

    private function createInvoiceProductDetailsByIdRequestDto(InvoiceDto $invoice_dto): InvoiceProductDetailsByIdRequestDto
    {
        $dto = new InvoiceProductDetailsByIdRequestDto();

        $dto->setValue('invoiceId', $invoice_dto->getValue('id'));

        return $dto;
    }

    public function processDownloadAction(InvoiceListResponseDto $invoice_list_response_dto): array
    {
        $now = now(constants('ARM_TIMEZONE'))->format(constants('FILE_TIME_FORMAT'));
        $file_name = "payment_invoices_$now.txt";
        $path = env('NOVA_DOWNLOADS_PATH');
        $file_path = public_path("$path/$file_name");

        try {
            $file = $this->createFile($file_path, $file_name);

            $this->writeHeaderToFile($file);
            // Write each invoice to the file
            foreach ($invoice_list_response_dto->getPayload() as $index => $data) {
                $this->writeInvoiceToFile($file, $index, $data);
            }

            fclose($file);

            Log::info('Payment Invoice File created', ['file_name' => $file_name]);

            return Action::download("$path/$file_name", $file_name);
        } catch (Exception $e) {
            Log::error('processDownloadAction Payment Invoices, Exception', ['error' => $e->getMessage()]);

            throw new InternalErrorException();
        }
    }

    private function createFile(string $file_path, string $file_name)
    {
        $file = fopen($file_path, 'wb');

        if (!$file) {
            Log::info('Error creating Payment Invoice file', ['file_name' => $file_name]);
            throw new InternalErrorException('Error creating Payment Invoice file');
        }

        return $file;
    }

    private function writeHeaderToFile($file)
    {
        fwrite($file, "#AS3XX# EXPORT-IMPORT DATA FILE\n\n");
    }

    /**
     * @throws Exception
     */
    private function writeInvoiceToFile($file, $index, InvoiceDto $data)
    {
        try {
            $currency = '000';
            $branch = '00';
            $department = '001';

            $invoice_as_array = $data->toArray();

            $current_date = now()->setTimezone(constants('ARM_TIMEZONE'))->format(constants('PAYMENT_INVOICE_EXPORT_DATE_FORMAT'));
            $issued_date = Carbon::parse($invoice_as_array['issued_at'])->setTimezone(constants('ARM_TIMEZONE'));
            $aim = DateHelper::composeLocalizedDateV2($issued_date).' '.$invoice_as_array['serial_no'].' '.StringHelper::removeUnwantedCharacters(reset($invoice_as_array['product_details'])['name'], '/[A-Za-z<>«»]/u');
            $resolved_total = NumberHelper::roundToNearestTenth($invoice_as_array['resolved_total']);

            $lines = [
                'CrPayOrd {4',
                'DOCNUM:'.($index + 1),
                'DATE:'.$current_date,
                'ACSBRANCH:'.$branch,
                'ACSDEPART:'.$department,
                'ACCDB:'.$invoice_as_array['supplier_debit_account'],
                'PAYER:'.UnicodeToAnsi::convert(StringHelper::removeUnwantedCharacters($invoice_as_array['buyer_name'])),
                'ACCCR:'.$invoice_as_array['details']['supplier_acc_no'],
                'RECEIVER:'.UnicodeToAnsi::convert(StringHelper::removeUnwantedCharacters($invoice_as_array['supplier_name'])),
                'SUMMA:'.$resolved_total,
                'CUR:'.$currency,
                'AIM:'.UnicodeToAnsi::convert(StringHelper::removeUnwantedCharacters($aim)),
                "}\n",
            ];

            foreach ($lines as $line) {
                // We need to convert every line from UTF-8 to ANSI make sure font formatting is okay in HC
                $ansi_line = iconv('UTF-8', 'Windows-1252//TRANSLIT', $line);
                fwrite($file, $ansi_line."\n");
            }
        } catch (Exception $e) {
            Log::error('Error writing payment Invoice to file', [
                'data' => $data,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }
}
