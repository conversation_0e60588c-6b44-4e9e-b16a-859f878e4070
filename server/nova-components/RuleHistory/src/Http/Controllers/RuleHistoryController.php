<?php

namespace Globalcredit\RuleHistory\Http\Controllers;

use App\Exceptions\RuleSetExistenceException;
use App\Models\LoanType;
use Database\Seeders\RulesTableBNPLSeeder;
use Database\Seeders\RulesTableOASLSeeder;
use Database\Seeders\RulesTableOBLSeeder;
use Database\Seeders\RulesTableOCLSeeder;
use Database\Seeders\RulesTableOEPLSeeder;
use Database\Seeders\RulesTableOFSLSeeder;
use Database\Seeders\RulesTableOIDLSeeder;
use Database\Seeders\RulesTableOIQLSeeder;
use Database\Seeders\RulesTableOIWLSeeder;
use Database\Seeders\RulesTableOTCLSeeder;
use Database\Seeders\RulesTableOUPLSeeder;
use Database\Seeders\RulesTableOVLSeeder;
use Database\Seeders\RulesTablePLSeeder;
use Database\Seeders\RulesTableREMLSeeder;
use Database\Seeders\RulesTableVLXSeeder;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Log;
use Laravel\Nova\Http\Requests\NovaRequest;
use Throwable;

class RuleHistoryController extends Controller
{
    const ALL_LOAN_TYPES_IDENTIFIER = 9999;

    public function getLoanTypes()
    {
        return array_merge([
            [
                'id' => self::ALL_LOAN_TYPES_IDENTIFIER,
                'name' => 'All Loan Types',
            ],
        ], $this->getLoanTypesAsArray());
    }

    public function getLoanTypesAsArray()
    {
        return LoanType::where('id', '!=', constants('LOAN_TYPES.COMMON'))
            ->where('disabled', false)
            ->get(['id', 'name'])
            ->toArray();
    }

    public function seed(NovaRequest $request)
    {
        $loan_type = intval($request->loan_type);

        if ($loan_type === self::ALL_LOAN_TYPES_IDENTIFIER) {
            $this->seedAllLoanTypes();
        } else {
            $this->seedSingleLoanType($loan_type);
        }
    }

    protected function seedAllLoanTypes()
    {
        $loan_types = $this->getLoanTypesAsArray();
        $updated_loan_types = [];
        $existing_loan_types = [];

        foreach ($loan_types as $loan_type) {
            try {
                $this->runSeeder($loan_type['id']);
                $updated_loan_types[] = $loan_type['id'];
            } catch (RuleSetExistenceException $e) {
                Log::error('seedAllLoanTypes, RuleSetExistenceException', ['loan_type' => $loan_type, 'error' => $e->getMessage()]);

                $existing_loan_types[] = $loan_type['id'];

                // If it's last loop operation and there are not updated rule sets of loan types,
                //throwing exception to show the appropriate message in the UI
                if (!next($loan_types) && empty($updated_loan_types)) {
                    Log::info('All loan types seeder run completed', [
                        'updated_loan_types' => $updated_loan_types,
                        'existing_loan_types' => $existing_loan_types,
                    ]);

                    throw $e;
                }
            } catch (Throwable $e) {
                Log::error('seedAllLoanTypes, seeding loan type exception', ['loan_type' => $loan_type, 'error' => $e->getMessage()]);

                throw $e;
            }
        }

        Log::info('All loan types seeder run completed', [
            'updated_loan_types' => $updated_loan_types,
            'existing_loan_types' => $existing_loan_types,
        ]);
    }

    protected function seedSingleLoanType($loan_type)
    {
        try {
            $this->runSeeder($loan_type);
            Log::info('Loan types seeder run completed', ['loan_type' => $loan_type]);
        } catch (Throwable $e) {
            Log::error('seedSingleLoanType, seeding loan type exception', ['loan_type' => $loan_type, 'error' => $e->getMessage()]);

            throw $e;
        }
    }

    protected function runSeeder($loan_type)
    {
        $seed = $this->getSeed($loan_type);
        $seed->run();
    }

    protected function getSeed($loan_type)
    {
        switch ($loan_type) {
            case constants('LOAN_TYPES.OCL'):
                return new RulesTableOCLSeeder();
            case constants('LOAN_TYPES.OVL'):
                return new RulesTableOVLSeeder();
            case constants('LOAN_TYPES.OIQL'):
                return new RulesTableOIQLSeeder();
            case constants('LOAN_TYPES.OASL'):
                return new RulesTableOASLSeeder();
            case constants('LOAN_TYPES.OIDL'):
                return new RulesTableOIDLSeeder();
            case constants('LOAN_TYPES.PL'):
                return new RulesTablePLSeeder();
            case constants('LOAN_TYPES.OTCL'):
                return new RulesTableOTCLSeeder();
            case constants('LOAN_TYPES.OEPL'):
                return new RulesTableOEPLSeeder();
            case constants('LOAN_TYPES.OUPL'):
                return new RulesTableOUPLSeeder();
            case constants('LOAN_TYPES.REML'):
                return new RulesTableREMLSeeder();
            case constants('LOAN_TYPES.VLX'):
                return new RulesTableVLXSeeder();
            case constants('LOAN_TYPES.BNPL'):
                return new RulesTableBNPLSeeder();
            case constants('LOAN_TYPES.OIWL'):
                return new RulesTableOIWLSeeder();
            case constants('LOAN_TYPES.OBL'):
                return new RulesTableOBLSeeder();
            case constants('LOAN_TYPES.OFSL'):
                return new RulesTableOFSLSeeder();
            default:
                return null;
        }
    }
}
