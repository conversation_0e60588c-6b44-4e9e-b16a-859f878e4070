<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Velox Loan Offer API Documentation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #2980b9;
            margin-top: 30px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        h3 {
            color: #3498db;
            margin-top: 25px;
        }
        code {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            padding: 2px 4px;
            color: #c7254e;
        }
        pre {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 10px;
            overflow: auto;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .endpoint {
            background-color: #e8f4fc;
            padding: 10px;
            border-left: 4px solid #3498db;
            margin: 15px 0;
        }
        .method {
            font-weight: bold;
            color: #fff;
            padding: 3px 6px;
            border-radius: 3px;
            margin-right: 10px;
        }
        .get {
            background-color: #61affe;
        }
        .post {
            background-color: #49cc90;
        }
        .delete {
            background-color: #f93e3e;
        }
        .put {
            background-color: #fca130;
        }
    </style>
</head>
<body>
<h1>Velox Loan Offer API Documentation - Version 1.1.0</h1>

<h2>Overview</h2>
<p>The Velox Loan Offer API provides functionality for processing loan applications through the Velox system. It consists of three main endpoints:</p>
<ol>
    <li><strong>Loan Offer</strong> - Initiates a loan application and returns loan offer details</li>
    <li><strong>Loan Approve</strong> - Approves a loan offer and processes the loan</li>
    <li><strong>Loan Offer Status</strong> - Checks the status of a loan offer</li>
</ol>

<h2>Base URL</h2>
<pre><code>https://cashme.am/api/velox</code></pre>

<h2>Content Types and Headers</h2>
<p>The Velox API accepts and returns data in JSON format. You should include the following headers in all requests:</p>

<table>
    <tr>
        <th>Header</th>
        <th>Value</th>
        <th>Description</th>
    </tr>
    <tr>
        <td>Content-Type</td>
        <td>application/json</td>
        <td>Indicates that the request body is in JSON format</td>
    </tr>
    <tr>
        <td>Accept</td>
        <td>application/json</td>
        <td>Indicates that the response should be in JSON format</td>
    </tr>
</table>

<h3>Example Request Headers</h3>
<pre><code>Content-Type: application/json
Accept: application/json
</code></pre>

<h3>Character Encoding</h3>
<p>All requests and responses use UTF-8 character encoding. Make sure to properly encode any non-ASCII characters, especially when sending Armenian text.</p>

<h2>Endpoints</h2>

<h3>1. Loan Offer</h3>
<div class="endpoint">
    <span class="method post">POST</span> <code>/loan-offer</code>
</div>
<p>Initiates a loan application and returns loan offer details.</p>

<h4>Request Parameters:</h4>
<table>
    <tr>
        <th>Parameter</th>
        <th>Type</th>
        <th>Required</th>
        <th>Description</th>
    </tr>
    <tr>
        <td>applicationId</td>
        <td>string</td>
        <td>No</td>
        <td>Unique identifier for the loan application</td>
    </tr>
    <tr>
        <td>loanAmount</td>
        <td>numeric</td>
        <td>Yes</td>
        <td>The requested loan amount in AMD</td>
    </tr>
    <tr>
        <td>loanDuration</td>
        <td>numeric</td>
        <td>Yes</td>
        <td>The requested loan duration in months</td>
    </tr>
    <tr>
        <td>interest</td>
        <td>numeric</td>
        <td>No</td>
        <td>The annual interest rate percentage</td>
    </tr>
    <tr>
        <td>actualIncome</td>
        <td>numeric</td>
        <td>No</td>
        <td>Applicant's actual income amount</td>
    </tr>
    <tr>
        <td>oti</td>
        <td>numeric</td>
        <td>No</td>
        <td>Obligation-to-Income ratio (between 0 and 1)</td>
    </tr>
    <tr>
        <td>availableMonthlyDeptPayment</td>
        <td>numeric</td>
        <td>No</td>
        <td>Available monthly payment amount for debt</td>
    </tr>
    <tr>
        <td>currentPaymentFromCreditBureau</td>
        <td>numeric</td>
        <td>No</td>
        <td>Current monthly payment amount from credit bureau report</td>
    </tr>
    <tr>
        <td>ekengData</td>
        <td>object</td>
        <td>Yes</td>
        <td>EKENG (Electronic Government) data for the applicant</td>
    </tr>
    <tr>
        <td>norqData</td>
        <td>object</td>
        <td>Yes</td>
        <td>NORK (Workplace Register) data for the applicant</td>
    </tr>
    <tr>
        <td>acraData</td>
        <td>object</td>
        <td>Yes</td>
        <td>ACRA (Credit Bureau) data for the applicant</td>
    </tr>
    <tr>
        <td>totalPledgePrice</td>
        <td>numeric</td>
        <td>No</td>
        <td>Total price of all pledged items</td>
    </tr>
    <tr>
        <td>productCategoryId</td>
        <td>string</td>
        <td>Yes</td>
        <td>ID of the product category</td>
    </tr>
    <tr>
        <td>productCategoryName</td>
        <td>string</td>
        <td>Yes</td>
        <td>Name of the product category</td>
    </tr>
    <tr>
        <td>merchantId</td>
        <td>string</td>
        <td>No</td>
        <td>Unique identifier of the merchant</td>
    </tr>
    <tr>
        <td>merchantName</td>
        <td>string</td>
        <td>Yes</td>
        <td>Name of the merchant</td>
    </tr>
    <tr>
        <td>merchantAddress</td>
        <td>string</td>
        <td>Yes</td>
        <td>Address of the merchant</td>
    </tr>
    <tr>
        <td>pledge</td>
        <td>array</td>
        <td>Yes</td>
        <td>Array of pledged items. Each item contains:
            <ul>
                <li><strong>name</strong>: Name or description of the pledged item</li>
                <li><strong>price</strong>: Market price of the pledged item in AMD</li>
                <li><strong>ratio</strong>: Collateral value of the pledged item in AMD</li>
            </ul>
        </td>
    </tr>
    <tr>
        <td>documentNumber</td>
        <td>string</td>
        <td>Yes</td>
        <td>Applicant's document number (passport or ID card)</td>
    </tr>
    <tr>
        <td>phoneNumber</td>
        <td>string</td>
        <td>Yes</td>
        <td>Applicant's phone number in international format</td>
    </tr>
</table>

<h4>Example Request:</h4>
<pre><code>{
  "applicationId": "abc123de-f456-789g-hijk-lmnopqrst012",
  "actualIncome": 0,
  "oti": 0.5,
  "availableMonthlyDeptPayment": 0,
  "phoneNumber": "+37477000000",
  "currentPaymentFromCreditBureau": 85000.50,
  "loanAmount": 2000000,
  "loanDuration": 36,
  "interest": 24,
  "totalPledgePrice": 4000000,
  "productCategoryId": "5",
  "productCategoryName": "Շինանյութ և վերանորոգման պարագաներ",
  "merchantId": "12345678-abcd-efgh-ijkl-987654321xyz",
  "merchantName": "ՆՈՐ ՏՈՒՆ-ԱՐՏԱՇԱՏ",
  "merchantAddress": "ԱՐԱՐԱՏ, Ք. ԱՐՏԱՇԱՏ, Օգոստոսի 23-ի փ. 93/1",
  "ekengData": {
    ...
    "passport_data": {
      "PNum": "0000000000",
      "DocumentType": "NON_BIOMETRIC_PASSPORT",
      "DocumentNumber": "*********"
    },
    ...
  },
  "norqData": {
    "content": "base64-encoded-content"
  },
  "acraData": {
    "content": "base64-encoded-content",
    "encoding": "base64"
  },
  "pledge": [
    {
      "name": "ՆՈՐ ՏՈՒՆ-ԱՐՏԱՇԱՏ Շինանյութ և վերանորոգման պարագաներ",
      "price": 2000000,
      "ratio": 4000000.0
    },
    {
      "name": "ՆՈՐ ՏՈՒՆ-ԱՐՏԱՇԱՏ Շինանյութ և վերանորոգման պարագաներ",
      "price": 2000000,
      "ratio": 4000000.0
    }
  ],
  "documentNumber": "*********",
  "phoneNumber": "+37477000000"
}</code></pre>

<h4>Response:</h4>
<pre><code>{
  "data": {
    "suuid": "00000000-0000-0000-0000-000000000000",
    "contractNumber": "VLX-000000",
    "loanCode": "VLX000000",
    "loanLimit": 2000000,
    "loanPercentage": 24,
    "loanServicePercentage": 0
  }
}</code></pre>

<h4>Response Parameters:</h4>
<table>
    <tr>
        <th>Parameter</th>
        <th>Type</th>
        <th>Description</th>
    </tr>
    <tr>
        <td>data</td>
        <td>object</td>
        <td>Container for response data</td>
    </tr>
    <tr>
        <td>data.suuid</td>
        <td>string</td>
        <td>Unique system identifier for the loan offer</td>
    </tr>
    <tr>
        <td>data.contractNumber</td>
        <td>string</td>
        <td>Generated contract number for the loan</td>
    </tr>
    <tr>
        <td>data.loanCode</td>
        <td>string</td>
        <td>Unique code for the loan</td>
    </tr>
    <tr>
        <td>data.loanLimit</td>
        <td>numeric</td>
        <td>Maximum approved loan amount in AMD</td>
    </tr>
    <tr>
        <td>data.loanPercentage</td>
        <td>numeric</td>
        <td>Annual interest rate percentage</td>
    </tr>
    <tr>
        <td>data.loanServicePercentage</td>
        <td>numeric</td>
        <td>Service fee percentage (if applicable)</td>
    </tr>
</table>

<h3>Product Categories</h3>
<p>The Velox API supports the following product categories. The <code>productCategoryId</code> parameter must be one of these values and the category must be active in the system.</p>

<table>
    <tr>
        <th>Category ID</th>
        <th>Category Name</th>
        <th>Description</th>
    </tr>
    <tr>
        <td>1</td>
        <td>Բջջային հեռախոսներ և դյուրակիր համակարգիչներ</td>
        <td>Mobile phones and laptops</td>
    </tr>
    <tr>
        <td>2</td>
        <td>Խառը ապրանքներ (այդ թվում նաև բջջ․հեռախոսներ)</td>
        <td>Mixed goods (including mobile phones)</td>
    </tr>
    <tr>
        <td>3</td>
        <td>Կենցաղային տեխնիկա հիմնական</td>
        <td>Household appliances</td>
    </tr>
    <tr>
        <td>4</td>
        <td>Կահույք</td>
        <td>Furniture</td>
    </tr>
    <tr>
        <td>5</td>
        <td>Շինանյութ և վերանորոգման պարագաներ</td>
        <td>Construction materials and renovation supplies</td>
    </tr>
    <tr>
        <td>6</td>
        <td>Խառը ապրանքներ (բջջ․հեռախոսներ բացակայում են)</td>
        <td>Mixed goods (excluding mobile phones)</td>
    </tr>
    <tr>
        <td>7</td>
        <td>Անվադողեր</td>
        <td>Tires</td>
    </tr>
    <tr>
        <td>8</td>
        <td>Օծանելիք, շպար և խնամք</td>
        <td>Perfumes, makeup and care products</td>
    </tr>
    <tr>
        <td>9</td>
        <td>Գյուղատնտեսական պարագաներ և տեխնիկա</td>
        <td>Agricultural equipment and machinery</td>
    </tr>
    <tr>
        <td>10</td>
        <td>Վարսավիրական պարագաներ և գործիքներ</td>
        <td>Hairdressing supplies and tools</td>
    </tr>
    <tr>
        <td>11</td>
        <td>ՀԴՄ Սարքավորումներ</td>
        <td>Cash register equipment</td>
    </tr>
    <tr>
        <td>12</td>
        <td>Ավտոպահեստամասեր, ավտոպարագաներ</td>
        <td>Auto parts and accessories</td>
    </tr>
    <tr>
        <td>13</td>
        <td>Ավտոսպասարկման սարքավորումներ, գործիքներ</td>
        <td>Auto service equipment and tools</td>
    </tr>
    <tr>
        <td>14</td>
        <td>Խառը Ավտոպահեստամասեր և սաքավորումներ</td>
        <td>Mixed auto parts and equipment</td>
    </tr>
    <tr>
        <td>20</td>
        <td>Ծառայություններ-էստետիկ բժշկություն</td>
        <td>Services - aesthetic medicine</td>
    </tr>
    <tr>
        <td>21</td>
        <td>Ծառայություններ-բժշկություն</td>
        <td>Services - medicine</td>
    </tr>
    <tr>
        <td>23</td>
        <td>Ծառայություններ-Դասընթացներ</td>
        <td>Services - Courses</td>
    </tr>
</table>

<p><strong>Note:</strong> The system validates that the product category is active before processing the loan offer. If an inactive or invalid category ID is provided, the request will be rejected with error code 6005 (PRODUCT_IS_NOT_ACTIVE).</p>

<h3>2. Loan Approve</h3>
<div class="endpoint">
    <span class="method post">POST</span> <code>/loan-approve</code>
</div>
<p>Approves a loan offer and processes the loan.</p>

<h4>Request Parameters:</h4>
<table>
    <tr>
        <th>Parameter</th>
        <th>Type</th>
        <th>Required</th>
        <th>Description</th>
    </tr>
    <tr>
        <td>loanCode</td>
        <td>string</td>
        <td>Yes</td>
        <td>Loan code received from loan offer response</td>
    </tr>
    <tr>
        <td>contractNumber</td>
        <td>string</td>
        <td>Yes</td>
        <td>Contract number received from loan offer response</td>
    </tr>
    <tr>
        <td>suuid</td>
        <td>string</td>
        <td>Yes</td>
        <td>Unique system identifier received from loan offer response</td>
    </tr>
    <tr>
        <td>armenianFirstName</td>
        <td>string</td>
        <td>No</td>
        <td>Applicant's first name in Armenian</td>
    </tr>
    <tr>
        <td>armenianLastName</td>
        <td>string</td>
        <td>No</td>
        <td>Applicant's last name in Armenian</td>
    </tr>
    <tr>
        <td>armenianMiddleName</td>
        <td>string</td>
        <td>No</td>
        <td>Applicant's middle name in Armenian</td>
    </tr>
    <tr>
        <td>englishFirstName</td>
        <td>string</td>
        <td>No</td>
        <td>Applicant's first name in English</td>
    </tr>
    <tr>
        <td>englishLastName</td>
        <td>string</td>
        <td>No</td>
        <td>Applicant's last name in English</td>
    </tr>
    <tr>
        <td>englishMiddleName</td>
        <td>string</td>
        <td>No</td>
        <td>Applicant's middle name in English</td>
    </tr>
    <tr>
        <td>lenderAccessToken</td>
        <td>string</td>
        <td>Yes</td>
        <td>Access token for the lender system</td>
    </tr>
    <tr>
        <td>birthDate</td>
        <td>string</td>
        <td>No</td>
        <td>Applicant's birth date in DD/MM/YYYY format</td>
    </tr>
    <tr>
        <td>socialCardNumber</td>
        <td>string</td>
        <td>No</td>
        <td>Applicant's social card number</td>
    </tr>
    <tr>
        <td>mobilePhoneNumber</td>
        <td>string</td>
        <td>No</td>
        <td>Applicant's mobile phone number in international format</td>
    </tr>
    <tr>
        <td>additionalPhoneNumber</td>
        <td>string</td>
        <td>No</td>
        <td>Applicant's additional phone number</td>
    </tr>
    <tr>
        <td>emailAddress</td>
        <td>string</td>
        <td>Yes</td>
        <td>Applicant's email address</td>
    </tr>
    <tr>
        <td>currentSalary</td>
        <td>numeric</td>
        <td>No</td>
        <td>Applicant's current monthly salary</td>
    </tr>
    <tr>
        <td>loanAmount</td>
        <td>numeric</td>
        <td>Yes</td>
        <td>The approved loan amount in AMD</td>
    </tr>
    <tr>
        <td>loanDuration</td>
        <td>numeric</td>
        <td>Yes</td>
        <td>The approved loan duration in months</td>
    </tr>
    <tr>
        <td>interestAmount</td>
        <td>numeric</td>
        <td>No</td>
        <td>The annual interest rate percentage</td>
    </tr>
    <tr>
        <td>loanSchedule</td>
        <td>array</td>
        <td>Yes</td>
        <td>Loan repayment schedule with payment dates and amounts. Each item contains:
            <ul>
                <li><strong>payment_date</strong>: Date of payment in YYYY-MM-DD format</li>
                <li><strong>payment</strong>: Total payment amount for this period</li>
                <li><strong>base</strong>: Principal payment amount</li>
                <li><strong>interest</strong>: Interest payment amount</li>
                <li><strong>balance</strong>: Remaining loan balance after this payment</li>
            </ul>
        </td>
    </tr>
    <tr>
        <td>currentAddress</td>
        <td>object</td>
        <td>No</td>
        <td>Applicant's current residential address. Contains:
            <ul>
                <li><strong>address</strong>: Street address with building number</li>
                <li><strong>community</strong>: Community or city name</li>
                <li><strong>region</strong>: Region or province name</li>
            </ul>
        </td>
    </tr>
    <tr>
        <td>registrationAddress</td>
        <td>object</td>
        <td>No</td>
        <td>Applicant's official registration address. Contains:
            <ul>
                <li><strong>apartments</strong>: Apartment number (if applicable)</li>
                <li><strong>residence</strong>: Residence name</li>
                <li><strong>building</strong>: Building number</li>
                <li><strong>buildingType</strong>: Type of building</li>
                <li><strong>community</strong>: Community or city name</li>
                <li><strong>locationCode</strong>: Location code</li>
                <li><strong>region</strong>: Region code</li>
                <li><strong>street</strong>: Street name</li>
            </ul>
        </td>
    </tr>
    <tr>
        <td>documents</td>
        <td>array</td>
        <td>No</td>
        <td>Array of applicant's identification documents. Each document contains:
            <ul>
                <li><strong>documentType</strong>: Type of document (e.g., NON_BIOMETRIC_PASSPORT, ID_CARD)</li>
                <li><strong>documentNumber</strong>: Document number</li>
                <li><strong>documentDepartment</strong>: Issuing department code</li>
                <li><strong>countryCode</strong>: Country code</li>
                <li><strong>countryName</strong>: Country name</li>
                <li><strong>armenianFirstName</strong>: First name in Armenian</li>
                <li><strong>armenianLastName</strong>: Last name in Armenian</li>
                <li><strong>armenianMiddleName</strong>: Middle name in Armenian</li>
                <li><strong>englishFirstName</strong>: First name in English</li>
                <li><strong>englishLastName</strong>: Last name in English</li>
                <li><strong>englishMiddleName</strong>: Middle name in English</li>
                <li><strong>gender</strong>: Gender (M or F)</li>
                <li><strong>birthDate</strong>: Birth date in DD/MM/YYYY format</li>
                <li><strong>issuanceDate</strong>: Document issuance date in DD/MM/YYYY format</li>
                <li><strong>validityDate</strong>: Document expiry date in DD/MM/YYYY format</li>
            </ul>
        </td>
    </tr>
    <tr>
        <td>totalPledgePrice</td>
        <td>numeric</td>
        <td>No</td>
        <td>Total price of all pledged items</td>
    </tr>
    <tr>
        <td>pledge</td>
        <td>array</td>
        <td>No</td>
        <td>Array of pledged items. Each item contains:
            <ul>
                <li><strong>name</strong>: Name or description of the pledged item</li>
                <li><strong>price</strong>: Market price of the pledged item in AMD</li>
                <li><strong>ratio</strong>: Collateral value of the pledged item in AMD</li>
            </ul>
        </td>
    </tr>
</table>

<h4>Example Request:</h4>
<pre><code>{
  "loanCode": "000000000000L000",
  "contractNumber": "X00-000000",
  "suuid": "00000000-0000-0000-0000-000000000000",
  "armenianFirstName": "ԱՆՈՒՆ",
  "armenianLastName": "ԱԶԳԱՆՈՒՆ",
  "armenianMiddleName": "ՀԱՅՐԱՆՈՒՆԻ",
  "englishFirstName": "ANUN",
  "englishLastName": "AZGANUN",
  "englishMiddleName": "HAYRANUNI",
  "lenderAccessToken": "00000000000000000000000000000000",
  "birthDate": "01/01/1990",
  "socialCardNumber": "0000000000",
  "mobilePhoneNumber": "+37477000000",
  "additionalPhoneNumber": "00000000",
  "emailAddress": "<EMAIL>",
  "currentSalary": 150000,
  "loanAmount": 500000,
  "loanDuration": 36,
  "interestAmount": 13.0,
  "loanSchedule": [
    {
      "payment_date": "2023-12-01",
      "payment": 75000,
      "base": 50000,
      "interest": 25000,
      "balance": 1950000
    },
    {
      "payment_date": "2024-01-01",
      "payment": 75000,
      "base": 51000,
      "interest": 24000,
      "balance": 1899000
    },
    ...,
  ],
  "currentAddress": {
    "address": "Փողոց, 0",
    "community": "ՀԱՄԱՅՆՔ",
    "region": "ՄԱՐԶ"
  },
  "registrationAddress": {
    "apartments": null,
    "residence": "ԲՆԱԿԱՎԱՅՐ",
    "building": "0",
    "buildingType": "Տ",
    "community": "ՀԱՄԱՅՆՔ",
    "locationCode": "0000000",
    "region": "0000000",
    "street": "ՓՈՂՈՑ Փ."
  },
  "documents": [
    {
      "documentType": "NON_BIOMETRIC_PASSPORT",
      "documentNumber": "*********",
      "documentDepartment": "000",
      "countryCode": "000",
      "countryName": "ՀԱՅԱՍՏԱՆԻ ՀԱՆՐԱՊԵՏՈՒԹՅՈՒՆ",
      "armenianFirstName": "ԱՆՈՒՆ",
      "armenianLastName": "ԱԶԳԱՆՈՒՆ",
      "armenianMiddleName": "ՀԱՅՐԱՆՈՒՆԻ",
      "englishFirstName": "ANUN",
      "englishLastName": "AZGANUN",
      "englishMiddleName": "HAYRANUNI",
      "gender": "M",
      "birthDate": "01/01/1990",
      "issuanceDate": "01/01/2020",
      "validityDate": "01/01/2030"
    }
  ],
  "totalPledgePrice": null,
  "pledge": [
    {
      "name": "Ապրանք",
      "ratio": 2000000,
      "price": 2000000
    }
  ],
  "registeredAddress": {
    "apartments": null,
    "residence": "ԲՆԱԿԱՎԱՅՐ",
    "building": "0",
    "buildingType": "Տ",
    "community": "ՀԱՄԱՅՆՔ",
    "locationCode": "0000000",
    "region": "0000000",
    "street": "ՓՈՂՈՑ Փ."
  }
}</code></pre>

<h4>Response:</h4>
<pre><code>{
  "suuid": "00000000-0000-0000-0000-000000000000",
  "success": true
}</code></pre>

<h4>Response Parameters:</h4>
<table>
    <tr>
        <th>Parameter</th>
        <th>Type</th>
        <th>Description</th>
    </tr>
    <tr>
        <td>suuid</td>
        <td>string</td>
        <td>Unique system identifier of the loan offer</td>
    </tr>
    <tr>
        <td>success</td>
        <td>boolean</td>
        <td>Indicates whether the loan approval was successful</td>
    </tr>
</table>

<h3>3. Loan Offer Status</h3>
<div class="endpoint">
    <span class="method get">GET</span> <code>/status</code>
</div>
<p>Checks the status of a loan offer.</p>

<h4>Request Parameters:</h4>
<table>
    <tr>
        <th>Parameter</th>
        <th>Type</th>
        <th>Required</th>
        <th>Description</th>
    </tr>
    <tr>
        <td>identifier</td>
        <td>string</td>
        <td>Yes</td>
        <td>The SUUID of the loan offer</td>
    </tr>
</table>

<h4>Example Request:</h4>
<pre><code>GET /api/velox/status?identifier=00000000-0000-0000-0000-000000000000</code></pre>

<h4>Response:</h4>
<pre><code>{
  "suuid": "00000000-0000-0000-0000-000000000000",
  "contractNumber": "VLX-000000",
  "status": "APPROVED",
  "approvedAt": "2024-05-08T08:23:02Z"
}</code></pre>

<h4>Response Parameters:</h4>
<table>
    <tr>
        <th>Parameter</th>
        <th>Type</th>
        <th>Description</th>
    </tr>
    <tr>
        <td>suuid</td>
        <td>string</td>
        <td>Unique system identifier of the loan offer</td>
    </tr>
    <tr>
        <td>contractNumber</td>
        <td>string</td>
        <td>Contract number of the loan</td>
    </tr>
    <tr>
        <td>status</td>
        <td>string</td>
        <td>Current status of the loan offer</td>
    </tr>
    <tr>
        <td>approvedAt</td>
        <td>string</td>
        <td>Timestamp when the loan was approved (ISO 8601 format)</td>
    </tr>
</table>

<h4>Possible Status Values:</h4>
<ul>
    <li><code>PENDING</code> - Loan offer is awaiting approval</li>
    <li><code>APPROVED</code> - Loan has been approved</li>
    <li><code>DECLINED</code> - Loan offer has been declined</li>
    <li><code>NOT_FOUND</code> - Loan offer not found</li>
</ul>

<h2>Error Handling</h2>
<p>The API returns standard HTTP status codes along with specific error codes:</p>
<ul>
    <li><code>200 OK</code> - Request successful</li>
    <li><code>400 Bad Request</code> - Invalid request parameters</li>
    <li><code>404 Not Found</code> - Resource not found</li>
    <li><code>500 Internal Server Error</code> - Server error</li>
</ul>

<p>Error responses include a message describing the error and a specific error code:</p>
<pre><code>{
  "error": {
    "message": "Error message description",
    "status_code": 400,
    "code": "6001"
  }
}</code></pre>

<h2>Specific Error Codes</h2>
<table>
    <tr>
        <th>Error Code</th>
        <th>Error Type</th>
        <th>Description</th>
    </tr>
    <tr>
        <td>6000</td>
        <td>INTERNAL_ERROR</td>
        <td>Internal server error occurred during processing</td>
    </tr>
    <tr>
        <td>6001</td>
        <td>INVALID_CITIZEN_INFO</td>
        <td>Invalid citizen data provided (EKENG, NORK, or ACRA data)</td>
    </tr>
    <tr>
        <td>6002</td>
        <td>INVALID_LOAN_SCHEDULE</td>
        <td>Invalid loan schedule provided in the request</td>
    </tr>
    <tr>
        <td>6003</td>
        <td>DOWN</td>
        <td>Velox service is currently unavailable</td>
    </tr>
    <tr>
        <td>6004</td>
        <td>MERCHANT_BLACKLISTED</td>
        <td>The merchant is blacklisted and cannot process loans</td>
    </tr>
    <tr>
        <td>6005</td>
        <td>PRODUCT_IS_NOT_ACTIVE</td>
        <td>The requested product category is not active</td>
    </tr>
    <tr>
        <td>6006</td>
        <td>INTERNAL_RULE_VIOLATION</td>
        <td>The loan application violates internal business rules</td>
    </tr>
</table>

<h3>Error Handling Examples</h3>

<h4>Example: Invalid Citizen Information</h4>
<pre><code>{
  "error": {
    "message": "Invalid citizen information provided",
    "status_code": 400,
    "code": "6001"
  }
}</code></pre>

<h4>Example: Merchant Blacklisted</h4>
<pre><code>{
  "error": {
    "message": "This merchant is blacklisted",
    "status_code": 403,
    "code": "6004"
  }
}</code></pre>

<h4>Example: Service Down</h4>
<pre><code>{
  "error": {
    "message": "Velox service is currently unavailable",
    "status_code": 503,
    "code": "6003"
  }
}</code></pre>

<h4>Example: Internal Rule Violation</h4>
<pre><code>{
  "error": {
    "message": "The loan application violates internal business rules",
    "status_code": 403,
    "code": "6006"
  }
}</code></pre>

<h2>Conclusion</h2>
<p>This documentation provides a comprehensive guide to integrating with the Velox Loan Offer API. By following these guidelines, you can successfully implement loan application and approval processes in your systems.</p>

</body>
</html>