<?php

namespace App\Services;

use Log;

class DevOTCLService extends OTCLService
{
    protected function requestWalletInfo($token, $suuid)
    {
        Log::info('Requesting user data from Telcell with token', ['token' => $token]);

        $res = FakerService::getMockedTelcellPayload($token);

        Log::info('Requesting user data from Telcell', ['result' => $res]);

        return $res;
    }

    public function transfer($token, $amount, $agreement_id, $suuid)
    {
    }
}
