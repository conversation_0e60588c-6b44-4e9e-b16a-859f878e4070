<?php

namespace App\Factory;

use App\Services\CreditLine\SecurityServiceBNPL;
use App\Services\CreditLine\SecurityServicePL;
use App\Services\CreditLine\SecurityServiceOBL;
use App\Services\SecurityService;
use App\Services\SecurityUtilityService;

class SecurityServiceFactory
{
    public static function build($suuid)
    {
        if ($suuid) {
            $loan_type = SecurityUtilityService::resolveLoanType($suuid);

            if ($loan_type === constants('LOAN_TYPES.PL')) {
                return new SecurityServicePL($suuid);
            }
            if ($loan_type === constants('LOAN_TYPES.BNPL')) {
                return new SecurityServiceBNPL($suuid);
            }
            if ($loan_type === constants('LOAN_TYPES.OBL')) {
                return new SecurityServiceOBL($suuid);
            }
        }

        return new SecurityService($suuid);
    }
}
