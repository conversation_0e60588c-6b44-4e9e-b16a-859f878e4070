<?php

namespace App\Logging;

use App\Helpers\ArrayHelper;
use App\Helpers\Security;

class DebugProcessor implements \Monolog\Processor\ProcessorInterface
{
    private $request;

    public function __construct($request)
    {
        $this->request = $request;
    }

    public function __invoke(array $record)
    {
        // Append suuid to each log
        if ($this->request) {
            $suuid = Security::getSuuid();
            $record['extra']['suuid'] = $suuid;
            $record['extra']['ip'] = $this->request->ip();
        }

        $key = array_key_first($record['context']);

        // Trim off base64 encoded photos from log
        if (in_array($key, ['citizen_context', 'seller_info', 'spouse_info', 'seller'], true)) {
            $copy = ArrayHelper::array_copy($record['context'][$key]);
            unset($copy['passport_data']->Photo);
            unset($copy['photo']);

            if (isset($copy['spouse_info'])) {
                unset($copy['spouse_info']['passport_data']->Photo);
                unset($copy['spouse_info']['photo']);
            }

            if (isset($copy['seller_info'])) {
                unset($copy['seller_info']['passport_data']->Photo);
                unset($copy['seller_info']['photo']);

                if (isset($copy['seller_info']['spouse_info'])) {
                    unset($copy['seller_info']['spouse_info']['passport_data']->Photo);
                    unset($copy['seller_info']['spouse_info']['photo']);
                }
            }

            $record['context'][$key] = $copy;
        }

        // Trim off base64 encoded photos from passport_data
        if (isset($record['context']['passport_data'])) {
            $copy = ArrayHelper::array_copy($record['context']['passport_data']);
            unset($copy['Photo']);

            $record['context']['passport_data'] = $copy;
        }

        return $record;
    }
}
