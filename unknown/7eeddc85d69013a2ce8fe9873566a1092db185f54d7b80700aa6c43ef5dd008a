<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPrimaryIdUsersPermissionsRolesTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_has_permissions', function (Blueprint $table) {
            $table->dropPrimary('user_has_permissions_pkey');
        });

        Schema::table('user_has_permissions', function (Blueprint $table) {
            $table->increments('id');
            $table->unique(['permission_id', 'user_id', 'model_type']);
        });

        Schema::table('user_has_roles', function (Blueprint $table) {
            $table->dropPrimary('user_has_roles_pkey');
        });

        Schema::table('user_has_roles', function (Blueprint $table) {
            $table->increments('id');
            $table->unique(['role_id', 'user_id', 'model_type']);
        });

        Schema::table('role_has_permissions', function (Blueprint $table) {
            $table->dropPrimary('role_has_permissions_pkey');
        });

        Schema::table('role_has_permissions', function (Blueprint $table) {
            $table->increments('id');
            $table->unique(['permission_id', 'role_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_has_permissions', function (Blueprint $table) {
            $table->dropUnique('user_has_permissions_permission_id_user_id_model_type_unique');
            $table->dropColumn('id');
        });

        Schema::table('user_has_permissions', function (Blueprint $table) {
            $table->primary(['permission_id', 'user_id', 'model_type']);
        });

        Schema::table('user_has_roles', function (Blueprint $table) {
            $table->dropUnique('user_has_roles_role_id_user_id_model_type_unique');
            $table->dropColumn('id');
        });

        Schema::table('user_has_roles', function (Blueprint $table) {
            $table->primary(['role_id', 'user_id', 'model_type']);
        });

        Schema::table('role_has_permissions', function (Blueprint $table) {
            $table->dropUnique('role_has_permissions_permission_id_role_id_unique');
            $table->dropColumn('id');
        });

        Schema::table('role_has_permissions', function (Blueprint $table) {
            $table->primary(['permission_id', 'role_id']);
        });
    }
}
