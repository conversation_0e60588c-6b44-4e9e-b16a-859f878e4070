<?php

namespace App\Dtos\InternalClient;

use App\Models\Loan;
use Illuminate\Contracts\Support\Arrayable;

class ClientDataDto implements Arrayable
{
    /**
     * @var string
     */
    private $ssn;
    /**
     * @var int|null
     */
    private $loan_type_id;

    /**
     * @var HashableDataDto|null
     */
    private $hashable_data_dto;

    /**
     * @var array|null
     */
    private $citizen;

    /**
     * @var string|null
     */
    private $outer_id;
    /**
     * @var Loan|null
     */
    private $loan;

    public function __construct($ssn, $loan_type_id, $hashable_data_dto, $citizen, $outer_id, $loan)
    {
        $this->setValue('ssn', $ssn);
        $this->setValue('loan_type_id', $loan_type_id);
        $this->setValue('hashable_data_dto', $hashable_data_dto);
        $this->setValue('citizen', $citizen);
        $this->setValue('outer_id', $outer_id);
        $this->setValue('loan', $loan);
    }

    public function getValue($name)
    {
        return $this->{$name};
    }

    /**
     * @param $value mixed
     */
    public function setValue(string $field, $value): void
    {
        $this->{$field} = $value;
    }

    public function toArray(): array
    {
        return [
            'ssn' => $this->ssn,
            'loan_type_id' => $this->loan_type_id,
            'hashable_data_dto' => $this->hashable_data_dto,
            'citizen' => $this->citizen,
            'outer_id' => $this->outer_id,
            'loan' => $this->loan,
        ];
    }
}
