<?php

namespace App\Http\Middleware;

use Closure;

class OEPLDown
{
    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $settings_service = resolve('App\Interfaces\ISettingsService');

        if ($settings_service->isLoanTypeDisabled((constants('LOAN_TYPES.OEPL')))) {
            return redirect(route('oepl_down'));
        }

        return $next($request);
    }
}
