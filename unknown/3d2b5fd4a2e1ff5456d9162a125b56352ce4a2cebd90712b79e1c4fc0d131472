<?php

namespace Tests\Unit\OCL;

use Tests\Unit\RuleEngineExecutorTest;

class RuleEngineBaseRulesOCLTest extends RuleEngineExecutorTest
{
    protected $loan_type = 'OCL';

    public function dataProvider(): array
    {
        return [
            [[
                'rule' => 'osmAmount',
                'context' => [
                    'input' => [
                        'osmAmount' => 1,
                    ],
                ],
                'output' => [
                    'osmAmount' => [
                        'amount' => 1,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => 1,
                    ],
                ],
            ]],
            [[
                'rule' => 'dstiAmount',
                'context' => [
                    'input' => [
                        'dstiAmount' => 1,
                    ],
                ],
                'output' => [
                    'dstiAmount' => [
                        'amount' => 1,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => 1,
                    ],
                ],
            ]],
            [[
                'rule' => 'isDead',
                'context' => [
                    'input' => [
                        'isDead' => true,
                    ],
                ],
                'output' => [
                    'isDead' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => 1,
                    ],
                ],
            ]],
            [[
                'rule' => 'age',
                'context' => [
                    'input' => [
                        'age' => 20,
                        'salary' => 150000,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => 1,
                    ],
                ],
            ]],
            [[
                'rule' => 'age',
                'context' => [
                    'input' => [
                        'age' => 66,
                        'salary' => 150000,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => 1,
                    ],
                ],
            ]],
            [[
                'rule' => 'citizenship',
                'context' => [
                    'input' => [
                        'citizenship' => 'not ARM',
                    ],
                ],
                'output' => [
                    'citizenship' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => 1,
                    ],
                ],
            ]],
            [[
                'rule' => 'hasAddress',
                'context' => [
                    'input' => [
                        'hasAddress' => false,
                    ],
                ],
                'output' => [
                    'hasAddress' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => 1,
                    ],
                ],
            ]],
            [[
                'rule' => 'hasLongHistory',
                'context' => [
                    'input' => [
                        'hasLongHistory' => false,
                    ],
                ],
                'output' => [
                    'hasLongHistory' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => 1,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_missing_0_501_age_21_65_oldOclCustomer',
                'context' => [
                    'input' => [
                        'fico' => 400,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_missing_0_501_age_21_65_oldOclCustomer',
                'context' => [
                    'input' => [
                        'fico_missing' => true,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '0',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_greater_or_less_0_0.4_newCustomer',
                'context' => [
                    'input' => [
                        'drScore' => 0.41,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '0',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'loanClass',
                'context' => [
                    'input' => [
                        'loanClass' => 'not Ստանդարտ',
                    ],
                ],
                'output' => [
                    'loanClass' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => 1,
                    ],
                ],
            ]],
            [[
                'rule' => 'hasOverdueLoans',
                'context' => [
                    'input' => [
                        'hasOverdueLoans' => true,
                    ],
                ],
                'output' => [
                    'hasOverdueLoans' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => 1,
                    ],
                ],
            ]],
            [[
                'rule' => 'acraActiveLoans',
                'context' => [
                    'input' => [
                        'isNewOclCustomer' => true,
                        'acraActiveLoans' => 1000,
                    ],
                ],
                'output' => [
                    'acraActiveLoans' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => 1,
                    ],
                ],
            ]],
            [[
                'rule' => 'isValidLoanClass12',
                'context' => [
                    'input' => [
                        'salary' => 0,
                        'isValidLoanClass12' => false,
                    ],
                ],
                'output' => [
                    'isValidLoanClass12' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => 1,
                    ],
                ],
            ]],
            [[
                'rule' => 'acraStatus',
                'context' => [
                    'input' => [
                        'salary' => 0,
                        'acraStatus' => 2,
                    ],
                ],
                'output' => [
                    'acraStatus' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => 1,
                    ],
                ],
            ]],
            [[
                'rule' => 'hasCredit',
                'context' => [
                    'input' => [
                        'hasCredit' => true,
                    ],
                ],
                'output' => [
                    'hasCredit' => [
                        'amount' => '0',
                        'duration' => 0,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'label',
                        'id' => 1,
                    ],
                ],
            ]],
        ];
    }
}
