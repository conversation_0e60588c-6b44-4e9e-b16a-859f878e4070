<?php

namespace App\Services;

use App\Abstracts\BaseDocumentService;
use App\Models\LoanDocument;
use Exception;
use File;
use function Functional\pluck;
use Illuminate\Support\Facades\Log;
use ZipArchive;

class LoanDocumentServiceVLX extends BaseDocumentService
{
    const ZIP_EXT = '.zip';
    const ATTACHMENT = 'ATTACHMENT';

    protected $tmp_file_path;

    public function storeVeloxDocuments($loan, $documents)
    {
        $this->removeDocuments($loan);

        $citizen = $loan->citizen;

        $directory = $this->prepareDirectory();

        // We are getting ATTACHMENT documents as ZIP, that's why we must use separately logic
        if (array_key_exists(self::ATTACHMENT, $documents)) {
            $type = self::ATTACHMENT;

            $this->prepareTmpDirectory();

            $this->storeAttachmentDocuments($documents, $citizen, $loan, $directory, $type);

            $this->cleanupTmpDirectory();

            unset($documents[$type]);
        }

        foreach ($documents as $type => $document) {
            $path = $this->getDocumentPath($citizen->first_name, $citizen->last_name, $this->getDocumentNameByType($type), $directory);

            $this->persistDocument($loan, $path, $this->getDocumentNameByType($type), $document, true);
        }
    }

    private function removeDocuments($loan)
    {
        $document_names = $this->getDocumentsForRemove();

        $loan->documents()
            ->whereIn('document_type', $document_names)
            ->delete();
    }

    public function getDocumentsForRemove($obfuscated = true, $loan = null)
    {
        return pluck(array_values($this->getDocumentTypes()), 'name');
    }

    public function getDocumentTypes()
    {
        return [
            'COMPLETE_SHEET' => LoanDocument::VELOX_COMPLETE_SHEET,
            'ATTACHMENT' => LoanDocument::VELOX_ATTACHMENT,
        ];
    }

    public function getDocumentNameByType($type)
    {
        return $this->getDocumentTypes()[$type]['name'];
    }

    protected function extractZip($document, $type)
    {
        try {
            Log::info('Start extracting Zip', ['type' => $type]);

            $tmp_zip_file = $this->prepareTmpZipFile($document);

            $zip = new ZipArchive();
            if ($zip->open($tmp_zip_file) !== true) {
                Log::error('Could not open ZIP file.', ['type' => $type]);
            }

            $zip->extractTo($this->tmp_file_path);
            $zip->close();

            Log::info('Extract Zip success', ['type' => $type]);

            return ['extracted_files' => glob($this->tmp_file_path.'/*')];
        } catch (Exception $e) {
            Log::critical('Extract Zip, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
        }
    }

    protected function storeAttachmentDocuments($files, $citizen, $loan, $directory, $type)
    {
        $suuid = $citizen->loan->loan_security->suuid ?? null;
        Log::info('Store Attachment Documents', ['suuid' => $suuid]);

        ['extracted_files' => $extracted_files] = $this->extractZip($files[$type], $type);

        foreach ($extracted_files as $file) {
            $path = $this->getDocumentPath($citizen->first_name, $citizen->last_name, $this->getDocumentNameByType($type), $directory);

            $file_content = file_get_contents($file);

            $this->persistDocument($loan, $path, $this->getDocumentNameByType($type), $file_content, true);
        }
    }

    protected function cleanupTmpDirectory()
    {
        File::deleteDirectory($this->tmp_file_path);

        unlink($this->tmp_file_path.self::ZIP_EXT);
    }

    // This method creates /tmp folder
    protected function prepareTmpDirectory()
    {
        $tmp_dir_path = public_path(sys_get_temp_dir());

        if (!File::exists($tmp_dir_path)) {
            File::makeDirectory($tmp_dir_path);
        }
    }

    // This method creates temporary zip file into /tmp folder
    protected function prepareTmpZipFile($document): string
    {
        $this->tmp_file_path = public_path(tempnam(sys_get_temp_dir(), str_random()));

        file_put_contents($this->tmp_file_path.self::ZIP_EXT, $document);

        return $this->tmp_file_path.self::ZIP_EXT;
    }
}
