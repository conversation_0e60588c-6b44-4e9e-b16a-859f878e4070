<?php

namespace App\Api\V1\Requests;

use Dingo\Api\Http\FormRequest;

class OaslApproveRequest extends FormRequest
{
    public function rules()
    {
        $rules = [
            'amount' => 'required|numeric',
            'months' => 'required|numeric',
            'solar_panel_type_id' => 'required|numeric',
            'region_id' => 'required|numeric|exists:regions,id',
            'village_id' => 'required|numeric|exists:villages,id',
            'address' => 'required|string',
        ];

        return $rules;
    }

    public function authorize()
    {
        return true;
    }
}
