<!DOCTYPE html>
<html>
<head>
    <title>Global Credit</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <style>
        @font-face {
            font-family: GHEAGrapalat;
            src: url({{ storage_path('fonts/GHEAGrapalatReg.ttf') }});
        }
        @font-face {
            font-family: GHEAGrapalat;
            font-weight: bold;
            src: url({{ storage_path('fonts/GHEAGrapalatBld.ttf') }});
        }
        @font-face {
            font-family: GHEAGrapalat;
            font-style: italic;
            src: url({{ storage_path('fonts/GHEAGrapalatRit.ttf') }});
        }
        @font-face {
            font-family: GHEAGrapalat;
            font-weight: bold;
            font-style: italic;
            src: url({{ storage_path('fonts/GHEAGrapalatBlit.ttf') }});
        }
        body {
            font-family: GHEAGrapalat;
            font-size: 13px;
            line-height: 12px;
        }
        .header-image {
            width: 2.5cm;
            margin: 0 auto;
            text-align: center;
            display: inline-block;
        }
        .logo {
            height: 2.5cm;
        }
        .header {
            height: 2.5cm;
            text-align: center;
            margin-bottom: 1cm;
            width: 100%;
        }
        .header-text {
            text-align: center;
        }
        .bold-header {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 0.5cm;
        }
        .content {
            width: 90%;
            text-align: center;
            margin: 0 auto;
        }
        .table-title {
            text-align: left;
            margin-bottom: 0.2cm;
        }
        table, td {
            border: 1px solid black;
        }
        td {
            padding: 0 10px;
        }
        table {
            width: 100%;
            margin: 0;
            border-collapse: collapse;
        }
        .name {
            width: 53%;
            font-weight: bold;
        }
        .value {
            width: 47%;
        }
        .executive-director {
            margin-top: 1cm;
        }
        .executive-director-left {
            width: 48%;
            float: left;
        }
        .executive-director-right {
            width: 48%;
            float: right;
        }
        .footer {
            position: absolute;
            bottom: 1.5cm;
        }
        .barcode {
            position: absolute;
            margin-left: 3.6cm;
            bottom: 0;
        }
    </style>
</head>
<body>
<div class="content">
    <div class="header-text">
        <div class="bold-header">ՀԱՎԵԼՎԱԾ</div>
    </div>
    <div class="italic-text table-title">Վարկային պարտավորությունների կատարման/մարման ժամանակացույց</div>
    <table>
        <tr>
            <th>N</th>
            <th class="bold-text">Մարման ամսաթիվ</th>
            <th class="bold-text">Մարման ենթակա մայր գումար<br />(ՀՀ դրամ)</th>
            <th class="bold-text">Մարման ենթակա տոկոսագումար<br />(ՀՀ դրամ)</th>
            <th class="bold-text">Մարման ենթակա սպասարկման վճար<br />(ՀՀ դրամ)</th>
            <th class="bold-text">Մարման ենթակա ընդհանուր գումար<br />(ՀՀ դրամ)</th>
            <th class="bold-text">Մայր գումարի մնացորդ<br />(ՀՀ դրամ)</th>
        </tr>
        @foreach($loan_schedule as $key => $schedule)
            <tr>
                <td>{{$key + 1}}</td>
                <td class="right-align">{{$schedule->date->format('d/m/Y')}}</td>
                <td class="right-align">@number_to_money($schedule->base)</td>
                <td class="right-align">@number_to_money($schedule->service_fee_interest)</td>
                <td class="right-align">@number_to_money($schedule->service_fee_plain)</td>
                <td class="right-align">@number_to_money($schedule->payment)</td>
                <td class="right-align">@number_to_money($schedule->balance)</td>
            </tr>
        @endforeach
        <tr>
            <td></td>
            <td class="bold-text right-align">ԸՆԴԱՄԵՆԸ</td>
            <td class="bold-text right-align">@number_to_money($amount)</td>
            <td class="bold-text right-align">@number_to_money($service_fee_interest_sum)</td>
            <td class="bold-text right-align">@number_to_money($service_fee_plain_sum)</td>
            <td class="bold-text right-align">@number_to_money($amount + $service_fee_sum)</td>
            <td></td>
        </tr>
    </table>
</div>
</body>
</html>