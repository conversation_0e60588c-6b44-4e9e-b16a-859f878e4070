import React, { Component } from 'react';
import { withNamespaces } from 'react-i18next';
import { Accordion, Transition } from 'semantic-ui-react';

import styles from '../index.module.scss';

import { ReactComponent as Arrow } from '../../../svgs/arrow.svg';

class Questions extends Component {
  state = { activeIndex: null };

  handleClick = (e, titleProps) => {
    const { index } = titleProps;
    const { activeIndex } = this.state;
    const newIndex = activeIndex === index ? -1 : index;

    this.setState({ activeIndex: newIndex });
  };

  render() {
    const { t } = this.props;
    const { activeIndex } = this.state;

    const questions = [
      {
        title: t('document_number.question_1'),
        content: t('document_number.answer_1'),
      },
      {
        title: t('document_number.question_2'),
        content: t('document_number.answer_2'),
      },
      {
        title: t('document_number.question_3'),
        content: t('document_number.answer_3'),
      },
      {
        title: t('document_number.question_4'),
        content: t('document_number.answer_4'),
      },
      {
        title: t('document_number.question_5'),
        content: t('document_number.answer_5'),
      },
      {
        title: t('document_number.question_6'),
        content: t('document_number.answer_6'),
      },
    ];

    return (
      <div className={styles.questions}>
        <div className={styles.info_title}>
          {t('document_number.frequently_questions')}
        </div>

        <Accordion className={styles.questions_body}>
          {questions.map((item, index) => {
            return (
              <div className={styles.question} key={index}>
                <Accordion.Title
                  className={styles.question_title}
                  active={activeIndex === index}
                  index={index}
                  onClick={this.handleClick}
                >
                  <div className={styles.question_title_text}>{item.title}</div>
                  <Arrow
                    className={styles.arrow}
                    style={
                      this.state.activeIndex === index
                        ? { transform: 'rotate(90deg' }
                        : null
                    }
                  />
                </Accordion.Title>
                <Transition
                  visible={activeIndex === index}
                  animation="fade down"
                  duration={300}
                >
                  <Accordion.Content active={activeIndex === index}>
                    <div className={styles.question_answer}>{item.content}</div>
                  </Accordion.Content>
                </Transition>
              </div>
            );
          })}
        </Accordion>
      </div>
    );
  }
}

export default withNamespaces('translations')(Questions);
