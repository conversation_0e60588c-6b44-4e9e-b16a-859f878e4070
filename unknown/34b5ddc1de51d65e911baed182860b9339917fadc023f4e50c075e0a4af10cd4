<?php

namespace App\Factory;

use App\Services\SecurityUtilityService;

use App\Services\LoanServiceOCL;
use App\Services\LoanServiceOVL;
use App\Services\LoanServiceOIQL;
use App\Services\LoanServiceOASL;
use App\Services\LoanServiceOWL;
use App\Services\LoanServiceREML;
use App\Services\LoanServiceVLX;
use App\Services\LoanServiceOIWL;
use App\Services\LoanServiceOBL;
use App\Services\LoanServiceCommon;

class LoanServiceFactory
{
    public static function build($loan_type_id)
    {
        if ($loan_type_id == constants('LOAN_TYPES.OCL')) {
            return new LoanServiceOCL();
        } elseif ($loan_type_id == constants('LOAN_TYPES.OVL')) {
            return new LoanServiceOVL();
        } elseif ($loan_type_id == constants('LOAN_TYPES.OIQL')) {
            return new LoanServiceOIQL();
        } elseif ($loan_type_id == constants('LOAN_TYPES.OASL')) {
            return new LoanServiceOASL();
        } elseif (SecurityUtilityService::isOwlLoan($loan_type_id)) {
            return new LoanServiceOWL($loan_type_id);
        } elseif (SecurityUtilityService::isREML($loan_type_id)) {
            return new LoanServiceREML();
        } elseif ($loan_type_id == constants('LOAN_TYPES.VLX')) {
            return new LoanServiceVLX();
        } elseif ($loan_type_id == constants('LOAN_TYPES.OIWL')) {
            return new LoanServiceOIWL();
        } elseif ($loan_type_id == constants('LOAN_TYPES.OBL')) {
            return new LoanServiceOBL();
        } else {
            return new LoanServiceCommon();
        }
    }
}
